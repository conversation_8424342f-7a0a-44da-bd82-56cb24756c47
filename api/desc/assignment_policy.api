syntax = "v1"

info (
	title:   "分配策略管理API"
	desc:    "空间分配策略的CRUD操作"
	author: "zhangyupeng"
    email: "<EMAIL>"
    version: "v1"
)

import "base.api"

// 通知目标
type NotificationTarget {
	TeamIds             []int64                    `json:"teamIds"`
	PersonIds           []int64                    `json:"personIds"`
	ScheduleToRoleIds   map[string][]int64         `json:"scheduleToRoleIds"`
	By                  NotificationBy             `json:"by"`
	Webhooks            []Webhook                  `json:"webhooks"`
}

// 通知方式
type NotificationBy {
	FollowPreference bool     `json:"followPreference"`
	Critical         []string `json:"critical"`
	Warning          []string `json:"warning"`
	Info             []string `json:"info"`
}

// Webhook配置
type Webhook {
	Type     string            `json:"type"`
	Settings map[string]string `json:"settings"`
}

// 通知层级
type NotificationLayer {
	MaxTimes       int32              `json:"maxTimes"`
	NotifyStep     int32              `json:"notifyStep"`
	EscalateWindow int32              `json:"escalateWindow"`
	ForceEscalate  bool               `json:"forceEscalate"`
	Target         NotificationTarget `json:"target"`
}

// 时间过滤器
type TimeFilter {
	Start  string  `json:"start"`
	End    string  `json:"end"`
	Repeat []int32 `json:"repeat"`
	CalId  string  `json:"calId"`
	IsOff  bool    `json:"isOff"`
}

// 过滤器条件
type FilterCondition {
	Key  string   `json:"key"`
	Oper string   `json:"oper"`
	Vals []string `json:"vals"`
}

// The response data of Assignment Policy information | 分配策略信息
type AssignmentPolicyInfo {
	BaseUUIDInfo

	// Space ID | 空间ID
	SpaceId *int64 `json:"spaceId,optional"`

	// Rule ID | 规则ID
	RuleId *string `json:"ruleId,optional"`

	// Rule Name | 规则名称
	RuleName *string `json:"ruleName,optional" validate:"omitempty,max=100"`

	// Template ID | 模板ID
	TemplateId *string `json:"templateId,optional"`

	// Description | 描述
	Description *string `json:"description,optional"`

	// Status | 状态 (enabled/disabled)
	Status *string `json:"status,optional" validate:"omitempty,oneof=enabled disabled"`

	// Priority | 优先级
	Priority *int32 `json:"priority,optional"`

	// Updated By | 更新者ID
	UpdatedBy *int64 `json:"updatedBy,optional"`

	// Notification Layers | 分层通知
	Layers *[]NotificationLayer `json:"layers,optional"`

	// Aggregation Window | 聚合窗口(秒)
	AggrWindow *int32 `json:"aggrWindow,optional"`

	// Time Filters | 时间过滤器
	TimeFilters *[]TimeFilter `json:"timeFilters,optional"`

	// Filter Groups | 过滤器组 - 二维数组结构
	Filters *[][]FilterCondition `json:"filters,optional"`
}

// 创建分配策略请求
type CreateAssignmentPolicyReq {
	SpaceId      int64                  `json:"spaceId" validate:"required"`
	RuleName     string                 `json:"ruleName" validate:"required"`
	TemplateId   string                 `json:"templateId"`
	Description  string                 `json:"description"`
	Status       string                 `json:"status" validate:"required,oneof=enabled disabled"`
	Priority     int32                  `json:"priority"`
	Layers       []NotificationLayer    `json:"layers"`
	AggrWindow   int32                  `json:"aggrWindow"`
	TimeFilters  []TimeFilter           `json:"timeFilters"`
	Filters      [][]FilterCondition    `json:"filters"`
	UpdatedBy    int64                  `json:"updatedBy"`
}

// 更新分配策略请求
type UpdateAssignmentPolicyReq {
	RuleId       string                 `json:"ruleId" validate:"required"`
	SpaceId      int64                  `json:"spaceId"`
	RuleName     string                 `json:"ruleName"`
	TemplateId   string                 `json:"templateId"`
	Description  string                 `json:"description"`
	Status       string                 `json:"status" validate:"oneof=enabled disabled"`
	Priority     int32                  `json:"priority"`
	Layers       []NotificationLayer    `json:"layers"`
	AggrWindow   int32                  `json:"aggrWindow"`
	TimeFilters  []TimeFilter           `json:"timeFilters"`
	Filters      [][]FilterCondition    `json:"filters"`
	UpdatedBy    int64                  `json:"updatedBy"`
}

// Get Assignment Policy list request params | 分配策略列表请求参数
type AssignmentPolicyListReq {
	PageInfo

	// Space ID | 空间ID
	SpaceId *int64 `form:"spaceId,optional"`

	// Status | 状态
	Status *string `form:"status,optional"`
}

// The response data of assignment policy list | 分配策略列表数据
type AssignmentPolicyListResp {
	BaseDataInfo

	// Assignment policy list data | 分配策略列表数据
	Data AssignmentPolicyListInfo `json:"data"`
}

// Assignment policy list data | 分配策略列表数据
type AssignmentPolicyListInfo {
	BaseListInfo

	// The Assignment policy list data | 分配策略列表数据
	Data []AssignmentPolicyInfo `json:"data"`
}

// Assignment policy information response | 分配策略信息返回体
type AssignmentPolicyInfoResp {
	BaseDataInfo

	// Assignment policy information | 分配策略数据
	Data AssignmentPolicyInfo `json:"data"`
}

// 删除分配策略请求
type DeleteAssignmentPolicyReq {
	RuleIds []string `json:"ruleIds" validate:"required,min=1"`
}

@server (
	group:      assignment_policy
	prefix:     /api/v1/assignment-policies
	// middleware: Authority
)
service Oncall {
	@doc "创建分配策略"
	@handler createAssignmentPolicy
	post / (CreateAssignmentPolicyReq) returns (BaseMsgResp)

	@doc "获取分配策略列表"
	@handler getAssignmentPolicyList
	post /list (AssignmentPolicyListReq) returns (AssignmentPolicyListResp)

	@doc "获取分配策略详情"
	@handler getAssignmentPolicyById
	get /:ruleId (RuleIDPathReq) returns (AssignmentPolicyInfoResp)

	@doc "更新分配策略"
	@handler updateAssignmentPolicy
	put /:ruleId (UpdateAssignmentPolicyReq) returns (BaseMsgResp)

	@doc "删除分配策略"
	@handler deleteAssignmentPolicy
	delete / (DeleteAssignmentPolicyReq) returns (BaseMsgResp)
}
