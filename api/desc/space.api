syntax = "v1"

info (
	title:   "空间管理API"
	desc:    "工作空间的CRUD操作"
	author: "zhangyupeng"
    email: "<EMAIL>"
    version: "v1"
)

import "base.api"

// The response data of Space information | 空间信息
type SpaceInfo {
	BaseIDInfo

	// Translated Name | 展示名称
	Trans *string `json:"trans,optional"`

	// Space Name | 空间名称
	Name *string `json:"name,optional" validate:"omitempty,max=50"`

	// Team ID | 团队ID
	TeamId *int64 `json:"teamId,optional"`

	// Team Name | 团队名称
	TeamName *string `json:"teamName,optional"`

	// Description | 描述
	Description *string `json:"description,optional"`

	// Visibility | 可见性 (private/public)
	Visibility *string `json:"visibility,optional" validate:"omitempty,oneof=private public"`

	// Issue Statistics | 问题统计
	IssueStats *IssueStats `json:"issueStats,optional"`

	// Enabled Status | 启用状态
	Enabled *bool `json:"enabled,optional"`

	// Status Changed Time | 状态变更时间
	StatusChangedAt *string `json:"statusChangedAt,optional"`
}

// 问题统计
type IssueStats {
	Processing int32 `json:"processing"` // 处理中
	Pending    int32 `json:"pending"`    // 待处理
	Completed  int32 `json:"completed"`  // 已处理
}

// 创建空间请求
type CreateSpaceReq {
	Name        string `json:"name" validate:"required"`
	TeamId      int64  `json:"teamId"`
	TeamName    string `json:"teamName"`
	Description string `json:"description"`
	Visibility  string `json:"visibility" validate:"required,oneof=private public"`
	Enabled     bool   `json:"enabled"`
}

// 更新空间请求
type UpdateSpaceReq {
	ID          string `json:"id" validate:"required"`
	Name        string `json:"name"`
	TeamId      int64  `json:"teamId"`
	TeamName    string `json:"teamName"`
	Description string `json:"description"`
	Visibility  string `json:"visibility" validate:"oneof=private public"`
	Enabled     bool   `json:"enabled"`
}

// Get Space list request params | 空间列表请求参数
type SpaceListReq {
	PageInfo

	// Name | 空间名称
	Name *string `form:"name,optional"`
}

// The response data of space list | 空间列表数据
type SpaceListResp {
	BaseDataInfo

	// Space list data | 空间列表数据
	Data SpaceListInfo `json:"data"`
}

// Space list data | 空间列表数据
type SpaceListInfo {
	BaseListInfo

	// The Space list data | 空间列表数据
	Data []SpaceInfo `json:"data"`
}

// Space information response | 空间信息返回体
type SpaceInfoResp {
	BaseDataInfo

	// Space information | 空间数据
	Data SpaceInfo `json:"data"`
}

// 删除空间请求
type DeleteSpaceReq {
	IDs []uint64 `json:"ids" validate:"required,min=1"`
}

@server (
	group:      space
	prefix:     /api/v1/spaces
	// middleware: Authority
)
service Oncall {
	@doc "创建空间"
	@handler createSpace
	post / (CreateSpaceReq) returns (BaseMsgResp)

	@doc "获取空间列表"
	@handler getSpaceList
	post /list (SpaceListReq) returns (SpaceListResp)

	@doc "获取空间详情"
	@handler getSpaceById
	get /:id returns (SpaceInfoResp)

	@doc "更新空间"
	@handler updateSpace
	put /:id (UpdateSpaceReq) returns (BaseMsgResp)

	@doc "删除空间"
	@handler deleteSpace
	delete / (DeleteSpaceReq) returns (BaseMsgResp)
}
