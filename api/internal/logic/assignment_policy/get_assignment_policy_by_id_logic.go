package assignment_policy

import (
	"context"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAssignmentPolicyByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取分配策略详情
func NewGetAssignmentPolicyByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAssignmentPolicyByIdLogic {
	return &GetAssignmentPolicyByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAssignmentPolicyByIdLogic) GetAssignmentPolicyById(req *types.RuleIDPathReq) (resp *types.AssignmentPolicyInfoResp, err error) {
	// 规则ID (从路径参数获取)
	ruleId := req.RuleId

	// 参数验证
	if ruleId == "" {
		return nil, fmt.Errorf("分配策略规则ID不能为空")
	}

	// 调用 RPC 服务获取分配策略详情
	rpcResp, err := l.svcCtx.OncallRpc.GetAssignmentPolicyById(l.ctx, &oncall.UUIDReq{
		Id: ruleId,
	})

	if err != nil {
		l.Errorf("调用RPC获取分配策略详情失败: %v", err)
		return nil, err
	}

	// 转换通知层级
	var layers []types.NotificationLayer
	for _, layer := range rpcResp.Layers {
		// 转换通知目标
		target := types.NotificationTarget{
			TeamIds:   layer.Target.TeamIds,
			PersonIds: layer.Target.PersonIds,
		}

		// 转换通知方式
		if layer.Target.By != nil {
			target.By = types.NotificationBy{
				FollowPreference: layer.Target.By.FollowPreference,
				Critical:         layer.Target.By.Critical,
				Warning:          layer.Target.By.Warning,
				Info:             layer.Target.By.Info,
			}
		}

		// 转换 Webhooks
		var webhooks []types.Webhook
		for _, webhook := range layer.Target.Webhooks {
			webhooks = append(webhooks, types.Webhook{
				Type:     webhook.Type,
				Settings: webhook.Settings,
			})
		}
		target.Webhooks = webhooks

		layers = append(layers, types.NotificationLayer{
			MaxTimes:       layer.MaxTimes,
			NotifyStep:     layer.NotifyStep,
			EscalateWindow: layer.EscalateWindow,
			ForceEscalate:  layer.ForceEscalate,
			Target:         target,
		})
	}

	// 转换时间过滤器
	var timeFilters []types.TimeFilter
	for _, filter := range rpcResp.TimeFilters {
		timeFilters = append(timeFilters, types.TimeFilter{
			Start:  filter.Start,
			End:    filter.End,
			Repeat: filter.Repeat,
			CalId:  filter.CalId,
			IsOff:  filter.IsOff,
		})
	}

	// 转换过滤器组 - 二维数组结构
	var filters [][]types.FilterCondition
	for _, group := range rpcResp.Filters {
		var conditions []types.FilterCondition
		for _, cond := range group.Conditions {
			conditions = append(conditions, types.FilterCondition{
				Key:  cond.Key,
				Oper: cond.Oper,
				Vals: cond.Vals,
			})
		}
		filters = append(filters, conditions)
	}

	// 转换字段为指针类型
	id := rpcResp.Id
	spaceId := rpcResp.SpaceId
	policyRuleId := rpcResp.RuleId
	ruleName := rpcResp.RuleName
	templateId := rpcResp.TemplateId
	description := rpcResp.Description
	status := rpcResp.Status
	priority := rpcResp.Priority
	updatedBy := rpcResp.UpdatedBy
	aggrWindow := rpcResp.AggrWindow
	createdAt := rpcResp.CreatedAt
	updatedAt := rpcResp.UpdatedAt

	policyInfo := types.AssignmentPolicyInfo{
		BaseUUIDInfo: types.BaseUUIDInfo{
			Id:        &id,
			CreatedAt: &createdAt,
			UpdatedAt: &updatedAt,
		},
		SpaceId:     &spaceId,
		RuleId:      &policyRuleId,
		RuleName:    &ruleName,
		TemplateId:  &templateId,
		Description: &description,
		Status:      &status,
		Priority:    &priority,
		UpdatedBy:   &updatedBy,
		Layers:      &layers,
		AggrWindow:  &aggrWindow,
		TimeFilters: &timeFilters,
		Filters:     &filters,
	}

	return &types.AssignmentPolicyInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg:  "获取分配策略详情成功",
			Data: "",
		},
		Data: policyInfo,
	}, nil
}
