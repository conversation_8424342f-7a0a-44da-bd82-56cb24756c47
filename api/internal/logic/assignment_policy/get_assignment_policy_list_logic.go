package assignment_policy

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAssignmentPolicyListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取分配策略列表
func NewGetAssignmentPolicyListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAssignmentPolicyListLogic {
	return &GetAssignmentPolicyListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAssignmentPolicyListLogic) GetAssignmentPolicyList(req *types.AssignmentPolicyListReq) (resp *types.AssignmentPolicyListResp, err error) {
	// 处理指针类型参数
	var spaceId int64
	var status string
	if req.SpaceId != nil {
		spaceId = *req.SpaceId
	}
	if req.Status != nil {
		status = *req.Status
	}

	// 调用 RPC 服务获取分配策略列表
	rpcResp, err := l.svcCtx.OncallRpc.GetAssignmentPolicyList(l.ctx, &oncall.AssignmentPolicyListReq{
		SpaceId:  &spaceId,
		Status:   &status,
		Page:     req.Page,
		PageSize: req.PageSize,
	})

	if err != nil {
		l.Errorf("调用RPC获取分配策略列表失败: %v", err)
		return nil, err
	}

	// 转换响应数据
	// var list []types.AssignmentPolicyInfo
	list := make([]types.AssignmentPolicyInfo, 0, len(rpcResp.List))
	for _, item := range rpcResp.List {
		// 转换通知层级
		var layers []types.NotificationLayer
		for _, layer := range item.Layers {
			// 转换通知目标
			target := types.NotificationTarget{
				TeamIds:   layer.Target.TeamIds,
				PersonIds: layer.Target.PersonIds,
			}

			// 转换通知方式
			if layer.Target.By != nil {
				target.By = types.NotificationBy{
					FollowPreference: layer.Target.By.FollowPreference,
					Critical:         layer.Target.By.Critical,
					Warning:          layer.Target.By.Warning,
					Info:             layer.Target.By.Info,
				}
			}

			// 转换 Webhooks
			var webhooks []types.Webhook
			for _, webhook := range layer.Target.Webhooks {
				webhooks = append(webhooks, types.Webhook{
					Type:     webhook.Type,
					Settings: webhook.Settings,
				})
			}
			target.Webhooks = webhooks

			layers = append(layers, types.NotificationLayer{
				MaxTimes:       layer.MaxTimes,
				NotifyStep:     layer.NotifyStep,
				EscalateWindow: layer.EscalateWindow,
				ForceEscalate:  layer.ForceEscalate,
				Target:         target,
			})
		}

		// 转换时间过滤器
		var timeFilters []types.TimeFilter
		for _, filter := range item.TimeFilters {
			timeFilters = append(timeFilters, types.TimeFilter{
				Start:  filter.Start,
				End:    filter.End,
				Repeat: filter.Repeat,
				CalId:  filter.CalId,
				IsOff:  filter.IsOff,
			})
		}

		// 转换过滤器组 - 二维数组结构
		var filters [][]types.FilterCondition
		for _, group := range item.Filters {
			var conditions []types.FilterCondition
			for _, cond := range group.Conditions {
				conditions = append(conditions, types.FilterCondition{
					Key:  cond.Key,
					Oper: cond.Oper,
					Vals: cond.Vals,
				})
			}
			filters = append(filters, conditions)
		}

		// 转换字段为指针类型
		id := item.Id
		spaceId := item.SpaceId
		ruleId := item.RuleId
		ruleName := item.RuleName
		templateId := item.TemplateId
		description := item.Description
		status := item.Status
		priority := item.Priority
		updatedBy := item.UpdatedBy
		aggrWindow := item.AggrWindow
		createdAt := item.CreatedAt
		updatedAt := item.UpdatedAt

		info := types.AssignmentPolicyInfo{
			BaseUUIDInfo: types.BaseUUIDInfo{
				Id:        &id,
				CreatedAt: &createdAt,
				UpdatedAt: &updatedAt,
			},
			SpaceId:     &spaceId,
			RuleId:      &ruleId,
			RuleName:    &ruleName,
			TemplateId:  &templateId,
			Description: &description,
			Status:      &status,
			Priority:    &priority,
			UpdatedBy:   &updatedBy,
			Layers:      &layers,
			AggrWindow:  &aggrWindow,
			TimeFilters: &timeFilters,
			Filters:     &filters,
		}
		list = append(list, info)
	}

	return &types.AssignmentPolicyListResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg:  "获取分配策略列表成功",
			Data: "",
		},
		Data: types.AssignmentPolicyListInfo{
			BaseListInfo: types.BaseListInfo{
				Total: rpcResp.Total,
				Data:  "",
			},
			Data: list,
		},
	}, nil
}
