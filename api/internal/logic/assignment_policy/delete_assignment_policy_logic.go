package assignment_policy

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteAssignmentPolicyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除分配策略
func NewDeleteAssignmentPolicyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAssignmentPolicyLogic {
	return &DeleteAssignmentPolicyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAssignmentPolicyLogic) DeleteAssignmentPolicy(req *types.DeleteAssignmentPolicyReq) (resp *types.BaseMsgResp, err error) {
	// 参数验证
	if len(req.RuleIds) == 0 {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "请选择要删除的分配策略",
		}, nil
	}

	// 调用 RPC 服务批量删除分配策略
	rpcResp, err := l.svcCtx.OncallRpc.DeleteAssignmentPolicy(l.ctx, &oncall.UUIDsReq{
		Ids: req.RuleIds,
	})

	if err != nil {
		l.Errorf("批量删除分配策略失败，错误: %v", err)
		return &types.BaseMsgResp{
			Code: 500,
			Msg:  "删除分配策略失败",
		}, nil
	}

	l.Infof("成功批量删除分配策略，数量: %d", len(req.RuleIds))

	return &types.BaseMsgResp{
		Msg: rpcResp.Msg,
	}, nil
}
