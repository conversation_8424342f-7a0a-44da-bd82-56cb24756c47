package assignment_policy

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateAssignmentPolicyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建分配策略
func NewCreateAssignmentPolicyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAssignmentPolicyLogic {
	return &CreateAssignmentPolicyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAssignmentPolicyLogic) CreateAssignmentPolicy(req *types.CreateAssignmentPolicyReq) (resp *types.BaseMsgResp, err error) {
	// 参数验证
	if req.SpaceId == 0 {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "空间ID不能为空",
		}, nil
	}
	if req.RuleName == "" {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "规则名称不能为空",
		}, nil
	}

	// 转换通知层级
	var layers []*oncall.NotificationLayer
	for _, layer := range req.Layers {
		// 转换通知目标
		target := &oncall.NotificationTarget{
			TeamIds:   layer.Target.TeamIds,
			PersonIds: layer.Target.PersonIds,
		}

		// 转换通知方式
		if layer.Target.By.Critical != nil || layer.Target.By.Warning != nil || layer.Target.By.Info != nil {
			target.By = &oncall.NotificationBy{
				FollowPreference: layer.Target.By.FollowPreference,
				Critical:         layer.Target.By.Critical,
				Warning:          layer.Target.By.Warning,
				Info:             layer.Target.By.Info,
			}
		}

		layers = append(layers, &oncall.NotificationLayer{
			MaxTimes:       layer.MaxTimes,
			NotifyStep:     layer.NotifyStep,
			EscalateWindow: layer.EscalateWindow,
			ForceEscalate:  layer.ForceEscalate,
			Target:         target,
		})
	}

	// 转换时间过滤器
	var timeFilters []*oncall.TimeFilter
	for _, filter := range req.TimeFilters {
		timeFilters = append(timeFilters, &oncall.TimeFilter{
			Start:  filter.Start,
			End:    filter.End,
			Repeat: filter.Repeat,
			CalId:  filter.CalId,
			IsOff:  filter.IsOff,
		})
	}

	// 转换过滤器组 - 二维数组结构
	var filters []*oncall.FilterConditionGroup
	for _, conditionGroup := range req.Filters {
		var conditions []*oncall.FilterCondition
		for _, cond := range conditionGroup {
			conditions = append(conditions, &oncall.FilterCondition{
				Key:  cond.Key,
				Oper: cond.Oper,
				Vals: cond.Vals,
			})
		}
		filters = append(filters, &oncall.FilterConditionGroup{
			Conditions: conditions,
		})
	}

	// 调用 RPC 服务创建分配策略
	rpcResp, err := l.svcCtx.OncallRpc.CreateAssignmentPolicy(l.ctx, &oncall.CreateAssignmentPolicyReq{
		SpaceId:     req.SpaceId,
		RuleName:    req.RuleName,
		TemplateId:  req.TemplateId,
		Description: req.Description,
		Status:      req.Status,
		Priority:    req.Priority,
		Layers:      layers,
		AggrWindow:  req.AggrWindow,
		TimeFilters: timeFilters,
		Filters:     filters,
		UpdatedBy:   req.UpdatedBy,
	})

	if err != nil {
		l.Errorf("调用RPC创建分配策略失败: %v", err)
		return &types.BaseMsgResp{
			Code: 500,
			Msg:  "创建分配策略失败",
		}, nil
	}

	l.Infof("成功创建分配策略: %s, 空间ID: %d", req.RuleName, req.SpaceId)

	return &types.BaseMsgResp{
		Msg: rpcResp.Msg,
	}, nil
}
