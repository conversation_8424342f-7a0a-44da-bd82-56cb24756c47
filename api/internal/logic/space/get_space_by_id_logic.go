package space

import (
	"context"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSpaceByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取空间详情
func NewGetSpaceByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSpaceByIdLogic {
	return &GetSpaceByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSpaceByIdLogic) GetSpaceById(req *types.IDPathReq) (resp *types.SpaceInfoResp, err error) {
	// 从路径参数中获取ID
	id := req.Id
	if id == 0 {
		return nil, fmt.Errorf("空间ID不能为空")
	}

	// 转换ID为uint64
	// idUint, err := strconv.ParseUint(id, 10, 64)
	// if err != nil {
	// 	return nil, fmt.Errorf("无效的空间ID: %s", id)
	// }

	// 调用 RPC 服务获取空间详情
	rpcResp, err := l.svcCtx.SpaceRpc.GetSpaceById(l.ctx, &oncall.IDReq{
		Id: id,
	})

	if err != nil {
		l.Errorf("调用RPC获取空间详情失败: %v", err)
		return nil, err
	}

	// 构建空间信息
	name := rpcResp.Name
	teamName := rpcResp.TeamName
	description := rpcResp.Description
	visibility := rpcResp.Visibility
	enabled := rpcResp.Enabled
	statusChangedAt := rpcResp.StatusChangedAt

	// 转换ID为uint64
	spaceId := id
	createdAt := rpcResp.CreatedAt
	updatedAt := rpcResp.UpdatedAt

	issueStats := &types.IssueStats{
		Processing: rpcResp.IssueStats.Processing,
		Pending:    rpcResp.IssueStats.Pending,
		Completed:  rpcResp.IssueStats.Completed,
	}

	spaceInfo := types.SpaceInfo{
		BaseIDInfo: types.BaseIDInfo{
			Id:        &spaceId,
			CreatedAt: &createdAt,
			UpdatedAt: &updatedAt,
		},
		Name:            &name,
		TeamId:          &rpcResp.TeamId,
		TeamName:        &teamName,
		Description:     &description,
		Visibility:      &visibility,
		IssueStats:      issueStats,
		Enabled:         &enabled,
		StatusChangedAt: &statusChangedAt,
	}

	return &types.SpaceInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg:  "获取空间详情成功",
			Data: "",
		},
		Data: spaceInfo,
	}, nil
}
