package space

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateSpaceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新空间
func NewUpdateSpaceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateSpaceLogic {
	return &UpdateSpaceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateSpaceLogic) UpdateSpace(req *types.UpdateSpaceReq) (resp *types.BaseMsgResp, err error) {
	// 参数验证
	if req.ID == "" {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "空间ID不能为空",
		}, nil
	}

	// 调用 RPC 服务更新空间
	rpcResp, err := l.svcCtx.SpaceRpc.UpdateSpace(l.ctx, &oncall.UpdateSpaceReq{
		Id:          req.ID,
		Name:        req.Name,
		TeamId:      req.TeamId,
		TeamName:    req.TeamName,
		Description: req.Description,
		Visibility:  req.Visibility,
		Enabled:     req.Enabled,
	})

	if err != nil {
		l.Errorf("调用RPC更新空间失败: %v", err)
		return &types.BaseMsgResp{
			Code: 500,
			Msg:  "更新空间失败",
		}, nil
	}

	l.Infof("成功更新空间: %s", req.ID)

	return &types.BaseMsgResp{
		Msg:  rpcResp.Msg,
	}, nil
}
