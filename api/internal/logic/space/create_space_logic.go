package space

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/client/space"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateSpaceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建空间
func NewCreateSpaceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateSpaceLogic {
	return &CreateSpaceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateSpaceLogic) CreateSpace(req *types.CreateSpaceReq) (resp *types.BaseMsgResp, err error) {
	// 参数验证
	if req.Name == "" {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "空间名称不能为空",
		}, nil
	}

	// 调用 RPC 服务创建空间
	rpcResp, err := l.svcCtx.SpaceRpc.CreateSpace(l.ctx, &space.CreateSpaceReq{
		Name:        req.Name,
		TeamId:      req.TeamId,
		TeamName:    req.TeamName,
		Description: req.Description,
		Visibility:  req.Visibility,
		Enabled:     req.Enabled,
	})

	if err != nil {
		l.Errorf("调用RPC创建空间失败: %v", err)
		return &types.BaseMsgResp{
			Code: 500,
			Msg:  "创建空间失败",
		}, nil
	}

	l.Infof("成功创建空间: %s, ID: %s", req.Name, rpcResp.Id)

	return &types.BaseMsgResp{
		Msg:  rpcResp.Msg + ", ID: " + rpcResp.Id,
	}, nil
}
