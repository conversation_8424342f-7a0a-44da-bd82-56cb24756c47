package space

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteSpaceLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除空间
func NewDeleteSpaceLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteSpaceLogic {
	return &DeleteSpaceLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteSpaceLogic) DeleteSpace(req *types.DeleteSpaceReq) (resp *types.BaseMsgResp, err error) {
	// 参数验证
	if len(req.IDs) == 0 {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "请选择要删除的空间",
		}, nil
	}

	// 调用 RPC 服务删除空间
	rpcResp, err := l.svcCtx.SpaceRpc.DeleteSpace(l.ctx, &oncall.IDsReq{
		Ids: req.IDs,
	})

	if err != nil {
		l.Errorf("调用RPC删除空间失败: %v", err)
		return &types.BaseMsgResp{
			Code: 500,
			Msg:  "删除空间失败",
		}, nil
	}

	l.Infof("成功删除空间，数量: %d", len(req.IDs))

	return &types.BaseMsgResp{
		Msg:  rpcResp.Msg,
	}, nil
}
