package space

import (
	"context"
	"strconv"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/client/space"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSpaceListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取空间列表
func NewGetSpaceListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSpaceListLogic {
	return &GetSpaceListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSpaceListLogic) GetSpaceList(req *types.SpaceListReq) (resp *types.SpaceListResp, err error) {
	// 处理指针类型参数
	var name string
	if req.Name != nil {
		name = *req.Name
	}

	// 调用 RPC 服务获取空间列表
	rpcResp, err := l.svcCtx.SpaceRpc.GetSpaceList(l.ctx, &space.SpaceListReq{
		Name:     &name,
		Page:     req.Page,
		PageSize: req.PageSize,
	})

	if err != nil {
		l.Errorf("调用RPC获取空间列表失败: %v", err)
		return nil, err
	}

	// 转换响应数据
	// var list []types.SpaceInfo
	list := make([]types.SpaceInfo, 0, len(rpcResp.List))
	for _, item := range rpcResp.List {
		// 转换字段为指针类型
		name := item.Name
		teamName := item.TeamName
		description := item.Description
		visibility := item.Visibility
		enabled := item.Enabled
		statusChangedAt := item.StatusChangedAt

		// 转换ID为uint64
		// var id uint64 = 1 // 模拟ID
		id := item.Id
		idUint, err := strconv.ParseUint(id, 10, 64)
		if err != nil {
			l.Errorf("解析空间ID失败: %v", err)
			continue
		}
		createdAt := item.CreatedAt
		updatedAt := item.UpdatedAt

		// l.svcCtx.SystemRpc.GetTeamList()

		issueStats := &types.IssueStats{
			Processing: item.IssueStats.Processing,
			Pending:    item.IssueStats.Pending,
			Completed:  item.IssueStats.Completed,
		}

		info := types.SpaceInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id:        &idUint,
				CreatedAt: &createdAt,
				UpdatedAt: &updatedAt,
			},
			Name:            &name,
			TeamId:          &item.TeamId,
			TeamName:        &teamName,
			Description:     &description,
			Visibility:      &visibility,
			IssueStats:      issueStats,
			Enabled:         &enabled,
			StatusChangedAt: &statusChangedAt,
		}
		list = append(list, info)
	}

	return &types.SpaceListResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg:  "获取空间列表成功",
			Data: "",
		},
		Data: types.SpaceListInfo{
			BaseListInfo: types.BaseListInfo{
				Total: rpcResp.Total,
				Data:  "",
			},
			Data: list,
		},
	}, nil
}
