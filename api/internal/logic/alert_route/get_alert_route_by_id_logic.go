package alert_route

import (
	"context"
	"fmt"
	"strconv"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"
)

type GetAlertRouteByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取告警路由详情
func NewGetAlertRouteByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAlertRouteByIdLogic {
	return &GetAlertRouteByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAlertRouteByIdLogic) GetAlertRouteById(req *types.IDPathReq) (resp *types.AlertRouteInfoResp, err error) {
	// 参数验证
	if req.Id == 0 {
		return nil, fmt.Errorf("告警路由ID不能为空")
	}

	// 调用 RPC 服务获取告警路由详情
	rpcResp, err := l.svcCtx.OncallRpc.GetAlertRouteById(l.ctx, &oncall.IDReq{
		Id: req.Id,
	})

	if err != nil {
		l.Errorf("调用RPC获取告警路由详情失败: %v", err)
		return nil, err
	}

	// 转换路由条件
	var cases []types.RouteCase
	for _, c := range rpcResp.Cases {
		var conditions []types.RouteCondition
		for _, cond := range c.If {
			conditions = append(conditions, types.RouteCondition{
				Key:  cond.Key,
				Oper: cond.Oper,
				Vals: cond.Vals,
			})
		}
		cases = append(cases, types.RouteCase{
			If:          conditions,
			ChannelIds:  c.ChannelIds,
			Fallthrough: c.Fallthrough,
		})
	}

	// 转换默认路由
	var defaultRoute types.RouteDefault
	if rpcResp.Default != nil {
		defaultRoute = types.RouteDefault{
			ChannelIds: rpcResp.Default.ChannelIds,
		}
	}

	// 转换字段为指针类型
	integrationId := rpcResp.IntegrationId
	status := rpcResp.Status
	version := rpcResp.Version
	updatedBy := rpcResp.UpdatedBy
	creatorId := rpcResp.CreatorId

	// 转换ID为uint64
	routeId, parseErr := strconv.ParseUint(rpcResp.Id, 10, 64)
	if parseErr != nil {
		l.Errorf("解析告警路由ID失败: %v", parseErr)
		return nil, fmt.Errorf("解析告警路由ID失败")
	}
	createdAt := rpcResp.CreatedAt
	updatedAt := rpcResp.UpdatedAt

	alertRouteInfo := types.AlertRouteInfo{
		BaseIDInfo: types.BaseIDInfo{
			Id:        &routeId,
			CreatedAt: &createdAt,
			UpdatedAt: &updatedAt,
		},
		IntegrationId: &integrationId,
		Cases:         &cases,
		Default:       &defaultRoute,
		Status:        &status,
		Version:       &version,
		UpdatedBy:     &updatedBy,
		CreatorId:     &creatorId,
	}

	return &types.AlertRouteInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg:  "获取告警路由详情成功",
			Data: "",
		},
		Data: alertRouteInfo,
	}, nil
}
