package alert_route

import (
	"context"
	"fmt"
	"strconv"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteAlertRouteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除告警路由
func NewDeleteAlertRouteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAlertRouteLogic {
	return &DeleteAlertRouteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAlertRouteLogic) DeleteAlertRoute(req *types.DeleteAlertRouteReq) (resp *types.BaseMsgResp, err error) {
	// 参数验证
	if len(req.IDs) == 0 {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "请选择要删除的告警路由",
		}, nil
	}

	// 转换ID列表为uint64
	var ids []uint64
	for _, idStr := range req.IDs {
		id, err := strconv.ParseUint(idStr, 10, 64)
		if err != nil {
			return &types.BaseMsgResp{
				Code: 400,
				Msg:  fmt.Sprintf("无效的告警路由ID: %s", idStr),
			}, nil
		}
		ids = append(ids, id)
	}

	// 调用 RPC 服务删除告警路由
	rpcResp, err := l.svcCtx.OncallRpc.DeleteAlertRoute(l.ctx, &oncall.IDsReq{
		Ids: ids,
	})

	if err != nil {
		l.Errorf("调用RPC删除告警路由失败: %v", err)
		return &types.BaseMsgResp{
			Code: 500,
			Msg:  "删除告警路由失败",
		}, nil
	}

	l.Infof("成功删除告警路由，数量: %d", len(req.IDs))

	return &types.BaseMsgResp{
		Msg:  rpcResp.Msg,
	}, nil
}
