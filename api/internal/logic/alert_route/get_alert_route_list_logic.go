package alert_route

import (
	"context"
	"strconv"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAlertRouteListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取告警路由列表
func NewGetAlertRouteListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAlertRouteListLogic {
	return &GetAlertRouteListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAlertRouteListLogic) GetAlertRouteList(req *types.AlertRouteListReq) (resp *types.AlertRouteListResp, err error) {
	// 处理指针类型参数
	var integrationId, status string
	if req.IntegrationId != nil {
		integrationId = *req.IntegrationId
	}
	if req.Status != nil {
		status = *req.Status
	}

	// 调用 RPC 服务获取告警路由列表
	rpcResp, err := l.svcCtx.OncallRpc.GetAlertRouteList(l.ctx, &oncall.AlertRouteListReq{
		IntegrationId: &integrationId,
		Status:        &status,
		Page:          req.Page,
		PageSize:      req.PageSize,
	})

	if err != nil {
		l.Errorf("调用RPC获取告警路由列表失败: %v", err)
		return nil, err
	}

	// 转换响应数据
	var list []types.AlertRouteInfo
	for _, item := range rpcResp.List {
		// 转换路由条件
		var cases []types.RouteCase
		for _, c := range item.Cases {
			var conditions []types.RouteCondition
			for _, cond := range c.If {
				conditions = append(conditions, types.RouteCondition{
					Key:  cond.Key,
					Oper: cond.Oper,
					Vals: cond.Vals,
				})
			}
			cases = append(cases, types.RouteCase{
				If:          conditions,
				ChannelIds:  c.ChannelIds,
				Fallthrough: c.Fallthrough,
			})
		}

		// 转换默认路由
		var defaultRoute types.RouteDefault
		if item.Default != nil {
			defaultRoute = types.RouteDefault{
				ChannelIds: item.Default.ChannelIds,
			}
		}

		// 转换字段为指针类型
		integrationId := item.IntegrationId
		status := item.Status
		version := item.Version
		updatedBy := item.UpdatedBy
		creatorId := item.CreatorId

		// 转换ID为uint64
		id, parseErr := strconv.ParseUint(item.Id, 10, 64)
		if parseErr != nil {
			l.Errorf("解析告警路由ID失败: %v", parseErr)
			continue
		}
		createdAt := item.CreatedAt
		updatedAt := item.UpdatedAt

		info := types.AlertRouteInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id:        &id,
				CreatedAt: &createdAt,
				UpdatedAt: &updatedAt,
			},
			IntegrationId: &integrationId,
			Cases:         &cases,
			Default:       &defaultRoute,
			Status:        &status,
			Version:       &version,
			UpdatedBy:     &updatedBy,
			CreatorId:     &creatorId,
		}
		list = append(list, info)
	}

	return &types.AlertRouteListResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg:  "获取告警路由列表成功",
			Data: "",
		},
		Data: types.AlertRouteListInfo{
			BaseListInfo: types.BaseListInfo{
				Total: rpcResp.Total,
				Data:  "",
			},
			Data: list,
		},
	}, nil
}
