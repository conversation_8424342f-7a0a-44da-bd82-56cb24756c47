package alert_route

import (
	"context"
	"strconv"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateAlertRouteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新告警路由
func NewUpdateAlertRouteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateAlertRouteLogic {
	return &UpdateAlertRouteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateAlertRouteLogic) UpdateAlertRoute(req *types.UpdateAlertRouteReq) (resp *types.BaseMsgResp, err error) {
	// 参数验证
	if req.ID == "" {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "告警路由ID不能为空",
		}, nil
	}

	// 转换路由条件
	var cases []*oncall.RouteCase
	for _, c := range req.Cases {
		var conditions []*oncall.RouteCondition
		for _, cond := range c.If {
			conditions = append(conditions, &oncall.RouteCondition{
				Key:  cond.Key,
				Oper: cond.Oper,
				Vals: cond.Vals,
			})
		}
		cases = append(cases, &oncall.RouteCase{
			If:          conditions,
			ChannelIds:  c.ChannelIds,
			Fallthrough: c.Fallthrough,
		})
	}

	// 转换默认路由
	defaultRoute := &oncall.RouteDefault{
		ChannelIds: req.Default.ChannelIds,
	}

	// 验证ID格式
	_, parseErr := strconv.ParseUint(req.ID, 10, 64)
	if parseErr != nil {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "无效的告警路由ID",
		}, nil
	}

	// 调用 RPC 服务更新告警路由
	rpcResp, err := l.svcCtx.OncallRpc.UpdateAlertRoute(l.ctx, &oncall.UpdateAlertRouteReq{
		Id:            req.ID,
		IntegrationId: req.IntegrationId,
		Cases:         cases,
		Default:       defaultRoute,
		Status:        req.Status,
		UpdatedBy:     req.UpdatedBy,
		Version:       req.Version,
	})

	if err != nil {
		l.Errorf("调用RPC更新告警路由失败: %v", err)
		return &types.BaseMsgResp{
			Code: 500,
			Msg:  "更新告警路由失败",
		}, nil
	}

	l.Infof("成功更新告警路由: %s", req.ID)

	return &types.BaseMsgResp{
		Msg:  rpcResp.Msg,
	}, nil
}
