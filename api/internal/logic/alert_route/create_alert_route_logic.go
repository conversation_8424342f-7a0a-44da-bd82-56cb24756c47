package alert_route

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateAlertRouteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建告警路由
func NewCreateAlertRouteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAlertRouteLogic {
	return &CreateAlertRouteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAlertRouteLogic) CreateAlertRoute(req *types.CreateAlertRouteReq) (resp *types.BaseMsgResp, err error) {
	// 参数验证
	if req.IntegrationId == "" {
		return &types.BaseMsgResp{
			Code: 400,
			Msg:  "集成ID不能为空",
		}, nil
	}

	// 转换路由条件
	var cases []*oncall.RouteCase
	for _, c := range req.Cases {
		var conditions []*oncall.RouteCondition
		for _, cond := range c.If {
			conditions = append(conditions, &oncall.RouteCondition{
				Key:  cond.Key,
				Oper: cond.Oper,
				Vals: cond.Vals,
			})
		}
		cases = append(cases, &oncall.RouteCase{
			If:          conditions,
			ChannelIds:  c.ChannelIds,
			Fallthrough: c.Fallthrough,
		})
	}

	// 转换默认路由
	defaultRoute := &oncall.RouteDefault{
		ChannelIds: req.Default.ChannelIds,
	}

	// 调用 RPC 服务创建告警路由
	rpcResp, err := l.svcCtx.OncallRpc.CreateAlertRoute(l.ctx, &oncall.CreateAlertRouteReq{
		IntegrationId: req.IntegrationId,
		Cases:         cases,
		Default:       defaultRoute,
		Status:        req.Status,
		CreatorId:     req.CreatorId,
	})

	if err != nil {
		l.Errorf("调用RPC创建告警路由失败: %v", err)
		return &types.BaseMsgResp{
			Code: 500,
			Msg:  "创建告警路由失败",
		}, nil
	}

	l.Infof("成功创建告警路由，集成ID: %s, 路由ID: %s", req.IntegrationId, rpcResp.Id)

	return &types.BaseMsgResp{
		Msg:  rpcResp.Msg + ", ID: " + rpcResp.Id,
	}, nil
}
