package assignment_policy

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/logic/assignment_policy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/types"
)

// 获取分配策略详情
func GetAssignmentPolicyByIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RuleIDPathReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := assignment_policy.NewGetAssignmentPolicyByIdLogic(r.Context(), svcCtx)
		resp, err := l.GetAssignmentPolicyById(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
