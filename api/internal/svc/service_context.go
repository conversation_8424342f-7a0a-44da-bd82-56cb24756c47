package svc

import (
	"github.com/casbin/casbin/v2"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_common/middleware"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_common/utils/i18n"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/api/internal/config"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/client/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/client/space"
	"gitlab.xsjcs.cn/zhangyupeng2/zero_duty_system/rpc/client/system"

	"github.com/redis/go-redis/v9"
)

type ServiceContext struct {
	Config    config.Config
	Authority rest.Middleware
	Redis     redis.UniversalClient
	Casbin    *casbin.Enforcer
	Trans     *i18n.Translator
	SystemRpc system.System
	OncallRpc integration.Integration
	SpaceRpc  space.Space
}

func NewServiceContext(c config.Config) *ServiceContext {
	rds := c.RedisConf.MustNewUniversalRedis()

	trans := i18n.NewTranslator(c.I18nConf, i18n.LocaleFS)
	systemClient := system.NewSystem(zrpc.MustNewClient(c.SystemRpc))
	oncallClient := integration.NewIntegration(zrpc.MustNewClient(c.OncallRpc))
	spaceClient := space.NewSpace(zrpc.MustNewClient(c.OncallRpc))

	cbn := c.CasbinConf.MustNewCasbinWithOriginalRedisWatcher(c.DatabaseConf.Type, c.DatabaseConf.GetDSN(),
		c.RedisConf)
	return &ServiceContext{
		Config:    c,
		Authority: middleware.NewAuthorityMiddleware(c.Auth.AccessSecret, cbn, rds, trans).Handle,
		SystemRpc: systemClient,
		OncallRpc: oncallClient,
		SpaceRpc:  spaceClient,
		Redis:     rds,
		Casbin:    cbn,
		Trans:     trans,
	}
}
