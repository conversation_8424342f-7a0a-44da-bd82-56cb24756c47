# Assignment Policy 列表查询问题修复

## 问题描述
创建分配策略成功后，通过列表查询 `{"page": 1, "pageSize": 10, "spaceId": 1}` 没有返回完整的功能示例数据。

## 问题原因
在RPC层的 `get_assignment_policy_list_logic.go` 中，第77-80行返回的是空数组，没有解析存储在数据库中的JSON数据：

```go
// 原来的代码 - 返回空数组
Layers:      []*oncall.NotificationLayer{}, // 空数组
AggrWindow:  item.AggrWindow,
TimeFilters: []*oncall.TimeFilter{},           // 空数组
Filters:     []*oncall.FilterConditionGroup{}, // 空数组
```

## 修复方案

### 1. 添加JSON解析函数
在 `utils.go` 中添加了以下函数：
- `ConvertJSONToLayers()` - 解析分层通知数据
- `ConvertJSONToTimeFilters()` - 解析时间过滤器数据
- `ConvertJSONToFilters()` - 解析过滤器组数据

### 2. 修复数据类型错误
- `oncall.RoleIds` → `oncall.ScheduleRoles`
- `map[string]interface{}` → `map[string]string` (Webhook settings)

### 3. 更新列表查询逻辑
```go
// 修复后的代码 - 解析JSON数据
layers, err := ConvertJSONToLayers(item.Layers)
if err != nil {
    l.Errorf("解析分层通知数据失败: %v", err)
    layers = []*oncall.NotificationLayer{} // 使用空数组作为默认值
}

timeFilters, err := ConvertJSONToTimeFilters(item.TimeFilters)
if err != nil {
    l.Errorf("解析时间过滤器数据失败: %v", err)
    timeFilters = []*oncall.TimeFilter{} // 使用空数组作为默认值
}

filters, err := ConvertJSONToFilters(item.Filters)
if err != nil {
    l.Errorf("解析过滤器组数据失败: %v", err)
    filters = []*oncall.FilterConditionGroup{} // 使用空数组作为默认值
}

info := &oncall.AssignmentPolicyInfo{
    // ... 其他字段
    Layers:      layers,
    TimeFilters: timeFilters,
    Filters:     filters,
}
```

### 4. 同步修复详情查询
在 `get_assignment_policy_by_id_logic.go` 中应用了相同的JSON解析逻辑。

## 数据流程

### 创建流程
1. **API层** 接收JSON请求
2. **RPC层** 调用 `ConvertLayersToJSON()` 等函数将protobuf转换为JSON
3. **数据库** 存储JSON格式的复杂数据

### 查询流程
1. **数据库** 返回包含JSON字段的记录
2. **RPC层** 调用 `ConvertJSONToLayers()` 等函数将JSON转换为protobuf
3. **API层** 将protobuf转换为响应JSON

## 测试验证

### 1. 创建测试数据
```bash
curl -X POST http://localhost:8888/api/v1/assignment-policies \
  -H "Content-Type: application/json" \
  -d '{
    "spaceId": 1,
    "ruleName": "测试完整功能",
    "status": "enabled",
    "priority": 1,
    "layers": [
      {
        "maxTimes": 3,
        "notifyStep": 1,
        "escalateWindow": 15,
        "forceEscalate": true,
        "target": {
          "teamIds": [1, 2],
          "personIds": [101, 102],
          "by": {
            "followPreference": true,
            "critical": ["email", "sms"],
            "warning": ["email"],
            "info": ["email"]
          },
          "webhooks": [
            {
              "type": "slack",
              "settings": {
                "webhook_url": "https://hooks.slack.com/test",
                "channel": "#alerts"
              }
            }
          ]
        }
      }
    ],
    "aggrWindow": 300,
    "timeFilters": [
      {
        "start": "09:00",
        "end": "18:00",
        "repeat": [1, 2, 3, 4, 5],
        "calId": "work-calendar",
        "isOff": false
      }
    ],
    "filters": [
      [
        {
          "key": "severity",
          "oper": "IN",
          "vals": ["Critical"]
        }
      ]
    ],
    "updatedBy": 1
  }'
```

### 2. 查询列表验证
```bash
curl -X POST http://localhost:8888/api/v1/assignment-policies/list \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "pageSize": 10,
    "spaceId": 1
  }'
```

### 3. 预期结果
现在应该返回完整的数据，包括：
- ✅ `layers` - 完整的分层通知配置
- ✅ `timeFilters` - 时间过滤器配置
- ✅ `filters` - 二维数组过滤器配置
- ✅ `webhooks` - Webhook配置详情

## 关键修复点

1. **JSON解析** - 添加了完整的JSON到protobuf转换逻辑
2. **类型匹配** - 修复了protobuf类型定义不匹配的问题
3. **错误处理** - 添加了JSON解析失败时的降级处理
4. **数据完整性** - 确保列表和详情查询返回相同的完整数据

## 注意事项

1. **向后兼容** - 如果JSON解析失败，会返回空数组而不是错误
2. **性能考虑** - JSON解析会增加一些CPU开销，但对于分配策略这种配置数据是可接受的
3. **数据一致性** - 创建和查询使用相同的转换逻辑，确保数据一致性

修复完成后，列表查询应该能够返回创建时的完整配置数据。
