#!/bin/bash

echo "=== 测试分配策略CRUD功能 ==="

# 1. 创建一个简单的分配策略
echo "1. 创建分配策略..."
CREATE_RESPONSE=$(curl -s -X POST http://localhost:2002/api/v1/assignment-policies \
  -H "Content-Type: application/json" \
  -d '{
    "spaceId": 1,
    "ruleName": "调试测试策略",
    "description": "用于调试的测试策略",
    "status": "enabled",
    "priority": 1,
    "layers": [
      {
        "maxTimes": 2,
        "notifyStep": 1,
        "escalateWindow": 15,
        "forceEscalate": false,
        "target": {
          "personIds": [101],
          "by": {
            "followPreference": true,
            "critical": ["email"],
            "warning": ["email"],
            "info": ["email"]
          },
          "webhooks": [
            {
              "type": "slack",
              "settings": {
                "webhook_url": "https://hooks.slack.com/test",
                "channel": "#test"
              }
            }
          ]
        }
      }
    ],
    "aggrWindow": 300,
    "timeFilters": [
      {
        "start": "09:00",
        "end": "18:00",
        "repeat": [1, 2, 3, 4, 5],
        "calId": "test-calendar",
        "isOff": false
      }
    ],
    "filters": [
      [
        {
          "key": "severity",
          "oper": "EQ",
          "vals": ["Critical"]
        }
      ]
    ],
    "updatedBy": 1
  }')

echo "创建响应: $CREATE_RESPONSE"

# 2. 查询列表
echo -e "\n2. 查询分配策略列表..."
LIST_RESPONSE=$(curl -s -X POST http://localhost:2002/api/v1/assignment-policies/list \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "pageSize": 10,
    "spaceId": 1
  }')

echo "列表响应: $LIST_RESPONSE"

# 3. 检查响应中是否包含完整数据
echo -e "\n3. 检查数据完整性..."
if echo "$LIST_RESPONSE" | grep -q '"layers":\[\]'; then
    echo "❌ 问题：layers字段为空数组"
else
    echo "✅ layers字段包含数据"
fi

if echo "$LIST_RESPONSE" | grep -q '"timeFilters":\[\]'; then
    echo "❌ 问题：timeFilters字段为空数组"
else
    echo "✅ timeFilters字段包含数据"
fi

if echo "$LIST_RESPONSE" | grep -q '"filters":\[\]'; then
    echo "❌ 问题：filters字段为空数组"
else
    echo "✅ filters字段包含数据"
fi

# 4. 美化输出JSON（如果安装了jq）
if command -v jq &> /dev/null; then
    echo -e "\n4. 格式化的列表响应:"
    echo "$LIST_RESPONSE" | jq '.'
else
    echo -e "\n4. 原始列表响应:"
    echo "$LIST_RESPONSE"
fi

echo -e "\n=== 测试完成 ==="
