## Go-Zero微服务开发标准流程

### API层开发流程：

1. **创建API定义文件**
   - 在 `api/desc/` 目录下创建具体业务的API文件（如 `integration.api`）
   - 必须导入 `base.api` 作为基础数据结构
   - 所有返回类型必须继承适当的base类型：
     - 单条数据返回：继承 `BaseDataInfo`
     - 列表数据返回：继承 `BaseListInfo` 
     - 消息返回：使用 `BaseMsgResp`
   - 添加中文注释说明业务逻辑

2. **验证和生成代码**
   ```bash
   # 验证API文件格式
   goctl api validate -api desc/all.api
   
   # 生成go_zero风格的代码（蛇形命名）
   goctl api go -api desc/all.api -dir . --style go_zero
   ```

3. **配置服务依赖**
   - 在 `api/etc/` 目录的配置文件中添·加Redis、数据库或RPC客户端配置
   - 修改 `api/internal/svc/service_context.go` 添加相关配置的初始化
   - 确保所有外部依赖都正确配置

4. **实现业务逻辑**
   - 在生成的logic文件中编写具体业务逻辑
   - 调用RPC服务获取数据
   - 进行参数验证和错误处理
   - 返回符合API定义的响应格式

### RPC层开发流程：

1. **创建Proto定义文件**
   - 根据API文件的需求在 `rpc/desc/` 目录创建或更新proto文件
   - 定义service接口和message结构
   - 确保字段类型与API层保持一致

2. **生成RPC代码**
   ```bash
   # 生成RPC模板代码
   goctl rpc protoc ./desc/oncall.proto --style=go_zero --go_out=./pb --go-grpc_out=./pb --zrpc_out=. -m
   ```

3. **配置数据层依赖**
   - 在 `rpc/etc/` 目录的配置文件中添加Redis和数据库配置
   - 修改 `rpc/internal/svc/service_context.go` 添加数据库和Redis客户端初始化
   - 确保数据层连接正常

4. **设计数据模型**
   - 在 `rpc/ent/schema/` 目录编写ent schema结构代码
   - 定义实体关系和字段约束
   - 考虑数据库索引和性能优化

5. **生成ORM代码**
   ```bash
   # 生成ent ORM代码
   go run -mod=mod entgo.io/ent/cmd/ent generate --template glob="./ent/template/*.tmpl" ./ent/schema --feature sql/execquery,intercept,sql/modifier
   ```

6. **实现业务逻辑**
   - 在生成的logic文件中编写数据操作逻辑
   - 使用ent ORM进行数据库操作
   - 实现完整的CRUD功能
   - 添加适当的事务处理和错误处理

### 注意事项：
- 所有业务逻辑必须添加中文注释
- 确保完整的参数验证和错误处理
- API响应格式必须符合统一的数据结构规范
- 遵循go_zero的代码风格和最佳实践

---

## 📊 项目开发进度跟踪

### 项目整体完成度：约 65%

## ✅ 已完成任务

### 1. 基础架构模块 (100% 完成)

- [x] **base.api基础数据结构** - 定义统一的响应格式和数据结构
- [x] **Go-Zero项目脚手架搭建** - API层和RPC层的完整项目结构
- [x] **数据库连接和配置** - MySQL数据库连接和Ent ORM配置
- [x] **Redis缓存配置** - Redis缓存服务的集成和配置

### 2. Integration集成模块 (100% 完成)

- [x] **Integration API定义** - 集成管理的API接口定义和数据结构
- [x] **Integration RPC服务** - 集成管理的RPC服务实现
- [x] **Integration数据库Schema** - Integration实体的Ent Schema定义
- [x] **Integration CRUD操作** - 集成的增删改查功能实现
- [x] **Integration Apifox文档** - 集成模块的API文档生成

### 3. Space空间模块 (100% 完成)

- [x] **Space API定义** - 空间管理的API接口定义和数据结构
- [x] **Space RPC服务** - 空间管理的RPC服务实现
- [x] **Space数据库Schema** - Space实体的Ent Schema定义
- [x] **Space CRUD操作** - 空间的增删改查功能实现
- [x] **Space与Integration关联** - 空间与集成的关联关系实现

### 4. Assignment Policy分配策略模块 (80% 完成)

- [x] **Assignment Policy API定义** - 分配策略的API接口定义和数据结构
- [x] **Assignment Policy数据库Schema** - AssignmentPolicy实体的Ent Schema定义
- [x] **Assignment Policy Apifox文档** - 分配策略模块的API文档生成
- [🔄] **Assignment Policy RPC服务** - 分配策略的RPC服务实现 (进行中)

### 5. Alert Route告警路由模块 (40% 完成)

- [x] **Alert Route API定义** - 告警路由的API接口定义和数据结构
- [x] **Alert Route数据库Schema** - AlertRoute实体的Ent Schema定义

## ⏳ 待开发任务计划排期

### 第1周 (当前周) - Assignment Policy模块完成

目标：完成Assignment Policy分配策略模块

- [ ] 完成Assignment Policy RPC服务实现
- [ ] 实现Assignment Policy CRUD操作
- **预期交付**: Assignment Policy模块100%完成

### 第2周 - Alert Route模块开发

目标：完成Alert Route告警路由模块

- [ ] Alert Route RPC服务实现
- [ ] Alert Route CRUD操作实现
- [ ] Alert Route与Integration关联关系实现
- **预期交付**: Alert Route模块100%完成

### 第3周 - 测试和文档完善

目标：建立完整的测试体系

- [ ] 单元测试编写 - 为所有模块编写单元测试用例
- [ ] 集成测试 - API层和RPC层的集成测试
- [ ] Apifox文档完善 - 所有模块的Apifox API文档完善
- [ ] 性能测试 - 系统性能和压力测试
- **预期交付**: 测试覆盖率达到80%以上

### 第4周 - 部署和运维

目标：生产环境部署准备

- [ ] Docker镜像构建 - API和RPC服务的Docker镜像构建
- [ ] Docker Compose配置 - 完整系统的Docker Compose部署配置
- [ ] 监控和日志 - Prometheus监控和日志收集配置
- [ ] CI/CD流水线 - 自动化构建和部署流水线
- **预期交付**: 可部署的完整系统

## 🎯 关键里程碑

| 里程碑 | 预期时间 | 交付内容 |
|--------|----------|----------|
| 核心功能完成 | 2周后 | 所有业务模块开发完成 |
| 测试完成 | 3周后 | 完整的测试覆盖和文档 |
| 生产就绪 | 4周后 | 可部署的完整系统 |
| 性能优化 | 5-6周后 | 性能测试和优化完成 |

## 📋 技术债务和风险管控

### 当前技术债务

1. **缺少单元测试** - 影响代码质量保证，需在第3周重点解决
2. **性能测试缺失** - 生产环境性能未知，需要压力测试验证
3. **监控体系不完整** - 运维可观测性不足，需要完善监控配置

### 潜在风险及应对策略

1. **Assignment Policy复杂度风险**
   - 风险：分配策略逻辑较复杂，可能存在边界情况
   - 应对：增加详细的单元测试和集成测试覆盖

2. **数据一致性风险**
   - 风险：多模块间的数据关联需要事务保证
   - 应对：使用数据库事务和分布式锁机制

3. **性能瓶颈风险**
   - 风险：大量告警数据的处理性能需要验证
   - 应对：进行压力测试，必要时进行性能优化

## 🔧 技术栈总结

- **微服务框架**: Go-Zero
- **数据库**: PostgreSQL + Ent ORM
- **缓存**: Redis
- **API文档**: Apifox
- **容器化**: Docker + Docker Compose
- **监控**: Prometheus (计划中)
- **代码风格**: go_zero命名规范

## 📞 联系方式

- **开发负责人**: zhangyupeng
- **邮箱**: <<EMAIL>>
- **项目状态**: 开发中 (65% 完成)