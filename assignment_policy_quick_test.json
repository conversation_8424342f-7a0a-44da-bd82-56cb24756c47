{"创建分配策略_最简版": {"spaceId": 1, "ruleName": "测试策略", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 2, "notifyStep": 1, "escalateWindow": 15, "forceEscalate": false, "target": {"personIds": [101], "by": {"followPreference": true, "critical": ["email"], "warning": ["email"], "info": ["email"]}, "webhooks": []}}], "aggrWindow": 300, "filters": [[{"key": "severity", "oper": "EQ", "vals": ["Critical"]}]], "updatedBy": 1}, "创建分配策略_标准版": {"spaceId": 1, "ruleName": "标准告警策略", "templateId": "template-001", "description": "标准的告警分派策略", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 3, "notifyStep": 1, "escalateWindow": 15, "forceEscalate": true, "target": {"teamIds": [1], "personIds": [101, 102], "by": {"followPreference": true, "critical": ["email", "sms"], "warning": ["email"], "info": ["email"]}, "webhooks": [{"type": "slack", "settings": {"webhook_url": "https://hooks.slack.com/services/xxx", "channel": "#alerts"}}]}}], "aggrWindow": 300, "timeFilters": [{"start": "09:00", "end": "18:00", "repeat": [1, 2, 3, 4, 5], "calId": "work-calendar", "isOff": false}], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical", "High"]}]], "updatedBy": 1}, "创建分配策略_复杂版": {"spaceId": 1, "ruleName": "复杂告警策略", "templateId": "template-complex", "description": "支持多层级升级的复杂告警策略", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 2, "notifyStep": 1, "escalateWindow": 10, "forceEscalate": true, "target": {"personIds": [101], "by": {"followPreference": true, "critical": ["email", "sms"], "warning": ["email"], "info": ["email"]}, "webhooks": []}}, {"maxTimes": 3, "notifyStep": 2, "escalateWindow": 20, "forceEscalate": true, "target": {"teamIds": [1], "personIds": [102, 103], "scheduleToRoleIds": {"schedule-001": [1, 2]}, "by": {"followPreference": false, "critical": ["phone", "sms", "email"], "warning": ["email", "slack"], "info": ["email"]}, "webhooks": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"integration_key": "pd_key_123", "severity": "critical"}}]}}], "aggrWindow": 180, "timeFilters": [{"start": "00:00", "end": "23:59", "repeat": [1, 2, 3, 4, 5, 6, 7], "calId": "24x7-calendar", "isOff": false}], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical", "Fatal"]}, {"key": "service", "oper": "EQ", "vals": ["payment-service"]}], [{"key": "environment", "oper": "EQ", "vals": ["production"]}]], "updatedBy": 1}, "更新分配策略_部分更新": {"ruleId": "rule-001", "ruleName": "更新后的策略名称", "description": "更新后的描述", "priority": 2, "updatedBy": 1}, "更新分配策略_状态切换": {"ruleId": "rule-002", "status": "disabled", "updatedBy": 1}, "更新分配策略_优先级调整": {"ruleId": "rule-003", "priority": 5, "updatedBy": 1}, "更新分配策略_通知目标": {"ruleId": "rule-004", "layers": [{"maxTimes": 4, "notifyStep": 1, "escalateWindow": 25, "forceEscalate": true, "target": {"teamIds": [2, 3], "personIds": [201, 202], "by": {"followPreference": true, "critical": ["phone", "email"], "warning": ["email"], "info": ["email"]}, "webhooks": [{"type": "feishu_app", "settings": {"app_id": "cli_new_app", "chat_id": "oc_new_chat"}}]}}], "updatedBy": 1}, "更新分配策略_过滤条件": {"ruleId": "rule-005", "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical", "High", "Medium"]}], [{"key": "tags.team", "oper": "EQ", "vals": ["backend"]}, {"key": "environment", "oper": "IN", "vals": ["production", "staging"]}]], "updatedBy": 1}, "更新分配策略_时间窗口": {"ruleId": "rule-006", "timeFilters": [{"start": "08:00", "end": "20:00", "repeat": [1, 2, 3, 4, 5, 6], "calId": "extended-hours", "isOff": false}], "aggrWindow": 600, "updatedBy": 1}}