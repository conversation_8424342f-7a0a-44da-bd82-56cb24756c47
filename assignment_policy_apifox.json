{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "Assignment Policy API", "description": "分配策略管理 API 文档", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "分配策略管理", "id": 1, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "空间分配策略的CRUD操作", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"id": 2, "name": "创建分配策略", "type": "httpApi", "parentId": 1, "serverId": "", "description": "创建新的分配策略", "api": {"id": 2, "method": "POST", "path": "/api/v1/assignment-policies", "name": "创建分配策略", "description": "创建新的分配策略", "tags": ["assignment_policy"], "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"spaceId": {"type": "integer", "description": "空间ID"}, "ruleName": {"type": "string", "description": "规则名称"}, "templateId": {"type": "string", "description": "模板ID"}, "description": {"type": "string", "description": "描述"}, "status": {"type": "string", "enum": ["enabled", "disabled"], "description": "状态"}, "priority": {"type": "integer", "description": "优先级"}, "layers": {"type": "array", "items": {"type": "object", "properties": {"maxTimes": {"type": "integer", "description": "最大通知次数"}, "notifyStep": {"type": "integer", "description": "通知步骤"}, "escalateWindow": {"type": "integer", "description": "升级窗口(分钟)"}, "forceEscalate": {"type": "boolean", "description": "强制升级"}, "target": {"type": "object", "properties": {"teamIds": {"type": "array", "items": {"type": "integer"}, "description": "团队ID列表"}, "personIds": {"type": "array", "items": {"type": "integer"}, "description": "个人ID列表"}, "scheduleToRoleIds": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "integer"}}, "description": "排班角色映射"}, "by": {"type": "object", "properties": {"followPreference": {"type": "boolean", "description": "遵循偏好设置"}, "critical": {"type": "array", "items": {"type": "string"}, "description": "严重级别通知方式"}, "warning": {"type": "array", "items": {"type": "string"}, "description": "警告级别通知方式"}, "info": {"type": "array", "items": {"type": "string"}, "description": "信息级别通知方式"}}}, "webhooks": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "description": "Webhook类型"}, "settings": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Webhook设置"}}}, "description": "Webhook列表"}}}}}, "description": "分层通知"}, "aggrWindow": {"type": "integer", "description": "聚合窗口(秒)"}, "timeFilters": {"type": "array", "items": {"type": "object", "properties": {"start": {"type": "string", "description": "开始时间 HH:MM"}, "end": {"type": "string", "description": "结束时间 HH:MM"}, "repeat": {"type": "array", "items": {"type": "integer"}, "description": "重复日期 (1-7)"}, "calId": {"type": "string", "description": "日历ID"}, "isOff": {"type": "boolean", "description": "是否关闭"}}}, "description": "时间过滤器"}, "filters": {"type": "array", "items": {"type": "array", "items": {"type": "object", "properties": {"key": {"type": "string", "description": "字段名"}, "oper": {"type": "string", "description": "操作符"}, "vals": {"type": "array", "items": {"type": "string"}, "description": "值列表"}}}}, "description": "过滤器组"}, "updatedBy": {"type": "integer", "description": "创建者ID"}}, "required": ["spaceId", "ruleName", "status"]}, "example": {"spaceId": 1, "ruleName": "关键告警分派策略", "templateId": "template-001", "description": "处理关键级别告警的分派策略", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 3, "notifyStep": 1, "escalateWindow": 15, "forceEscalate": true, "target": {"teamIds": [1], "personIds": [101, 102], "scheduleToRoleIds": {"schedule-001": [1, 2]}, "by": {"followPreference": true, "critical": ["email", "sms"], "warning": ["email"], "info": ["email"]}, "webhooks": [{"type": "feishu_app", "settings": {}}]}}], "aggrWindow": 300, "timeFilters": [{"start": "09:00", "end": "18:00", "repeat": [1, 2, 3, 4, 5], "calId": "calendar-001", "isOff": false}], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical"]}]], "updatedBy": 1}}, "responses": [{"id": 1, "name": "成功响应", "code": 200, "contentType": "application/json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误代码"}, "msg": {"type": "string", "description": "提示信息"}}}, "example": {"code": 0, "msg": "创建成功"}}]}}, {"id": 3, "name": "获取分配策略列表", "type": "httpApi", "parentId": 1, "serverId": "", "description": "获取分配策略列表", "api": {"id": 3, "method": "POST", "path": "/api/v1/assignment-policies/list", "name": "获取分配策略列表", "description": "获取分配策略列表", "tags": ["assignment_policy"], "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码"}, "pageSize": {"type": "integer", "description": "每页数量"}, "spaceId": {"type": "integer", "description": "空间ID"}, "status": {"type": "string", "description": "状态"}}, "required": ["page", "pageSize"]}, "example": {"page": 1, "pageSize": 10, "spaceId": 1, "status": "enabled"}}, "responses": [{"id": 1, "name": "成功响应", "code": 200, "contentType": "application/json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误代码"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "总数"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "UUID"}, "spaceId": {"type": "integer", "description": "空间ID"}, "ruleId": {"type": "string", "description": "规则ID"}, "ruleName": {"type": "string", "description": "规则名称"}, "templateId": {"type": "string", "description": "模板ID"}, "description": {"type": "string", "description": "描述"}, "status": {"type": "string", "description": "状态"}, "priority": {"type": "integer", "description": "优先级"}, "createdAt": {"type": "integer", "description": "创建时间"}, "updatedAt": {"type": "integer", "description": "更新时间"}, "updatedBy": {"type": "integer", "description": "更新者ID"}}}}}}}}, "example": {"code": 0, "msg": "success", "data": {"total": 2, "data": [{"id": "550e8400-e29b-41d4-a716-************", "spaceId": 1, "ruleId": "rule-001", "ruleName": "关键告警分派策略", "templateId": "template-001", "description": "处理关键级别告警的分派策略", "status": "enabled", "priority": 1, "createdAt": 1682409000, "updatedAt": 1682409000, "updatedBy": 1}]}}}]}}, {"id": 4, "name": "获取分配策略详情", "type": "httpApi", "parentId": 1, "serverId": "", "description": "根据规则ID获取分配策略详情", "api": {"id": 4, "method": "GET", "path": "/api/v1/assignment-policies/{ruleId}", "name": "获取分配策略详情", "description": "根据规则ID获取分配策略详情", "tags": ["assignment_policy"], "parameters": [{"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "规则ID", "example": "rule-001"}], "responses": [{"id": 1, "name": "成功响应", "code": 200, "contentType": "application/json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误代码"}, "msg": {"type": "string", "description": "提示信息"}, "data": {"type": "object", "properties": {"id": {"type": "string", "description": "UUID"}, "spaceId": {"type": "integer", "description": "空间ID"}, "ruleId": {"type": "string", "description": "规则ID"}, "ruleName": {"type": "string", "description": "规则名称"}, "templateId": {"type": "string", "description": "模板ID"}, "description": {"type": "string", "description": "描述"}, "status": {"type": "string", "description": "状态"}, "priority": {"type": "integer", "description": "优先级"}, "layers": {"type": "array", "description": "分层通知"}, "aggrWindow": {"type": "integer", "description": "聚合窗口(秒)"}, "timeFilters": {"type": "array", "description": "时间过滤器"}, "filters": {"type": "array", "description": "过滤器组"}, "createdAt": {"type": "integer", "description": "创建时间"}, "updatedAt": {"type": "integer", "description": "更新时间"}, "updatedBy": {"type": "integer", "description": "更新者ID"}}}}}, "example": {"code": 0, "msg": "success", "data": {"id": "550e8400-e29b-41d4-a716-************", "spaceId": 1, "ruleId": "rule-001", "ruleName": "关键告警分派策略", "templateId": "template-001", "description": "处理关键级别告警的分派策略", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 3, "notifyStep": 1, "escalateWindow": 15, "forceEscalate": true, "target": {"teamIds": [1], "personIds": [101, 102], "scheduleToRoleIds": {"schedule-001": [1, 2]}, "by": {"followPreference": true, "critical": ["email", "sms"], "warning": ["email"], "info": ["email"]}, "webhooks": [{"type": "feishu_app", "settings": {}}]}}], "aggrWindow": 300, "timeFilters": [{"start": "09:00", "end": "18:00", "repeat": [1, 2, 3, 4, 5], "calId": "calendar-001", "isOff": false}], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical"]}]], "createdAt": 1682409000, "updatedAt": 1682409000, "updatedBy": 1}}}]}}, {"id": 5, "name": "更新分配策略", "type": "httpApi", "parentId": 1, "serverId": "", "description": "更新分配策略", "api": {"id": 5, "method": "PUT", "path": "/api/v1/assignment-policies/{ruleId}", "name": "更新分配策略", "description": "更新分配策略", "tags": ["assignment_policy"], "parameters": [{"name": "ruleId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "规则ID", "example": "rule-001"}], "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"ruleId": {"type": "string", "description": "规则ID"}, "spaceId": {"type": "integer", "description": "空间ID"}, "ruleName": {"type": "string", "description": "规则名称"}, "templateId": {"type": "string", "description": "模板ID"}, "description": {"type": "string", "description": "描述"}, "status": {"type": "string", "enum": ["enabled", "disabled"], "description": "状态"}, "priority": {"type": "integer", "description": "优先级"}, "layers": {"type": "array", "description": "分层通知"}, "aggrWindow": {"type": "integer", "description": "聚合窗口(秒)"}, "timeFilters": {"type": "array", "description": "时间过滤器"}, "filters": {"type": "array", "description": "过滤器组"}, "updatedBy": {"type": "integer", "description": "更新者ID"}}, "required": ["ruleId"]}, "example": {"ruleId": "rule-001", "spaceId": 1, "ruleName": "关键告警分派策略(更新)", "templateId": "template-001", "description": "处理关键级别告警的分派策略(更新)", "status": "enabled", "priority": 2, "updatedBy": 1}}, "responses": [{"id": 1, "name": "成功响应", "code": 200, "contentType": "application/json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误代码"}, "msg": {"type": "string", "description": "提示信息"}}}, "example": {"code": 0, "msg": "更新成功"}}]}}, {"id": 6, "name": "删除分配策略", "type": "httpApi", "parentId": 1, "serverId": "", "description": "批量删除分配策略", "api": {"id": 6, "method": "DELETE", "path": "/api/v1/assignment-policies", "name": "删除分配策略", "description": "批量删除分配策略", "tags": ["assignment_policy"], "requestBody": {"type": "application/json", "jsonSchema": {"type": "object", "properties": {"ruleIds": {"type": "array", "items": {"type": "string"}, "description": "规则ID列表"}}, "required": ["ruleIds"]}, "example": {"ruleIds": ["rule-001", "rule-002"]}}, "responses": [{"id": 1, "name": "成功响应", "code": 200, "contentType": "application/json", "jsonSchema": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误代码"}, "msg": {"type": "string", "description": "提示信息"}}}, "example": {"code": 0, "msg": "删除成功"}}]}}]}]}