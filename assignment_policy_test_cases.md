# Assignment Policy API 测试用例

## 1. 创建分配策略 (POST)

### 1.1 完整功能测试
```bash
curl -X POST http://localhost:8888/api/v1/assignment-policies \
  -H "Content-Type: application/json" \
  -d '{
    "spaceId": 1,
    "ruleName": "关键告警分派策略",
    "templateId": "template-001",
    "description": "处理关键级别告警的分派策略",
    "status": "enabled",
    "priority": 1,
    "layers": [
      {
        "maxTimes": 3,
        "notifyStep": 1,
        "escalateWindow": 15,
        "forceEscalate": true,
        "target": {
          "teamIds": [1, 2],
          "personIds": [101, 102],
          "scheduleToRoleIds": {
            "schedule-001": [1, 2]
          },
          "by": {
            "followPreference": true,
            "critical": ["email", "sms"],
            "warning": ["email"],
            "info": ["email"]
          },
          "webhooks": [
            {
              "type": "feishu_app",
              "settings": {
                "app_id": "cli_a1b2c3d4e5f6"
              }
            }
          ]
        }
      }
    ],
    "aggrWindow": 300,
    "timeFilters": [
      {
        "start": "09:00",
        "end": "18:00",
        "repeat": [1, 2, 3, 4, 5],
        "calId": "calendar-001",
        "isOff": false
      }
    ],
    "filters": [
      [
        {
          "key": "severity",
          "oper": "IN",
          "vals": ["Critical"]
        }
      ]
    ],
    "updatedBy": 1
  }'
```

### 1.2 简化版测试
```bash
curl -X POST http://localhost:8888/api/v1/assignment-policies \
  -H "Content-Type: application/json" \
  -d '{
    "spaceId": 1,
    "ruleName": "简单告警策略",
    "description": "简单的告警分派策略",
    "status": "enabled",
    "priority": 1,
    "layers": [
      {
        "maxTimes": 2,
        "notifyStep": 1,
        "escalateWindow": 15,
        "forceEscalate": false,
        "target": {
          "personIds": [101],
          "by": {
            "followPreference": true,
            "critical": ["email"],
            "warning": ["email"],
            "info": ["email"]
          },
          "webhooks": []
        }
      }
    ],
    "aggrWindow": 300,
    "filters": [
      [
        {
          "key": "severity",
          "oper": "EQ",
          "vals": ["Critical"]
        }
      ]
    ],
    "updatedBy": 1
  }'
```

### 1.3 高优先级策略测试
```bash
curl -X POST http://localhost:8888/api/v1/assignment-policies \
  -H "Content-Type: application/json" \
  -d '{
    "spaceId": 1,
    "ruleName": "高优先级告警策略",
    "templateId": "template-high-priority",
    "description": "处理高优先级告警，需要立即响应",
    "status": "enabled",
    "priority": 1,
    "layers": [
      {
        "maxTimes": 5,
        "notifyStep": 1,
        "escalateWindow": 5,
        "forceEscalate": true,
        "target": {
          "teamIds": [1],
          "personIds": [101, 102],
          "by": {
            "followPreference": false,
            "critical": ["phone", "sms", "email"],
            "warning": ["phone", "email"],
            "info": ["email"]
          },
          "webhooks": [
            {
              "type": "pagerduty",
              "settings": {
                "integration_key": "pd_integration_key_123",
                "severity": "critical"
              }
            }
          ]
        }
      }
    ],
    "aggrWindow": 60,
    "timeFilters": [],
    "filters": [
      [
        {
          "key": "severity",
          "oper": "IN",
          "vals": ["Critical", "Fatal"]
        },
        {
          "key": "priority",
          "oper": "GTE",
          "vals": ["P0"]
        }
      ]
    ],
    "updatedBy": 1
  }'
```

## 2. 更新分配策略 (PUT)

### 2.1 完整更新测试
```bash
curl -X PUT http://localhost:8888/api/v1/assignment-policies/rule-001 \
  -H "Content-Type: application/json" \
  -d '{
    "ruleId": "rule-001",
    "spaceId": 1,
    "ruleName": "关键告警分派策略(更新版)",
    "templateId": "template-002",
    "description": "处理关键级别告警的分派策略(已更新)",
    "status": "enabled",
    "priority": 2,
    "layers": [
      {
        "maxTimes": 5,
        "notifyStep": 1,
        "escalateWindow": 10,
        "forceEscalate": true,
        "target": {
          "teamIds": [1, 2, 4],
          "personIds": [101, 102, 104, 105],
          "by": {
            "followPreference": true,
            "critical": ["email", "sms", "phone", "wechat"],
            "warning": ["email", "slack", "teams"],
            "info": ["email"]
          },
          "webhooks": [
            {
              "type": "slack",
              "settings": {
                "webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz",
                "channel": "#alerts"
              }
            }
          ]
        }
      }
    ],
    "aggrWindow": 600,
    "updatedBy": 1
  }'
```

### 2.2 部分更新测试
```bash
curl -X PUT http://localhost:8888/api/v1/assignment-policies/rule-001 \
  -H "Content-Type: application/json" \
  -d '{
    "ruleId": "rule-001",
    "ruleName": "更新后的简单策略",
    "description": "更新后的描述",
    "priority": 2,
    "updatedBy": 1
  }'
```

### 2.3 状态更新测试
```bash
curl -X PUT http://localhost:8888/api/v1/assignment-policies/rule-002 \
  -H "Content-Type: application/json" \
  -d '{
    "ruleId": "rule-002",
    "status": "disabled",
    "updatedBy": 1
  }'
```

### 2.4 优先级更新测试
```bash
curl -X PUT http://localhost:8888/api/v1/assignment-policies/rule-003 \
  -H "Content-Type: application/json" \
  -d '{
    "ruleId": "rule-003",
    "priority": 5,
    "updatedBy": 1
  }'
```

## 3. 数据结构说明

### 3.1 过滤器结构 (filters)
- **二维数组结构**: `[][]FilterCondition`
- **外层数组**: 表示多个过滤器组，组间为OR关系
- **内层数组**: 表示单个过滤器组内的条件，条件间为AND关系

**示例**:
```json
"filters": [
  [
    {"key": "severity", "oper": "IN", "vals": ["Critical"]},
    {"key": "service", "oper": "EQ", "vals": ["payment"]}
  ],
  [
    {"key": "environment", "oper": "IN", "vals": ["production"]}
  ]
]
```
**逻辑**: `(severity IN [Critical] AND service = payment) OR (environment IN [production])`

### 3.2 通知层级结构 (layers)
- **多层级升级**: 支持多个通知层级，按顺序执行
- **升级策略**: 可配置升级时间窗口和强制升级
- **通知目标**: 支持团队、个人、排班、Webhook等多种方式

### 3.3 时间过滤器 (timeFilters)
- **时间段控制**: 可配置生效的时间段
- **重复模式**: 支持按星期重复 (1=周一, 7=周日)
- **日历集成**: 可关联日历系统

### 3.4 Webhook配置
支持多种Webhook类型:
- `feishu_app`: 飞书应用
- `dingtalk_robot`: 钉钉机器人
- `slack`: Slack集成
- `pagerduty`: PagerDuty集成
- `teams`: Microsoft Teams

## 4. 错误处理测试

### 4.1 必填字段验证
```bash
# 缺少spaceId
curl -X POST http://localhost:8888/api/v1/assignment-policies \
  -H "Content-Type: application/json" \
  -d '{
    "ruleName": "测试策略",
    "status": "enabled"
  }'
```

### 4.2 无效状态值
```bash
# 无效的status值
curl -X POST http://localhost:8888/api/v1/assignment-policies \
  -H "Content-Type: application/json" \
  -d '{
    "spaceId": 1,
    "ruleName": "测试策略",
    "status": "invalid_status"
  }'
```

### 4.3 不存在的规则ID
```bash
# 更新不存在的规则
curl -X PUT http://localhost:8888/api/v1/assignment-policies/non-existent-rule \
  -H "Content-Type: application/json" \
  -d '{
    "ruleId": "non-existent-rule",
    "ruleName": "不存在的规则"
  }'
```
