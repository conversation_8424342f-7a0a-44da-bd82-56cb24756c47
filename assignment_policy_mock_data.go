package main

import (
	"encoding/json"
	"fmt"
)

// NotificationTarget 通知目标结构
type NotificationTarget struct {
	TeamIds           []int64            `json:"teamIds"`           // 团队ID列表
	PersonIds         []int64            `json:"personIds"`         // 个人ID列表
	ScheduleToRoleIds map[string][]int64 `json:"scheduleToRoleIds"` // 排班角色映射
	By                NotificationMethod `json:"by"`                // 通知方式
	Webhooks          []WebhookConfig    `json:"webhooks"`          // Webhook配置
}

// NotificationMethod 通知方式配置
type NotificationMethod struct {
	FollowPreference bool     `json:"followPreference"` // 遵循偏好设置
	Critical         []string `json:"critical"`         // 严重级别通知方式
	Warning          []string `json:"warning"`          // 警告级别通知方式
	Info             []string `json:"info"`             // 信息级别通知方式
}

// WebhookConfig Webhook配置
type WebhookConfig struct {
	Type     string                 `json:"type"`     // Webhook类型
	Settings map[string]interface{} `json:"settings"` // Webhook设置
}

// NotificationLayer 通知层级
type NotificationLayer struct {
	MaxTimes       int32              `json:"maxTimes"`       // 最大通知次数
	NotifyStep     int32              `json:"notifyStep"`     // 通知步骤
	EscalateWindow int32              `json:"escalateWindow"` // 升级窗口(分钟)
	ForceEscalate  bool               `json:"forceEscalate"`  // 强制升级
	Target         NotificationTarget `json:"target"`         // 通知目标
}

// TimeFilter 时间过滤器
type TimeFilter struct {
	Start  string  `json:"start"`  // 开始时间 HH:MM
	End    string  `json:"end"`    // 结束时间 HH:MM
	Repeat []int32 `json:"repeat"` // 重复日期 (1-7)
	CalId  string  `json:"calId"`  // 日历ID
	IsOff  bool    `json:"isOff"`  // 是否关闭
}

// FilterCondition 过滤条件
type FilterCondition struct {
	Key  string   `json:"key"`  // 字段名
	Oper string   `json:"oper"` // 操作符
	Vals []string `json:"vals"` // 值列表
}

// CreateAssignmentPolicyReq 创建分配策略请求
type CreateAssignmentPolicyReq struct {
	SpaceId     int64               `json:"spaceId"`     // 空间ID
	RuleName    string              `json:"ruleName"`    // 规则名称
	TemplateId  string              `json:"templateId"`  // 模板ID
	Description string              `json:"description"` // 描述
	Status      string              `json:"status"`      // 状态
	Priority    int32               `json:"priority"`    // 优先级
	Layers      []NotificationLayer `json:"layers"`      // 分层通知
	AggrWindow  int32               `json:"aggrWindow"`  // 聚合窗口(秒)
	TimeFilters []TimeFilter        `json:"timeFilters"` // 时间过滤器
	Filters     [][]FilterCondition `json:"filters"`     // 过滤器组
	UpdatedBy   int64               `json:"updatedBy"`   // 创建者ID
}

// UpdateAssignmentPolicyReq 更新分配策略请求
type UpdateAssignmentPolicyReq struct {
	RuleId      string              `json:"ruleId"`      // 规则ID
	SpaceId     int64               `json:"spaceId"`     // 空间ID
	RuleName    string              `json:"ruleName"`    // 规则名称
	TemplateId  string              `json:"templateId"`  // 模板ID
	Description string              `json:"description"` // 描述
	Status      string              `json:"status"`      // 状态
	Priority    int32               `json:"priority"`    // 优先级
	Layers      []NotificationLayer `json:"layers"`      // 分层通知
	AggrWindow  int32               `json:"aggrWindow"`  // 聚合窗口(秒)
	TimeFilters []TimeFilter        `json:"timeFilters"` // 时间过滤器
	Filters     [][]FilterCondition `json:"filters"`     // 过滤器组
	UpdatedBy   int64               `json:"updatedBy"`   // 更新者ID
}

// 生成模拟数据
func generateMockData() {
	// 创建分配策略的模拟数据
	createReq := CreateAssignmentPolicyReq{
		SpaceId:     1,
		RuleName:    "关键告警分派策略",
		TemplateId:  "template-001",
		Description: "处理关键级别告警的分派策略",
		Status:      "enabled",
		Priority:    1,
		Layers: []NotificationLayer{
			{
				MaxTimes:       3,
				NotifyStep:     1,
				EscalateWindow: 15,
				ForceEscalate:  true,
				Target: NotificationTarget{
					TeamIds:   []int64{1, 2},
					PersonIds: []int64{101, 102, 103},
					ScheduleToRoleIds: map[string][]int64{
						"schedule-001": {1, 2},
						"schedule-002": {3, 4},
					},
					By: NotificationMethod{
						FollowPreference: true,
						Critical:         []string{"email", "sms", "phone"},
						Warning:          []string{"email", "slack"},
						Info:             []string{"email"},
					},
					Webhooks: []WebhookConfig{
						{
							Type: "feishu_app",
							Settings: map[string]interface{}{
								"app_id":     "cli_a1b2c3d4e5f6",
								"app_secret": "secret_key_123",
								"chat_id":    "oc_chat_123456",
							},
						},
						{
							Type: "dingtalk_robot",
							Settings: map[string]interface{}{
								"webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=xxx",
								"secret":      "SEC123456789",
							},
						},
					},
				},
			},
			{
				MaxTimes:       2,
				NotifyStep:     2,
				EscalateWindow: 30,
				ForceEscalate:  false,
				Target: NotificationTarget{
					TeamIds:   []int64{3},
					PersonIds: []int64{201, 202},
					ScheduleToRoleIds: map[string][]int64{
						"schedule-003": {5, 6},
					},
					By: NotificationMethod{
						FollowPreference: false,
						Critical:         []string{"phone", "sms"},
						Warning:          []string{"email"},
						Info:             []string{"email"},
					},
					Webhooks: []WebhookConfig{},
				},
			},
		},
		AggrWindow: 300,
		TimeFilters: []TimeFilter{
			{
				Start:  "09:00",
				End:    "18:00",
				Repeat: []int32{1, 2, 3, 4, 5}, // 工作日
				CalId:  "calendar-001",
				IsOff:  false,
			},
			{
				Start:  "00:00",
				End:    "23:59",
				Repeat: []int32{6, 7}, // 周末
				CalId:  "calendar-002",
				IsOff:  true, // 周末关闭
			},
		},
		Filters: [][]FilterCondition{
			// 第一个过滤器组 (OR关系)
			{
				{
					Key:  "severity",
					Oper: "IN",
					Vals: []string{"Critical", "High"},
				},
				{
					Key:  "service",
					Oper: "EQ",
					Vals: []string{"payment-service"},
				},
			},
			// 第二个过滤器组 (OR关系)
			{
				{
					Key:  "environment",
					Oper: "IN",
					Vals: []string{"production", "staging"},
				},
			},
		},
		UpdatedBy: 1,
	}

	// 更新分配策略的模拟数据
	updateReq := UpdateAssignmentPolicyReq{
		RuleId:      "rule-001",
		SpaceId:     1,
		RuleName:    "关键告警分派策略(更新版)",
		TemplateId:  "template-002",
		Description: "处理关键级别告警的分派策略(已更新)",
		Status:      "enabled",
		Priority:    2,
		Layers: []NotificationLayer{
			{
				MaxTimes:       5,
				NotifyStep:     1,
				EscalateWindow: 10,
				ForceEscalate:  true,
				Target: NotificationTarget{
					TeamIds:   []int64{1, 2, 4},
					PersonIds: []int64{101, 102, 104, 105},
					ScheduleToRoleIds: map[string][]int64{
						"schedule-001": {1, 2, 7},
						"schedule-004": {8, 9},
					},
					By: NotificationMethod{
						FollowPreference: true,
						Critical:         []string{"email", "sms", "phone", "wechat"},
						Warning:          []string{"email", "slack", "teams"},
						Info:             []string{"email"},
					},
					Webhooks: []WebhookConfig{
						{
							Type: "slack",
							Settings: map[string]interface{}{
								"webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz",
								"channel":     "#alerts",
								"username":    "AlertBot",
							},
						},
					},
				},
			},
		},
		AggrWindow: 600,
		TimeFilters: []TimeFilter{
			{
				Start:  "08:00",
				End:    "20:00",
				Repeat: []int32{1, 2, 3, 4, 5, 6}, // 工作日+周六
				CalId:  "calendar-003",
				IsOff:  false,
			},
		},
		Filters: [][]FilterCondition{
			{
				{
					Key:  "severity",
					Oper: "IN",
					Vals: []string{"Critical"},
				},
				{
					Key:  "tags.team",
					Oper: "EQ",
					Vals: []string{"backend"},
				},
			},
		},
		UpdatedBy: 1,
	}

	// 输出JSON格式的模拟数据
	fmt.Println("=== 创建分配策略 POST 请求模拟数据 ===")
	createJSON, _ := json.MarshalIndent(createReq, "", "  ")
	fmt.Println(string(createJSON))

	fmt.Println("\n=== 更新分配策略 PUT 请求模拟数据 ===")
	updateJSON, _ := json.MarshalIndent(updateReq, "", "  ")
	fmt.Println(string(updateJSON))
}

// 生成简化版模拟数据
func generateSimpleMockData() {
	// 简化的创建请求
	simpleCreateReq := CreateAssignmentPolicyReq{
		SpaceId:     1,
		RuleName:    "简单告警策略",
		Description: "简单的告警分派策略",
		Status:      "enabled",
		Priority:    1,
		Layers: []NotificationLayer{
			{
				MaxTimes:       2,
				NotifyStep:     1,
				EscalateWindow: 15,
				ForceEscalate:  false,
				Target: NotificationTarget{
					PersonIds: []int64{101},
					By: NotificationMethod{
						FollowPreference: true,
						Critical:         []string{"email"},
						Warning:          []string{"email"},
						Info:             []string{"email"},
					},
					Webhooks: []WebhookConfig{},
				},
			},
		},
		AggrWindow: 300,
		Filters: [][]FilterCondition{
			{
				{
					Key:  "severity",
					Oper: "EQ",
					Vals: []string{"Critical"},
				},
			},
		},
		UpdatedBy: 1,
	}

	// 简化的更新请求
	simpleUpdateReq := UpdateAssignmentPolicyReq{
		RuleId:      "rule-001",
		RuleName:    "更新后的简单策略",
		Description: "更新后的描述",
		Priority:    2,
		UpdatedBy:   1,
	}

	fmt.Println("\n=== 简化版创建分配策略 POST 请求 ===")
	simpleCreateJSON, _ := json.MarshalIndent(simpleCreateReq, "", "  ")
	fmt.Println(string(simpleCreateJSON))

	fmt.Println("\n=== 简化版更新分配策略 PUT 请求 ===")
	simpleUpdateJSON, _ := json.MarshalIndent(simpleUpdateReq, "", "  ")
	fmt.Println(string(simpleUpdateJSON))
}

// 生成不同场景的模拟数据
func generateScenarioMockData() {
	// 场景1: 高优先级告警策略
	highPriorityReq := CreateAssignmentPolicyReq{
		SpaceId:     1,
		RuleName:    "高优先级告警策略",
		TemplateId:  "template-high-priority",
		Description: "处理高优先级告警，需要立即响应",
		Status:      "enabled",
		Priority:    1,
		Layers: []NotificationLayer{
			{
				MaxTimes:       5,
				NotifyStep:     1,
				EscalateWindow: 5, // 5分钟快速升级
				ForceEscalate:  true,
				Target: NotificationTarget{
					TeamIds:   []int64{1},        // 核心团队
					PersonIds: []int64{101, 102}, // 主要负责人
					By: NotificationMethod{
						FollowPreference: false, // 不遵循个人偏好
						Critical:         []string{"phone", "sms", "email"},
						Warning:          []string{"phone", "email"},
						Info:             []string{"email"},
					},
					Webhooks: []WebhookConfig{
						{
							Type: "pagerduty",
							Settings: map[string]interface{}{
								"integration_key": "pd_integration_key_123",
								"severity":        "critical",
							},
						},
					},
				},
			},
		},
		AggrWindow:  60,             // 1分钟聚合窗口
		TimeFilters: []TimeFilter{}, // 24/7 无时间限制
		Filters: [][]FilterCondition{
			{
				{
					Key:  "severity",
					Oper: "IN",
					Vals: []string{"Critical", "Fatal"},
				},
				{
					Key:  "priority",
					Oper: "GTE",
					Vals: []string{"P0"},
				},
			},
		},
		UpdatedBy: 1,
	}

	// 场景2: 工作时间告警策略
	workHoursReq := CreateAssignmentPolicyReq{
		SpaceId:     2,
		RuleName:    "工作时间告警策略",
		TemplateId:  "template-work-hours",
		Description: "仅在工作时间处理的告警策略",
		Status:      "enabled",
		Priority:    3,
		Layers: []NotificationLayer{
			{
				MaxTimes:       3,
				NotifyStep:     1,
				EscalateWindow: 30,
				ForceEscalate:  false,
				Target: NotificationTarget{
					TeamIds: []int64{2, 3},
					By: NotificationMethod{
						FollowPreference: true,
						Critical:         []string{"email", "slack"},
						Warning:          []string{"email"},
						Info:             []string{"email"},
					},
					Webhooks: []WebhookConfig{
						{
							Type: "slack",
							Settings: map[string]interface{}{
								"webhook_url": "https://hooks.slack.com/services/work/hours/channel",
								"channel":     "#work-alerts",
							},
						},
					},
				},
			},
		},
		AggrWindow: 900, // 15分钟聚合窗口
		TimeFilters: []TimeFilter{
			{
				Start:  "09:00",
				End:    "18:00",
				Repeat: []int32{1, 2, 3, 4, 5}, // 仅工作日
				CalId:  "work-calendar",
				IsOff:  false,
			},
		},
		Filters: [][]FilterCondition{
			{
				{
					Key:  "severity",
					Oper: "IN",
					Vals: []string{"Warning", "Info"},
				},
			},
		},
		UpdatedBy: 2,
	}

	fmt.Println("\n=== 场景1: 高优先级告警策略 ===")
	highPriorityJSON, _ := json.MarshalIndent(highPriorityReq, "", "  ")
	fmt.Println(string(highPriorityJSON))

	fmt.Println("\n=== 场景2: 工作时间告警策略 ===")
	workHoursJSON, _ := json.MarshalIndent(workHoursReq, "", "  ")
	fmt.Println(string(workHoursJSON))
}

func main() {
	generateMockData()
	generateSimpleMockData()
	generateScenarioMockData()
}
