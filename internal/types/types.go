// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5

package types

type AlertRouteInfo struct {
	BaseIDInfo
	Trans         *string       `json:"trans,optional"`
	IntegrationId *string       `json:"integrationId,optional"`
	Cases         *[]RouteCase  `json:"cases,optional"`
	Default       *RouteDefault `json:"default,optional"`
	Status        *string       `json:"status,optional" validate:"omitempty,oneof=enabled disabled"`
	Version       *int32        `json:"version,optional"`
	UpdatedBy     *int64        `json:"updatedBy,optional"`
	CreatorId     *int64        `json:"creatorId,optional"`
}

type AlertRouteInfoResp struct {
	BaseDataInfo
	Data AlertRouteInfo `json:"data"`
}

type AlertRouteListInfo struct {
	BaseListInfo
	Data []AlertRouteInfo `json:"data"`
}

type AlertRouteListReq struct {
	PageInfo
	IntegrationId *string `form:"integrationId,optional"`
	Status        *string `form:"status,optional"`
}

type AlertRouteListResp struct {
	BaseDataInfo
	Data AlertRouteListInfo `json:"data"`
}

type AssignmentPolicyInfo struct {
	BaseUUIDInfo
	SpaceId     *int64               `json:"spaceId,optional"`
	RuleId      *string              `json:"ruleId,optional"`
	RuleName    *string              `json:"ruleName,optional" validate:"omitempty,max=100"`
	TemplateId  *string              `json:"templateId,optional"`
	Description *string              `json:"description,optional"`
	Status      *string              `json:"status,optional" validate:"omitempty,oneof=enabled disabled"`
	Priority    *int32               `json:"priority,optional"`
	UpdatedBy   *int64               `json:"updatedBy,optional"`
	Layers      *[]NotificationLayer `json:"layers,optional"`
	AggrWindow  *int32               `json:"aggrWindow,optional"`
	TimeFilters *[]TimeFilter        `json:"timeFilters,optional"`
	Filters     *[][]FilterCondition `json:"filters,optional"`
}

type AssignmentPolicyInfoResp struct {
	BaseDataInfo
	Data AssignmentPolicyInfo `json:"data"`
}

type AssignmentPolicyListInfo struct {
	BaseListInfo
	Data []AssignmentPolicyInfo `json:"data"`
}

type AssignmentPolicyListReq struct {
	PageInfo
	SpaceId *int64  `form:"spaceId,optional"`
	Status  *string `form:"status,optional"`
}

type AssignmentPolicyListResp struct {
	BaseDataInfo
	Data AssignmentPolicyListInfo `json:"data"`
}

type BaseDataInfo struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data string `json:"data,omitempty"`
}

type BaseDataResp struct {
	BaseDataInfo
}

type BaseIDInfo struct {
	Id        *uint64 `json:"id,optional"`
	CreatedAt *int64  `json:"createdAt,optional"`
	UpdatedAt *int64  `json:"updatedAt,optional"`
}

type BaseListInfo struct {
	Total uint64 `json:"total"`
	Data  string `json:"data,omitempty"`
}

type BaseListResp struct {
	Code  int    `json:"code"`
	Msg   string `json:"msg"`
	Total uint64 `json:"total"`
	Data  string `json:"data,omitempty"`
}

type BaseMsgResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type BaseUUIDInfo struct {
	Id        *string `json:"id,optional"`
	CreatedAt *int64  `json:"createdAt,optional"`
	UpdatedAt *int64  `json:"updatedAt,optional"`
}

type CreateAlertRouteReq struct {
	IntegrationId string       `json:"integrationId" validate:"required"`
	Cases         []RouteCase  `json:"cases"`
	Default       RouteDefault `json:"default"`
	Status        string       `json:"status" validate:"required,oneof=enabled disabled"`
	CreatorId     int64        `json:"creatorId"`
}

type CreateAssignmentPolicyReq struct {
	SpaceId     int64               `json:"spaceId" validate:"required"`
	RuleName    string              `json:"ruleName" validate:"required"`
	TemplateId  string              `json:"templateId"`
	Description string              `json:"description"`
	Status      string              `json:"status" validate:"required,oneof=enabled disabled"`
	Priority    int32               `json:"priority"`
	Layers      []NotificationLayer `json:"layers"`
	AggrWindow  int32               `json:"aggrWindow"`
	TimeFilters []TimeFilter        `json:"timeFilters"`
	Filters     [][]FilterCondition `json:"filters"`
	UpdatedBy   int64               `json:"updatedBy"`
}

type CreateIntegrationReq struct {
	ID     string `json:"id,optional"`
	Name   string `json:"name,validate:\"required,max=50\""`
	Kind   string `json:"kind,default=private,validate:\"oneof=private public\""`
	Type   string `json:"type,validate:\"required\""`
	Status bool   `json:"status,default=true"`
}

type CreateSpaceReq struct {
	Name        string `json:"name" validate:"required"`
	TeamId      int64  `json:"teamId"`
	TeamName    string `json:"teamName"`
	Description string `json:"description"`
	Visibility  string `json:"visibility" validate:"required,oneof=private public"`
	Enabled     bool   `json:"enabled"`
}

type DataSourceInfo struct {
	ID       string `json:"id"`
	TypeName string `json:"type_name"`
	Type     string `json:"type"`
	URL      string `json:"url"`
	Logo     string `json:"logo"`
	Sort     int64  `json:"sort"`
}

type DeleteAlertRouteReq struct {
	IDs []string `json:"ids" validate:"required,min=1"`
}

type DeleteAssignmentPolicyReq struct {
	RuleIds []string `json:"ruleIds" validate:"required,min=1"`
}

type DeleteIntegrationReq struct {
	IDs []string `json:"ids" validate:"required,min=1"`
}

type DeleteSpaceReq struct {
	IDs []uint64 `json:"ids" validate:"required,min=1"`
}

type FilterCondition struct {
	Key  string   `json:"key"`
	Oper string   `json:"oper"`
	Vals []string `json:"vals"`
}

type IDPathReq struct {
	Id uint64 `path:"id"`
}

type IDReq struct {
	Id uint64 `json:"id" validate:"number"`
}

type IDsReq struct {
	Ids []uint64 `json:"ids"`
}

type IntegrationInfo struct {
	BaseUUIDInfo
	Trans         *string         `json:"trans,optional"`
	Name          *string         `json:"name,optional" validate:"omitempty,max=50"`
	DataSource    *DataSourceInfo `json:"dataSource,optional"`
	Kind          *string         `json:"kind,optional" validate:"omitempty,oneof=private public"`
	Type          *string         `json:"type,optional"`
	Status        *bool           `json:"status,optional"`
	LastEventTime *string         `json:"lastEventTime,optional"`
	Webhook       *string         `json:"webhook,optional"`
}

type IntegrationInfoResp struct {
	BaseDataInfo
	Data IntegrationInfo `json:"data"`
}

type IntegrationListInfo struct {
	BaseListInfo
	Data []IntegrationInfo `json:"data"`
}

type IntegrationListReq struct {
	PageInfo
	Name *string `form:"name,optional"`
	Kind *string `form:"kind,optional"`
	Type *string `form:"type,optional"`
}

type IntegrationListResp struct {
	BaseDataInfo
	Data IntegrationListInfo `json:"data"`
}

type IssueStats struct {
	Processing int32 `json:"processing"` // 处理中
	Pending    int32 `json:"pending"`    // 待处理
	Completed  int32 `json:"completed"`  // 已处理
}

type NotificationBy struct {
	FollowPreference bool     `json:"followPreference"`
	Critical         []string `json:"critical"`
	Warning          []string `json:"warning"`
	Info             []string `json:"info"`
}

type NotificationLayer struct {
	MaxTimes       int32              `json:"maxTimes"`
	NotifyStep     int32              `json:"notifyStep"`
	EscalateWindow int32              `json:"escalateWindow"`
	ForceEscalate  bool               `json:"forceEscalate"`
	Target         NotificationTarget `json:"target"`
}

type NotificationTarget struct {
	TeamIds           []int64            `json:"teamIds"`
	PersonIds         []string           `json:"personIds"`
	ScheduleToRoleIds map[string][]int64 `json:"scheduleToRoleIds"`
	By                NotificationBy     `json:"by"`
	Webhooks          []Webhook          `json:"webhooks"`
}

type PageInfo struct {
	Page     uint64 `json:"page" validate:"required,number,gt=0"`
	PageSize uint64 `json:"pageSize" validate:"required,number,lt=100000"`
}

type RouteCase struct {
	If          []RouteCondition `json:"if"`
	ChannelIds  []int64          `json:"channelIds"`
	Fallthrough bool             `json:"fallthrough"`
}

type RouteCondition struct {
	Key  string   `json:"key"`
	Oper string   `json:"oper"`
	Vals []string `json:"vals"`
}

type RouteDefault struct {
	ChannelIds []int64 `json:"channelIds"`
}

type RuleIDPathReq struct {
	RuleId string `path:"ruleId"`
}

type SpaceInfo struct {
	BaseIDInfo
	Trans           *string     `json:"trans,optional"`
	Name            *string     `json:"name,optional" validate:"omitempty,max=50"`
	TeamId          *int64      `json:"teamId,optional"`
	TeamName        *string     `json:"teamName,optional"`
	Description     *string     `json:"description,optional"`
	Visibility      *string     `json:"visibility,optional" validate:"omitempty,oneof=private public"`
	IssueStats      *IssueStats `json:"issueStats,optional"`
	Enabled         *bool       `json:"enabled,optional"`
	StatusChangedAt *string     `json:"statusChangedAt,optional"`
}

type SpaceInfoResp struct {
	BaseDataInfo
	Data SpaceInfo `json:"data"`
}

type SpaceListInfo struct {
	BaseListInfo
	Data []SpaceInfo `json:"data"`
}

type SpaceListReq struct {
	PageInfo
	Name *string `form:"name,optional"`
}

type SpaceListResp struct {
	BaseDataInfo
	Data SpaceListInfo `json:"data"`
}

type TimeFilter struct {
	Start  string  `json:"start"`
	End    string  `json:"end"`
	Repeat []int32 `json:"repeat"`
	CalId  string  `json:"calId"`
	IsOff  bool    `json:"isOff"`
}

type UUIDReq struct {
	Id string `path:"id" validate:"len=36"`
}

type UUIDsReq struct {
	Ids []string `json:"ids"`
}

type UpdateAlertRouteReq struct {
	ID            string       `json:"id" validate:"required"`
	IntegrationId string       `json:"integrationId"`
	Cases         []RouteCase  `json:"cases"`
	Default       RouteDefault `json:"default"`
	Status        string       `json:"status" validate:"oneof=enabled disabled"`
	UpdatedBy     int64        `json:"updatedBy"`
	Version       int32        `json:"version"`
}

type UpdateAssignmentPolicyReq struct {
	RuleId      string              `json:"ruleId" validate:"required"`
	SpaceId     int64               `json:"spaceId"`
	RuleName    string              `json:"ruleName"`
	TemplateId  string              `json:"templateId"`
	Description string              `json:"description"`
	Status      string              `json:"status" validate:"oneof=enabled disabled"`
	Priority    int32               `json:"priority"`
	Layers      []NotificationLayer `json:"layers"`
	AggrWindow  int32               `json:"aggrWindow"`
	TimeFilters []TimeFilter        `json:"timeFilters"`
	Filters     [][]FilterCondition `json:"filters"`
	UpdatedBy   int64               `json:"updatedBy"`
}

type UpdateIntegrationReq struct {
	ID     string `json:"id,validate:\"required\""`
	Name   string `json:"name,validate:\"required,max=50\""`
	Kind   string `json:"kind,validate:\"required,oneof=private public\""`
	Type   string `json:"type,validate:\"required\""`
	Status bool   `json:"status,optional"`
}

type UpdateSpaceReq struct {
	ID          string `json:"id" validate:"required"`
	Name        string `json:"name"`
	TeamId      int64  `json:"teamId"`
	TeamName    string `json:"teamName"`
	Description string `json:"description"`
	Visibility  string `json:"visibility" validate:"oneof=private public"`
	Enabled     bool   `json:"enabled"`
}

type Webhook struct {
	Type     string            `json:"type"`
	Settings map[string]string `json:"settings"`
}
