package assignment_policy

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAssignmentPolicyListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取分配策略列表
func NewGetAssignmentPolicyListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAssignmentPolicyListLogic {
	return &GetAssignmentPolicyListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAssignmentPolicyListLogic) GetAssignmentPolicyList(req *types.AssignmentPolicyListReq) (resp *types.AssignmentPolicyListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
