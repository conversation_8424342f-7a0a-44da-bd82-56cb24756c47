package assignment_policy

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteAssignmentPolicyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除分配策略
func NewDeleteAssignmentPolicyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAssignmentPolicyLogic {
	return &DeleteAssignmentPolicyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAssignmentPolicyLogic) DeleteAssignmentPolicy(req *types.DeleteAssignmentPolicyReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
