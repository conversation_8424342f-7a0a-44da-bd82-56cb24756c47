package assignment_policy

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateAssignmentPolicyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建分配策略
func NewCreateAssignmentPolicyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAssignmentPolicyLogic {
	return &CreateAssignmentPolicyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAssignmentPolicyLogic) CreateAssignmentPolicy(req *types.CreateAssignmentPolicyReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
