package assignment_policy

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateAssignmentPolicyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新分配策略
func NewUpdateAssignmentPolicyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateAssignmentPolicyLogic {
	return &UpdateAssignmentPolicyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateAssignmentPolicyLogic) UpdateAssignmentPolicy(req *types.UpdateAssignmentPolicyReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
