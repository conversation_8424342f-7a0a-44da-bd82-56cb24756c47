package assignment_policy

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAssignmentPolicyByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取分配策略详情
func NewGetAssignmentPolicyByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAssignmentPolicyByIdLogic {
	return &GetAssignmentPolicyByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAssignmentPolicyByIdLogic) GetAssignmentPolicyById(req *types.RuleIDPathReq) (resp *types.AssignmentPolicyInfoResp, err error) {
	// todo: add your logic here and delete this line

	return
}
