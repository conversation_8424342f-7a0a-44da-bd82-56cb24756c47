package integration

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetIntegrationKeyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取Integration密钥
func NewGetIntegrationKeyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetIntegrationKeyLogic {
	return &GetIntegrationKeyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetIntegrationKeyLogic) GetIntegrationKey() (resp *types.BaseUUIDInfo, err error) {
	// todo: add your logic here and delete this line

	return
}
