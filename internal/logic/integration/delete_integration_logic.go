package integration

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteIntegrationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除Integration
func NewDeleteIntegrationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteIntegrationLogic {
	return &DeleteIntegrationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteIntegrationLogic) DeleteIntegration(req *types.DeleteIntegrationReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
