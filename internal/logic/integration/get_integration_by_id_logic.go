package integration

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetIntegrationByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取Integration详情
func NewGetIntegrationByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetIntegrationByIdLogic {
	return &GetIntegrationByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetIntegrationByIdLogic) GetIntegrationById() (resp *types.IntegrationInfoResp, err error) {
	// todo: add your logic here and delete this line

	return
}
