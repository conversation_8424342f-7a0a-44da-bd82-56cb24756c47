package integration

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateIntegrationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新Integration
func NewUpdateIntegrationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateIntegrationLogic {
	return &UpdateIntegrationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateIntegrationLogic) UpdateIntegration(req *types.UpdateIntegrationReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
