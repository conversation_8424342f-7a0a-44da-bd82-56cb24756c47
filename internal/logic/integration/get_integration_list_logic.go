package integration

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetIntegrationListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取Integration列表
func NewGetIntegrationListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetIntegrationListLogic {
	return &GetIntegrationListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetIntegrationListLogic) GetIntegrationList(req *types.IntegrationListReq) (resp *types.IntegrationListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
