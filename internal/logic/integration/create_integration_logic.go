package integration

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateIntegrationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建Integration
func NewCreateIntegrationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateIntegrationLogic {
	return &CreateIntegrationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateIntegrationLogic) CreateIntegration(req *types.CreateIntegrationReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
