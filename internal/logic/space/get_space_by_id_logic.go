package space

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSpaceByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取空间详情
func NewGetSpaceByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSpaceByIdLogic {
	return &GetSpaceByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSpaceByIdLogic) GetSpaceById() (resp *types.SpaceInfoResp, err error) {
	// todo: add your logic here and delete this line

	return
}
