package space

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSpaceListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取空间列表
func NewGetSpaceListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSpaceListLogic {
	return &GetSpaceListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSpaceListLogic) GetSpaceList(req *types.SpaceListReq) (resp *types.SpaceListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
