package alert_route

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteAlertRouteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除告警路由
func NewDeleteAlertRouteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAlertRouteLogic {
	return &DeleteAlertRouteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteAlertRouteLogic) DeleteAlertRoute(req *types.DeleteAlertRouteReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
