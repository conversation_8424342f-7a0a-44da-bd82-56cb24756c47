package alert_route

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAlertRouteByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取告警路由详情
func NewGetAlertRouteByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAlertRouteByIdLogic {
	return &GetAlertRouteByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAlertRouteByIdLogic) GetAlertRouteById(req *types.IDPathReq) (resp *types.AlertRouteInfoResp, err error) {
	// todo: add your logic here and delete this line

	return
}
