package alert_route

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateAlertRouteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建告警路由
func NewCreateAlertRouteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAlertRouteLogic {
	return &CreateAlertRouteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAlertRouteLogic) CreateAlertRoute(req *types.CreateAlertRouteReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
