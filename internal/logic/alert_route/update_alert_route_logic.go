package alert_route

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateAlertRouteLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新告警路由
func NewUpdateAlertRouteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateAlertRouteLogic {
	return &UpdateAlertRouteLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateAlertRouteLogic) UpdateAlertRoute(req *types.UpdateAlertRouteReq) (resp *types.BaseMsgResp, err error) {
	// todo: add your logic here and delete this line

	return
}
