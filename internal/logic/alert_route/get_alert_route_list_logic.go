package alert_route

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAlertRouteListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取告警路由列表
func NewGetAlertRouteListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAlertRouteListLogic {
	return &GetAlertRouteListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAlertRouteListLogic) GetAlertRouteList(req *types.AlertRouteListReq) (resp *types.AlertRouteListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
