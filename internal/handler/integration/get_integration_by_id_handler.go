package integration

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/logic/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
)

// 获取Integration详情
func GetIntegrationByIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := integration.NewGetIntegrationByIdLogic(r.Context(), svcCtx)
		resp, err := l.GetIntegrationById()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
