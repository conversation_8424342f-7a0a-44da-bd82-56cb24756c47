package integration

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/logic/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
)

// 获取Integration密钥
func GetIntegrationKeyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := integration.NewGetIntegrationKeyLogic(r.Context(), svcCtx)
		resp, err := l.GetIntegrationKey()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
