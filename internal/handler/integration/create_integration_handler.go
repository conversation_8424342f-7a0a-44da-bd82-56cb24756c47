package integration

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/logic/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"
)

// 创建Integration
func CreateIntegrationHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateIntegrationReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := integration.NewCreateIntegrationLogic(r.Context(), svcCtx)
		resp, err := l.CreateIntegration(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
