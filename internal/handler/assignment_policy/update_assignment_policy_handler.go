package assignment_policy

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/logic/assignment_policy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"
)

// 更新分配策略
func UpdateAssignmentPolicyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateAssignmentPolicyReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := assignment_policy.NewUpdateAssignmentPolicyLogic(r.Context(), svcCtx)
		resp, err := l.UpdateAssignmentPolicy(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
