package space

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/logic/space"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
)

// 获取空间详情
func GetSpaceByIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := space.NewGetSpaceByIdLogic(r.Context(), svcCtx)
		resp, err := l.GetSpaceById()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
