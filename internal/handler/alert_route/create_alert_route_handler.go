package alert_route

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/logic/alert_route"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/types"
)

// 创建告警路由
func CreateAlertRouteHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateAlertRouteReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := alert_route.NewCreateAlertRouteLogic(r.Context(), svcCtx)
		resp, err := l.CreateAlertRoute(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
