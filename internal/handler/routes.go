// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5

package handler

import (
	"net/http"

	alert_route "gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/handler/alert_route"
	assignment_policy "gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/handler/assignment_policy"
	integration "gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/handler/integration"
	space "gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/handler/space"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				// 创建告警路由
				Method:  http.MethodPost,
				Path:    "/",
				Handler: alert_route.CreateAlertRouteHandler(serverCtx),
			},
			{
				// 删除告警路由
				Method:  http.MethodDelete,
				Path:    "/",
				Handler: alert_route.DeleteAlertRouteHandler(serverCtx),
			},
			{
				// 获取告警路由详情
				Method:  http.MethodGet,
				Path:    "/:id",
				Handler: alert_route.GetAlertRouteByIdHandler(serverCtx),
			},
			{
				// 更新告警路由
				Method:  http.MethodPut,
				Path:    "/:id",
				Handler: alert_route.UpdateAlertRouteHandler(serverCtx),
			},
			{
				// 获取告警路由列表
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: alert_route.GetAlertRouteListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/alert-routes"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建分配策略
				Method:  http.MethodPost,
				Path:    "/",
				Handler: assignment_policy.CreateAssignmentPolicyHandler(serverCtx),
			},
			{
				// 删除分配策略
				Method:  http.MethodDelete,
				Path:    "/",
				Handler: assignment_policy.DeleteAssignmentPolicyHandler(serverCtx),
			},
			{
				// 获取分配策略详情
				Method:  http.MethodGet,
				Path:    "/:ruleId",
				Handler: assignment_policy.GetAssignmentPolicyByIdHandler(serverCtx),
			},
			{
				// 更新分配策略
				Method:  http.MethodPut,
				Path:    "/:ruleId",
				Handler: assignment_policy.UpdateAssignmentPolicyHandler(serverCtx),
			},
			{
				// 获取分配策略列表
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: assignment_policy.GetAssignmentPolicyListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/assignment-policies"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建Integration
				Method:  http.MethodPost,
				Path:    "/",
				Handler: integration.CreateIntegrationHandler(serverCtx),
			},
			{
				// 删除Integration
				Method:  http.MethodDelete,
				Path:    "/",
				Handler: integration.DeleteIntegrationHandler(serverCtx),
			},
			{
				// 获取Integration详情
				Method:  http.MethodGet,
				Path:    "/:id",
				Handler: integration.GetIntegrationByIdHandler(serverCtx),
			},
			{
				// 更新Integration
				Method:  http.MethodPut,
				Path:    "/:id",
				Handler: integration.UpdateIntegrationHandler(serverCtx),
			},
			{
				// 获取Integration密钥
				Method:  http.MethodGet,
				Path:    "/key",
				Handler: integration.GetIntegrationKeyHandler(serverCtx),
			},
			{
				// 获取Integration列表
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: integration.GetIntegrationListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/integrations"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建空间
				Method:  http.MethodPost,
				Path:    "/",
				Handler: space.CreateSpaceHandler(serverCtx),
			},
			{
				// 删除空间
				Method:  http.MethodDelete,
				Path:    "/",
				Handler: space.DeleteSpaceHandler(serverCtx),
			},
			{
				// 获取空间详情
				Method:  http.MethodGet,
				Path:    "/:id",
				Handler: space.GetSpaceByIdHandler(serverCtx),
			},
			{
				// 更新空间
				Method:  http.MethodPut,
				Path:    "/:id",
				Handler: space.UpdateSpaceHandler(serverCtx),
			},
			{
				// 获取空间列表
				Method:  http.MethodPost,
				Path:    "/list",
				Handler: space.GetSpaceListHandler(serverCtx),
			},
		},
		rest.WithPrefix("/api/v1/spaces"),
	)
}
