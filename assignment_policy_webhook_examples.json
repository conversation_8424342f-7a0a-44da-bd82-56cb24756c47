{"Webhook类型示例": {"飞书应用": {"type": "feishu_app", "settings": {"app_id": "cli_a1b2c3d4e5f6", "app_secret": "secret_key_123456", "chat_id": "oc_chat_123456789", "user_id": "ou_user_123456", "message_type": "interactive"}}, "钉钉机器人": {"type": "dingtalk_robot", "settings": {"webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=xxx", "secret": "SEC123456789abcdef", "at_mobiles": ["13800138000"], "at_all": false, "message_type": "markdown"}}, "Slack": {"type": "slack", "settings": {"webhook_url": "*****************************************************************************", "channel": "#alerts", "username": "AlertBot", "icon_emoji": ":warning:", "color": "danger"}}, "Microsoft Teams": {"type": "teams", "settings": {"webhook_url": "https://outlook.office.com/webhook/xxx/IncomingWebhook/yyy", "title": "Alert Notification", "theme_color": "FF0000", "summary": "<PERSON><PERSON>"}}, "PagerDuty": {"type": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"integration_key": "pd_integration_key_123456789", "severity": "critical", "source": "monitoring-system", "component": "payment-service", "group": "backend-team"}}, "企业微信": {"type": "wechat_work", "settings": {"webhook_url": "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx", "msgtype": "markdown", "mentioned_list": ["@all"], "mentioned_mobile_list": ["13800138000"]}}, "自定义Webhook": {"type": "webhook_custom", "settings": {"url": "https://api.example.com/webhook/alerts", "method": "POST", "headers": {"Authorization": "Bearer token123456", "Content-Type": "application/json", "X-API-Key": "api_key_123"}, "timeout": 30, "retry_count": 3}}, "邮件通知": {"type": "email_smtp", "settings": {"smtp_server": "smtp.example.com", "smtp_port": 587, "username": "<EMAIL>", "password": "smtp_password", "from": "<EMAIL>", "to": ["<EMAIL>", "<EMAIL>"], "subject_template": "[ALERT] {{severity}} - {{service}}", "use_tls": true}}}, "包含多种Webhook的完整策略": {"spaceId": 1, "ruleName": "多Webhook集成策略", "templateId": "template-multi-webhook", "description": "集成多种Webhook通知方式的策略", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 3, "notifyStep": 1, "escalateWindow": 15, "forceEscalate": true, "target": {"teamIds": [1], "personIds": [101, 102], "by": {"followPreference": true, "critical": ["email", "sms"], "warning": ["email"], "info": ["email"]}, "webhooks": [{"type": "slack", "settings": {"webhook_url": "https://hooks.slack.com/services/xxx", "channel": "#critical-alerts", "username": "CriticalAlertBot", "color": "danger"}}, {"type": "feishu_app", "settings": {"app_id": "cli_critical_app", "app_secret": "critical_secret", "chat_id": "oc_critical_chat"}}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"integration_key": "pd_critical_key", "severity": "critical"}}]}}, {"maxTimes": 2, "notifyStep": 2, "escalateWindow": 30, "forceEscalate": false, "target": {"teamIds": [2], "personIds": [201], "by": {"followPreference": false, "critical": ["phone", "email"], "warning": ["email"], "info": ["email"]}, "webhooks": [{"type": "teams", "settings": {"webhook_url": "https://outlook.office.com/webhook/escalation", "title": "Escalated <PERSON>", "theme_color": "FF6600"}}, {"type": "dingtalk_robot", "settings": {"webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=escalation", "secret": "SEC_escalation_123", "at_all": true}}]}}], "aggrWindow": 300, "timeFilters": [{"start": "00:00", "end": "23:59", "repeat": [1, 2, 3, 4, 5, 6, 7], "calId": "24x7-calendar", "isOff": false}], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical", "Fatal"]}]], "updatedBy": 1}, "Webhook配置说明": {"feishu_app": {"必填字段": ["app_id", "app_secret", "chat_id"], "可选字段": ["user_id", "message_type"], "说明": "飞书应用机器人，需要在飞书开放平台创建应用"}, "dingtalk_robot": {"必填字段": ["webhook_url"], "可选字段": ["secret", "at_mobiles", "at_all", "message_type"], "说明": "钉钉自定义机器人，支持加签验证"}, "slack": {"必填字段": ["webhook_url"], "可选字段": ["channel", "username", "icon_emoji", "color"], "说明": "Slack Incoming Webhook"}, "teams": {"必填字段": ["webhook_url"], "可选字段": ["title", "theme_color", "summary"], "说明": "Microsoft Teams Incoming Webhook"}, "pagerduty": {"必填字段": ["integration_key"], "可选字段": ["severity", "source", "component", "group"], "说明": "PagerDuty Events API v2"}, "wechat_work": {"必填字段": ["webhook_url"], "可选字段": ["msgtype", "mentioned_list", "mentioned_mobile_list"], "说明": "企业微信群机器人"}, "webhook_custom": {"必填字段": ["url", "method"], "可选字段": ["headers", "timeout", "retry_count"], "说明": "自定义HTTP Webhook"}, "email_smtp": {"必填字段": ["smtp_server", "smtp_port", "username", "password", "from", "to"], "可选字段": ["subject_template", "use_tls"], "说明": "SMTP邮件通知"}}}