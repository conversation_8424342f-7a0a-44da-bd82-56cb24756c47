{"create_requests": {"complete_example": {"spaceId": 1, "ruleName": "关键告警分派策略", "templateId": "template-001", "description": "处理关键级别告警的分派策略", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 3, "notifyStep": 1, "escalateWindow": 15, "forceEscalate": true, "target": {"teamIds": [1, 2], "personIds": [101, 102, 103], "scheduleToRoleIds": {"schedule-001": [1, 2], "schedule-002": [3, 4]}, "by": {"followPreference": true, "critical": ["email", "sms", "phone"], "warning": ["email", "slack"], "info": ["email"]}, "webhooks": [{"type": "feishu_app", "settings": {"app_id": "cli_a1b2c3d4e5f6", "app_secret": "secret_key_123", "chat_id": "oc_chat_123456"}}, {"type": "dingtalk_robot", "settings": {"webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=xxx", "secret": "SEC123456789"}}]}}, {"maxTimes": 2, "notifyStep": 2, "escalateWindow": 30, "forceEscalate": false, "target": {"teamIds": [3], "personIds": [201, 202], "scheduleToRoleIds": {"schedule-003": [5, 6]}, "by": {"followPreference": false, "critical": ["phone", "sms"], "warning": ["email"], "info": ["email"]}, "webhooks": []}}], "aggrWindow": 300, "timeFilters": [{"start": "09:00", "end": "18:00", "repeat": [1, 2, 3, 4, 5], "calId": "calendar-001", "isOff": false}, {"start": "00:00", "end": "23:59", "repeat": [6, 7], "calId": "calendar-002", "isOff": true}], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical", "High"]}, {"key": "service", "oper": "EQ", "vals": ["payment-service"]}], [{"key": "environment", "oper": "IN", "vals": ["production", "staging"]}]], "updatedBy": 1}, "simple_example": {"spaceId": 1, "ruleName": "简单告警策略", "description": "简单的告警分派策略", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 2, "notifyStep": 1, "escalateWindow": 15, "forceEscalate": false, "target": {"personIds": [101], "by": {"followPreference": true, "critical": ["email"], "warning": ["email"], "info": ["email"]}, "webhooks": []}}], "aggrWindow": 300, "filters": [[{"key": "severity", "oper": "EQ", "vals": ["Critical"]}]], "updatedBy": 1}, "high_priority_example": {"spaceId": 1, "ruleName": "高优先级告警策略", "templateId": "template-high-priority", "description": "处理高优先级告警，需要立即响应", "status": "enabled", "priority": 1, "layers": [{"maxTimes": 5, "notifyStep": 1, "escalateWindow": 5, "forceEscalate": true, "target": {"teamIds": [1], "personIds": [101, 102], "by": {"followPreference": false, "critical": ["phone", "sms", "email"], "warning": ["phone", "email"], "info": ["email"]}, "webhooks": [{"type": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"integration_key": "pd_integration_key_123", "severity": "critical"}}]}}], "aggrWindow": 60, "timeFilters": [], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical", "Fatal"]}, {"key": "priority", "oper": "GTE", "vals": ["P0"]}]], "updatedBy": 1}, "work_hours_example": {"spaceId": 2, "ruleName": "工作时间告警策略", "templateId": "template-work-hours", "description": "仅在工作时间处理的告警策略", "status": "enabled", "priority": 3, "layers": [{"maxTimes": 3, "notifyStep": 1, "escalateWindow": 30, "forceEscalate": false, "target": {"teamIds": [2, 3], "by": {"followPreference": true, "critical": ["email", "slack"], "warning": ["email"], "info": ["email"]}, "webhooks": [{"type": "slack", "settings": {"webhook_url": "https://hooks.slack.com/services/work/hours/channel", "channel": "#work-alerts"}}]}}], "aggrWindow": 900, "timeFilters": [{"start": "09:00", "end": "18:00", "repeat": [1, 2, 3, 4, 5], "calId": "work-calendar", "isOff": false}], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Warning", "Info"]}]], "updatedBy": 2}}, "update_requests": {"complete_example": {"ruleId": "rule-001", "spaceId": 1, "ruleName": "关键告警分派策略(更新版)", "templateId": "template-002", "description": "处理关键级别告警的分派策略(已更新)", "status": "enabled", "priority": 2, "layers": [{"maxTimes": 5, "notifyStep": 1, "escalateWindow": 10, "forceEscalate": true, "target": {"teamIds": [1, 2, 4], "personIds": [101, 102, 104, 105], "scheduleToRoleIds": {"schedule-001": [1, 2, 7], "schedule-004": [8, 9]}, "by": {"followPreference": true, "critical": ["email", "sms", "phone", "wechat"], "warning": ["email", "slack", "teams"], "info": ["email"]}, "webhooks": [{"type": "slack", "settings": {"webhook_url": "https://hooks.slack.com/services/xxx/yyy/zzz", "channel": "#alerts", "username": "AlertBot"}}]}}], "aggrWindow": 600, "timeFilters": [{"start": "08:00", "end": "20:00", "repeat": [1, 2, 3, 4, 5, 6], "calId": "calendar-003", "isOff": false}], "filters": [[{"key": "severity", "oper": "IN", "vals": ["Critical"]}, {"key": "tags.team", "oper": "EQ", "vals": ["backend"]}]], "updatedBy": 1}, "simple_example": {"ruleId": "rule-001", "ruleName": "更新后的简单策略", "description": "更新后的描述", "priority": 2, "updatedBy": 1}, "status_only_example": {"ruleId": "rule-002", "status": "disabled", "updatedBy": 1}, "priority_only_example": {"ruleId": "rule-003", "priority": 5, "updatedBy": 1}}}