# Assignment Policy API 测试示例

## 1. 创建分配策略

### 请求
```bash
POST /api/v1/assignment-policies
Content-Type: application/json

{
  "spaceId": 1,
  "ruleName": "关键告警分派策略",
  "templateId": "template-001",
  "description": "处理关键级别告警的分派策略",
  "status": "enabled",
  "priority": 1,
  "layers": [
    {
      "maxTimes": 3,
      "notifyStep": 1,
      "escalateWindow": 15,
      "forceEscalate": true,
      "target": {
        "teamIds": [1],
        "personIds": [101, 102],
        "scheduleToRoleIds": {
          "schedule-001": [1, 2]
        },
        "by": {
          "followPreference": true,
          "critical": ["email", "sms"],
          "warning": ["email"],
          "info": ["email"]
        },
        "webhooks": [
          {
            "type": "feishu_app",
            "settings": {}
          }
        ]
      }
    }
  ],
  "aggrWindow": 300,
  "timeFilters": [
    {
      "start": "09:00",
      "end": "18:00",
      "repeat": [1, 2, 3, 4, 5],
      "calId": "calendar-001",
      "isOff": false
    }
  ],
  "filters": [
    [
      {
        "key": "severity",
        "oper": "IN",
        "vals": ["Critical"]
      }
    ]
  ],
  "updatedBy": 1
}
```

### 响应
```json
{
  "code": 0,
  "msg": "创建分配策略成功"
}
```

## 2. 获取分配策略列表

### 请求
```bash
POST /api/v1/assignment-policies/list
Content-Type: application/json

{
  "page": 1,
  "pageSize": 10,
  "spaceId": 1,
  "status": "enabled"
}
```

### 响应
```json
{
  "code": 0,
  "msg": "获取分配策略列表成功",
  "data": {
    "total": 2,
    "data": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "spaceId": 1,
        "ruleId": "rule-001",
        "ruleName": "关键告警分派策略",
        "templateId": "template-001",
        "description": "处理关键级别告警的分派策略",
        "status": "enabled",
        "priority": 1,
        "createdAt": 1682409000,
        "updatedAt": 1682409000,
        "updatedBy": 1,
        "layers": [...],
        "aggrWindow": 300,
        "timeFilters": [...],
        "filters": [[...]]
      }
    ]
  }
}
```

## 3. 获取分配策略详情

### 请求
```bash
GET /api/v1/assignment-policies/rule-001
```

### 响应
```json
{
  "code": 0,
  "msg": "获取分配策略详情成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "spaceId": 1,
    "ruleId": "rule-001",
    "ruleName": "关键告警分派策略",
    "templateId": "template-001",
    "description": "处理关键级别告警的分派策略",
    "status": "enabled",
    "priority": 1,
    "layers": [
      {
        "maxTimes": 3,
        "notifyStep": 1,
        "escalateWindow": 15,
        "forceEscalate": true,
        "target": {
          "teamIds": [1],
          "personIds": [101, 102],
          "scheduleToRoleIds": {
            "schedule-001": [1, 2]
          },
          "by": {
            "followPreference": true,
            "critical": ["email", "sms"],
            "warning": ["email"],
            "info": ["email"]
          },
          "webhooks": [
            {
              "type": "feishu_app",
              "settings": {}
            }
          ]
        }
      }
    ],
    "aggrWindow": 300,
    "timeFilters": [
      {
        "start": "09:00",
        "end": "18:00",
        "repeat": [1, 2, 3, 4, 5],
        "calId": "calendar-001",
        "isOff": false
      }
    ],
    "filters": [
      [
        {
          "key": "severity",
          "oper": "IN",
          "vals": ["Critical"]
        }
      ]
    ],
    "createdAt": 1682409000,
    "updatedAt": 1682409000,
    "updatedBy": 1
  }
}
```

## 4. 更新分配策略

### 请求
```bash
PUT /api/v1/assignment-policies/rule-001
Content-Type: application/json

{
  "ruleId": "rule-001",
  "spaceId": 1,
  "ruleName": "关键告警分派策略(更新)",
  "templateId": "template-001",
  "description": "处理关键级别告警的分派策略(更新)",
  "status": "enabled",
  "priority": 2,
  "updatedBy": 1
}
```

### 响应
```json
{
  "code": 0,
  "msg": "更新分配策略成功"
}
```

## 5. 删除分配策略

### 请求
```bash
DELETE /api/v1/assignment-policies
Content-Type: application/json

{
  "ruleIds": ["rule-001", "rule-002"]
}
```

### 响应
```json
{
  "code": 0,
  "msg": "成功删除 2 个分配策略"
}
```

## 数据结构说明

### 过滤器结构 (filters)
- 采用二维数组结构：`[][]FilterCondition`
- 外层数组表示多个过滤器组（OR关系）
- 内层数组表示单个过滤器组内的条件（AND关系）

### 通知层级结构 (layers)
- 支持多层级通知升级
- 每层包含通知目标、次数、时间窗口等配置
- 支持团队、个人、排班、Webhook等多种通知方式

### 时间过滤器结构 (timeFilters)
- 支持按时间段过滤告警
- 可配置工作日、节假日等不同时间策略
