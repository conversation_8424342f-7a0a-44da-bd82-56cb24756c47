// Code generated by ent, DO NOT EDIT.

package ent

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/space"
)

// AssignmentPolicy is the model entity for the AssignmentPolicy schema.
type AssignmentPolicy struct {
	config `json:"-"`
	// ID of the ent.
	// UUID
	ID uuid.UUID `json:"id,omitempty"`
	// Create Time | 创建日期
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Update Time | 修改日期
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 规则ID
	RuleID string `json:"rule_id,omitempty"`
	// 规则名称
	RuleName string `json:"rule_name,omitempty"`
	// 模板ID
	TemplateID string `json:"template_id,omitempty"`
	// 描述
	Description string `json:"description,omitempty"`
	// 状态
	Status assignmentpolicy.Status `json:"status,omitempty"`
	// 优先级
	Priority int32 `json:"priority,omitempty"`
	// 分层通知
	Layers []map[string]interface{} `json:"layers,omitempty"`
	// 聚合窗口(秒)
	AggrWindow int32 `json:"aggr_window,omitempty"`
	// 时间过滤器
	TimeFilters []map[string]interface{} `json:"time_filters,omitempty"`
	// 过滤器组
	Filters []map[string]interface{} `json:"filters,omitempty"`
	// 更新者ID
	UpdatedBy int64 `json:"updated_by,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the AssignmentPolicyQuery when eager-loading is set.
	Edges                     AssignmentPolicyEdges `json:"edges"`
	space_assignment_policies *uint64
	selectValues              sql.SelectValues
}

// AssignmentPolicyEdges holds the relations/edges for other nodes in the graph.
type AssignmentPolicyEdges struct {
	// 关联空间
	Space *Space `json:"space,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// SpaceOrErr returns the Space value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e AssignmentPolicyEdges) SpaceOrErr() (*Space, error) {
	if e.Space != nil {
		return e.Space, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: space.Label}
	}
	return nil, &NotLoadedError{edge: "space"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AssignmentPolicy) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case assignmentpolicy.FieldLayers, assignmentpolicy.FieldTimeFilters, assignmentpolicy.FieldFilters:
			values[i] = new([]byte)
		case assignmentpolicy.FieldPriority, assignmentpolicy.FieldAggrWindow, assignmentpolicy.FieldUpdatedBy:
			values[i] = new(sql.NullInt64)
		case assignmentpolicy.FieldRuleID, assignmentpolicy.FieldRuleName, assignmentpolicy.FieldTemplateID, assignmentpolicy.FieldDescription, assignmentpolicy.FieldStatus:
			values[i] = new(sql.NullString)
		case assignmentpolicy.FieldCreatedAt, assignmentpolicy.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		case assignmentpolicy.FieldID:
			values[i] = new(uuid.UUID)
		case assignmentpolicy.ForeignKeys[0]: // space_assignment_policies
			values[i] = new(sql.NullInt64)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AssignmentPolicy fields.
func (ap *AssignmentPolicy) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case assignmentpolicy.FieldID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value != nil {
				ap.ID = *value
			}
		case assignmentpolicy.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ap.CreatedAt = value.Time
			}
		case assignmentpolicy.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ap.UpdatedAt = value.Time
			}
		case assignmentpolicy.FieldRuleID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rule_id", values[i])
			} else if value.Valid {
				ap.RuleID = value.String
			}
		case assignmentpolicy.FieldRuleName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field rule_name", values[i])
			} else if value.Valid {
				ap.RuleName = value.String
			}
		case assignmentpolicy.FieldTemplateID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field template_id", values[i])
			} else if value.Valid {
				ap.TemplateID = value.String
			}
		case assignmentpolicy.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				ap.Description = value.String
			}
		case assignmentpolicy.FieldStatus:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				ap.Status = assignmentpolicy.Status(value.String)
			}
		case assignmentpolicy.FieldPriority:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field priority", values[i])
			} else if value.Valid {
				ap.Priority = int32(value.Int64)
			}
		case assignmentpolicy.FieldLayers:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field layers", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ap.Layers); err != nil {
					return fmt.Errorf("unmarshal field layers: %w", err)
				}
			}
		case assignmentpolicy.FieldAggrWindow:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field aggr_window", values[i])
			} else if value.Valid {
				ap.AggrWindow = int32(value.Int64)
			}
		case assignmentpolicy.FieldTimeFilters:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field time_filters", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ap.TimeFilters); err != nil {
					return fmt.Errorf("unmarshal field time_filters: %w", err)
				}
			}
		case assignmentpolicy.FieldFilters:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field filters", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &ap.Filters); err != nil {
					return fmt.Errorf("unmarshal field filters: %w", err)
				}
			}
		case assignmentpolicy.FieldUpdatedBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field updated_by", values[i])
			} else if value.Valid {
				ap.UpdatedBy = value.Int64
			}
		case assignmentpolicy.ForeignKeys[0]:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for edge-field space_assignment_policies", value)
			} else if value.Valid {
				ap.space_assignment_policies = new(uint64)
				*ap.space_assignment_policies = uint64(value.Int64)
			}
		default:
			ap.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AssignmentPolicy.
// This includes values selected through modifiers, order, etc.
func (ap *AssignmentPolicy) Value(name string) (ent.Value, error) {
	return ap.selectValues.Get(name)
}

// QuerySpace queries the "space" edge of the AssignmentPolicy entity.
func (ap *AssignmentPolicy) QuerySpace() *SpaceQuery {
	return NewAssignmentPolicyClient(ap.config).QuerySpace(ap)
}

// Update returns a builder for updating this AssignmentPolicy.
// Note that you need to call AssignmentPolicy.Unwrap() before calling this method if this AssignmentPolicy
// was returned from a transaction, and the transaction was committed or rolled back.
func (ap *AssignmentPolicy) Update() *AssignmentPolicyUpdateOne {
	return NewAssignmentPolicyClient(ap.config).UpdateOne(ap)
}

// Unwrap unwraps the AssignmentPolicy entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ap *AssignmentPolicy) Unwrap() *AssignmentPolicy {
	_tx, ok := ap.config.driver.(*txDriver)
	if !ok {
		panic("ent: AssignmentPolicy is not a transactional entity")
	}
	ap.config.driver = _tx.drv
	return ap
}

// String implements the fmt.Stringer.
func (ap *AssignmentPolicy) String() string {
	var builder strings.Builder
	builder.WriteString("AssignmentPolicy(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ap.ID))
	builder.WriteString("created_at=")
	builder.WriteString(ap.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ap.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("rule_id=")
	builder.WriteString(ap.RuleID)
	builder.WriteString(", ")
	builder.WriteString("rule_name=")
	builder.WriteString(ap.RuleName)
	builder.WriteString(", ")
	builder.WriteString("template_id=")
	builder.WriteString(ap.TemplateID)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(ap.Description)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", ap.Status))
	builder.WriteString(", ")
	builder.WriteString("priority=")
	builder.WriteString(fmt.Sprintf("%v", ap.Priority))
	builder.WriteString(", ")
	builder.WriteString("layers=")
	builder.WriteString(fmt.Sprintf("%v", ap.Layers))
	builder.WriteString(", ")
	builder.WriteString("aggr_window=")
	builder.WriteString(fmt.Sprintf("%v", ap.AggrWindow))
	builder.WriteString(", ")
	builder.WriteString("time_filters=")
	builder.WriteString(fmt.Sprintf("%v", ap.TimeFilters))
	builder.WriteString(", ")
	builder.WriteString("filters=")
	builder.WriteString(fmt.Sprintf("%v", ap.Filters))
	builder.WriteString(", ")
	builder.WriteString("updated_by=")
	builder.WriteString(fmt.Sprintf("%v", ap.UpdatedBy))
	builder.WriteByte(')')
	return builder.String()
}

// AssignmentPolicies is a parsable slice of AssignmentPolicy.
type AssignmentPolicies []*AssignmentPolicy
