package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_common/utils/orm/mixins"
)

type AssignmentPolicy struct {
	ent.Schema
}

func (AssignmentPolicy) Fields() []ent.Field {
	return []ent.Field{
		field.String("rule_id").Unique().Comment("规则ID"),
		field.String("rule_name").Comment("规则名称"),
		field.String("template_id").Optional().Comment("模板ID"),
		field.String("description").Optional().Comment("描述"),
		field.Enum("status").Values("enabled", "disabled").Default("enabled").Comment("状态"),
		field.Int32("priority").Default(1).Comment("优先级"),
		field.JSON("layers", []map[string]interface{}{}).Optional().Comment("分层通知"),
		field.Int32("aggr_window").Default(300).Comment("聚合窗口(秒)"),
		field.JSON("time_filters", []map[string]interface{}{}).Optional().Comment("时间过滤器"),
		field.JSON("filters", []map[string]interface{}{}).Optional().Comment("过滤器组"),
		field.Int64("updated_by").Optional().Comment("更新者ID"),
	}
}

func (AssignmentPolicy) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("rule_id"),
		index.Fields("priority"),
	}
}

func (AssignmentPolicy) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixins.UUIDMixin{},
	}
}

func (AssignmentPolicy) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("space", Space.Type).Ref("assignment_policies").Unique().Comment("关联空间"),
	}
}

func (AssignmentPolicy) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.WithComments(true),
		entsql.Annotation{Table: "oncall_assignment_policies"},
	}
}
