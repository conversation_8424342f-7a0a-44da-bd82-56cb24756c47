// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/alertroutes"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/alertrule"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/enrichment"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/schema"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/space"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	alertroutesMixin := schema.AlertRoutes{}.Mixin()
	alertroutesMixinFields0 := alertroutesMixin[0].Fields()
	_ = alertroutesMixinFields0
	alertroutesFields := schema.AlertRoutes{}.Fields()
	_ = alertroutesFields
	// alertroutesDescCreatedAt is the schema descriptor for created_at field.
	alertroutesDescCreatedAt := alertroutesMixinFields0[1].Descriptor()
	// alertroutes.DefaultCreatedAt holds the default value on creation for the created_at field.
	alertroutes.DefaultCreatedAt = alertroutesDescCreatedAt.Default.(func() time.Time)
	// alertroutesDescUpdatedAt is the schema descriptor for updated_at field.
	alertroutesDescUpdatedAt := alertroutesMixinFields0[2].Descriptor()
	// alertroutes.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	alertroutes.DefaultUpdatedAt = alertroutesDescUpdatedAt.Default.(func() time.Time)
	// alertroutes.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	alertroutes.UpdateDefaultUpdatedAt = alertroutesDescUpdatedAt.UpdateDefault.(func() time.Time)
	// alertroutesDescVersion is the schema descriptor for version field.
	alertroutesDescVersion := alertroutesFields[4].Descriptor()
	// alertroutes.DefaultVersion holds the default value on creation for the version field.
	alertroutes.DefaultVersion = alertroutesDescVersion.Default.(int32)
	alertruleMixin := schema.AlertRule{}.Mixin()
	alertruleMixinFields0 := alertruleMixin[0].Fields()
	_ = alertruleMixinFields0
	alertruleFields := schema.AlertRule{}.Fields()
	_ = alertruleFields
	// alertruleDescCreatedAt is the schema descriptor for created_at field.
	alertruleDescCreatedAt := alertruleMixinFields0[1].Descriptor()
	// alertrule.DefaultCreatedAt holds the default value on creation for the created_at field.
	alertrule.DefaultCreatedAt = alertruleDescCreatedAt.Default.(func() time.Time)
	// alertruleDescUpdatedAt is the schema descriptor for updated_at field.
	alertruleDescUpdatedAt := alertruleMixinFields0[2].Descriptor()
	// alertrule.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	alertrule.DefaultUpdatedAt = alertruleDescUpdatedAt.Default.(func() time.Time)
	// alertrule.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	alertrule.UpdateDefaultUpdatedAt = alertruleDescUpdatedAt.UpdateDefault.(func() time.Time)
	// alertruleDescPriority is the schema descriptor for priority field.
	alertruleDescPriority := alertruleFields[7].Descriptor()
	// alertrule.DefaultPriority holds the default value on creation for the priority field.
	alertrule.DefaultPriority = alertruleDescPriority.Default.(int)
	assignmentpolicyMixin := schema.AssignmentPolicy{}.Mixin()
	assignmentpolicyMixinFields0 := assignmentpolicyMixin[0].Fields()
	_ = assignmentpolicyMixinFields0
	assignmentpolicyFields := schema.AssignmentPolicy{}.Fields()
	_ = assignmentpolicyFields
	// assignmentpolicyDescCreatedAt is the schema descriptor for created_at field.
	assignmentpolicyDescCreatedAt := assignmentpolicyMixinFields0[1].Descriptor()
	// assignmentpolicy.DefaultCreatedAt holds the default value on creation for the created_at field.
	assignmentpolicy.DefaultCreatedAt = assignmentpolicyDescCreatedAt.Default.(func() time.Time)
	// assignmentpolicyDescUpdatedAt is the schema descriptor for updated_at field.
	assignmentpolicyDescUpdatedAt := assignmentpolicyMixinFields0[2].Descriptor()
	// assignmentpolicy.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	assignmentpolicy.DefaultUpdatedAt = assignmentpolicyDescUpdatedAt.Default.(func() time.Time)
	// assignmentpolicy.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	assignmentpolicy.UpdateDefaultUpdatedAt = assignmentpolicyDescUpdatedAt.UpdateDefault.(func() time.Time)
	// assignmentpolicyDescPriority is the schema descriptor for priority field.
	assignmentpolicyDescPriority := assignmentpolicyFields[5].Descriptor()
	// assignmentpolicy.DefaultPriority holds the default value on creation for the priority field.
	assignmentpolicy.DefaultPriority = assignmentpolicyDescPriority.Default.(int32)
	// assignmentpolicyDescAggrWindow is the schema descriptor for aggr_window field.
	assignmentpolicyDescAggrWindow := assignmentpolicyFields[7].Descriptor()
	// assignmentpolicy.DefaultAggrWindow holds the default value on creation for the aggr_window field.
	assignmentpolicy.DefaultAggrWindow = assignmentpolicyDescAggrWindow.Default.(int32)
	// assignmentpolicyDescID is the schema descriptor for id field.
	assignmentpolicyDescID := assignmentpolicyMixinFields0[0].Descriptor()
	// assignmentpolicy.DefaultID holds the default value on creation for the id field.
	assignmentpolicy.DefaultID = assignmentpolicyDescID.Default.(func() uuid.UUID)
	enrichmentMixin := schema.Enrichment{}.Mixin()
	enrichmentMixinFields0 := enrichmentMixin[0].Fields()
	_ = enrichmentMixinFields0
	enrichmentFields := schema.Enrichment{}.Fields()
	_ = enrichmentFields
	// enrichmentDescCreatedAt is the schema descriptor for created_at field.
	enrichmentDescCreatedAt := enrichmentMixinFields0[1].Descriptor()
	// enrichment.DefaultCreatedAt holds the default value on creation for the created_at field.
	enrichment.DefaultCreatedAt = enrichmentDescCreatedAt.Default.(func() time.Time)
	// enrichmentDescUpdatedAt is the schema descriptor for updated_at field.
	enrichmentDescUpdatedAt := enrichmentMixinFields0[2].Descriptor()
	// enrichment.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	enrichment.DefaultUpdatedAt = enrichmentDescUpdatedAt.Default.(func() time.Time)
	// enrichment.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	enrichment.UpdateDefaultUpdatedAt = enrichmentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// enrichmentDescPriority is the schema descriptor for priority field.
	enrichmentDescPriority := enrichmentFields[7].Descriptor()
	// enrichment.DefaultPriority holds the default value on creation for the priority field.
	enrichment.DefaultPriority = enrichmentDescPriority.Default.(int)
	integrationMixin := schema.Integration{}.Mixin()
	integrationMixinFields0 := integrationMixin[0].Fields()
	_ = integrationMixinFields0
	integrationMixinFields1 := integrationMixin[1].Fields()
	_ = integrationMixinFields1
	integrationFields := schema.Integration{}.Fields()
	_ = integrationFields
	// integrationDescCreatedAt is the schema descriptor for created_at field.
	integrationDescCreatedAt := integrationMixinFields0[1].Descriptor()
	// integration.DefaultCreatedAt holds the default value on creation for the created_at field.
	integration.DefaultCreatedAt = integrationDescCreatedAt.Default.(func() time.Time)
	// integrationDescUpdatedAt is the schema descriptor for updated_at field.
	integrationDescUpdatedAt := integrationMixinFields0[2].Descriptor()
	// integration.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	integration.DefaultUpdatedAt = integrationDescUpdatedAt.Default.(func() time.Time)
	// integration.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	integration.UpdateDefaultUpdatedAt = integrationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// integrationDescStatus is the schema descriptor for status field.
	integrationDescStatus := integrationMixinFields1[0].Descriptor()
	// integration.DefaultStatus holds the default value on creation for the status field.
	integration.DefaultStatus = integrationDescStatus.Default.(uint8)
	// integrationDescID is the schema descriptor for id field.
	integrationDescID := integrationMixinFields0[0].Descriptor()
	// integration.DefaultID holds the default value on creation for the id field.
	integration.DefaultID = integrationDescID.Default.(func() uuid.UUID)
	spaceMixin := schema.Space{}.Mixin()
	spaceMixinFields0 := spaceMixin[0].Fields()
	_ = spaceMixinFields0
	spaceFields := schema.Space{}.Fields()
	_ = spaceFields
	// spaceDescCreatedAt is the schema descriptor for created_at field.
	spaceDescCreatedAt := spaceMixinFields0[1].Descriptor()
	// space.DefaultCreatedAt holds the default value on creation for the created_at field.
	space.DefaultCreatedAt = spaceDescCreatedAt.Default.(func() time.Time)
	// spaceDescUpdatedAt is the schema descriptor for updated_at field.
	spaceDescUpdatedAt := spaceMixinFields0[2].Descriptor()
	// space.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	space.DefaultUpdatedAt = spaceDescUpdatedAt.Default.(func() time.Time)
	// space.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	space.UpdateDefaultUpdatedAt = spaceDescUpdatedAt.UpdateDefault.(func() time.Time)
	// spaceDescEnabled is the schema descriptor for enabled field.
	spaceDescEnabled := spaceFields[5].Descriptor()
	// space.DefaultEnabled holds the default value on creation for the enabled field.
	space.DefaultEnabled = spaceDescEnabled.Default.(bool)
}
