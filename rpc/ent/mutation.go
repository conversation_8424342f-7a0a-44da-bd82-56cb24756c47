// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/alertroutes"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/alertrule"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/enrichment"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/predicate"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/space"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAlertRoutes      = "AlertRoutes"
	TypeAlertRule        = "AlertRule"
	TypeAssignmentPolicy = "AssignmentPolicy"
	TypeEnrichment       = "Enrichment"
	TypeIntegration      = "Integration"
	TypeSpace            = "Space"
)

// AlertRoutesMutation represents an operation that mutates the AlertRoutes nodes in the graph.
type AlertRoutesMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uint64
	created_at          *time.Time
	updated_at          *time.Time
	integration_id      *string
	cases               *[]map[string]interface{}
	appendcases         []map[string]interface{}
	default_route       *map[string]interface{}
	status              *alertroutes.Status
	version             *int32
	addversion          *int32
	updated_by          *int64
	addupdated_by       *int64
	creator_by          *int64
	addcreator_by       *int64
	clearedFields       map[string]struct{}
	spaces              map[uint64]struct{}
	removedspaces       map[uint64]struct{}
	clearedspaces       bool
	integrations        map[uuid.UUID]struct{}
	removedintegrations map[uuid.UUID]struct{}
	clearedintegrations bool
	done                bool
	oldValue            func(context.Context) (*AlertRoutes, error)
	predicates          []predicate.AlertRoutes
}

var _ ent.Mutation = (*AlertRoutesMutation)(nil)

// alertroutesOption allows management of the mutation configuration using functional options.
type alertroutesOption func(*AlertRoutesMutation)

// newAlertRoutesMutation creates new mutation for the AlertRoutes entity.
func newAlertRoutesMutation(c config, op Op, opts ...alertroutesOption) *AlertRoutesMutation {
	m := &AlertRoutesMutation{
		config:        c,
		op:            op,
		typ:           TypeAlertRoutes,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAlertRoutesID sets the ID field of the mutation.
func withAlertRoutesID(id uint64) alertroutesOption {
	return func(m *AlertRoutesMutation) {
		var (
			err   error
			once  sync.Once
			value *AlertRoutes
		)
		m.oldValue = func(ctx context.Context) (*AlertRoutes, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().AlertRoutes.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAlertRoutes sets the old AlertRoutes of the mutation.
func withAlertRoutes(node *AlertRoutes) alertroutesOption {
	return func(m *AlertRoutesMutation) {
		m.oldValue = func(context.Context) (*AlertRoutes, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AlertRoutesMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AlertRoutesMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of AlertRoutes entities.
func (m *AlertRoutesMutation) SetID(id uint64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AlertRoutesMutation) ID() (id uint64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AlertRoutesMutation) IDs(ctx context.Context) ([]uint64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uint64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().AlertRoutes.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *AlertRoutesMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *AlertRoutesMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *AlertRoutesMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *AlertRoutesMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *AlertRoutesMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *AlertRoutesMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetIntegrationID sets the "integration_id" field.
func (m *AlertRoutesMutation) SetIntegrationID(s string) {
	m.integration_id = &s
}

// IntegrationID returns the value of the "integration_id" field in the mutation.
func (m *AlertRoutesMutation) IntegrationID() (r string, exists bool) {
	v := m.integration_id
	if v == nil {
		return
	}
	return *v, true
}

// OldIntegrationID returns the old "integration_id" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldIntegrationID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIntegrationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIntegrationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIntegrationID: %w", err)
	}
	return oldValue.IntegrationID, nil
}

// ResetIntegrationID resets all changes to the "integration_id" field.
func (m *AlertRoutesMutation) ResetIntegrationID() {
	m.integration_id = nil
}

// SetCases sets the "cases" field.
func (m *AlertRoutesMutation) SetCases(value []map[string]interface{}) {
	m.cases = &value
	m.appendcases = nil
}

// Cases returns the value of the "cases" field in the mutation.
func (m *AlertRoutesMutation) Cases() (r []map[string]interface{}, exists bool) {
	v := m.cases
	if v == nil {
		return
	}
	return *v, true
}

// OldCases returns the old "cases" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldCases(ctx context.Context) (v []map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCases is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCases requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCases: %w", err)
	}
	return oldValue.Cases, nil
}

// AppendCases adds value to the "cases" field.
func (m *AlertRoutesMutation) AppendCases(value []map[string]interface{}) {
	m.appendcases = append(m.appendcases, value...)
}

// AppendedCases returns the list of values that were appended to the "cases" field in this mutation.
func (m *AlertRoutesMutation) AppendedCases() ([]map[string]interface{}, bool) {
	if len(m.appendcases) == 0 {
		return nil, false
	}
	return m.appendcases, true
}

// ClearCases clears the value of the "cases" field.
func (m *AlertRoutesMutation) ClearCases() {
	m.cases = nil
	m.appendcases = nil
	m.clearedFields[alertroutes.FieldCases] = struct{}{}
}

// CasesCleared returns if the "cases" field was cleared in this mutation.
func (m *AlertRoutesMutation) CasesCleared() bool {
	_, ok := m.clearedFields[alertroutes.FieldCases]
	return ok
}

// ResetCases resets all changes to the "cases" field.
func (m *AlertRoutesMutation) ResetCases() {
	m.cases = nil
	m.appendcases = nil
	delete(m.clearedFields, alertroutes.FieldCases)
}

// SetDefaultRoute sets the "default_route" field.
func (m *AlertRoutesMutation) SetDefaultRoute(value map[string]interface{}) {
	m.default_route = &value
}

// DefaultRoute returns the value of the "default_route" field in the mutation.
func (m *AlertRoutesMutation) DefaultRoute() (r map[string]interface{}, exists bool) {
	v := m.default_route
	if v == nil {
		return
	}
	return *v, true
}

// OldDefaultRoute returns the old "default_route" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldDefaultRoute(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDefaultRoute is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDefaultRoute requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDefaultRoute: %w", err)
	}
	return oldValue.DefaultRoute, nil
}

// ClearDefaultRoute clears the value of the "default_route" field.
func (m *AlertRoutesMutation) ClearDefaultRoute() {
	m.default_route = nil
	m.clearedFields[alertroutes.FieldDefaultRoute] = struct{}{}
}

// DefaultRouteCleared returns if the "default_route" field was cleared in this mutation.
func (m *AlertRoutesMutation) DefaultRouteCleared() bool {
	_, ok := m.clearedFields[alertroutes.FieldDefaultRoute]
	return ok
}

// ResetDefaultRoute resets all changes to the "default_route" field.
func (m *AlertRoutesMutation) ResetDefaultRoute() {
	m.default_route = nil
	delete(m.clearedFields, alertroutes.FieldDefaultRoute)
}

// SetStatus sets the "status" field.
func (m *AlertRoutesMutation) SetStatus(a alertroutes.Status) {
	m.status = &a
}

// Status returns the value of the "status" field in the mutation.
func (m *AlertRoutesMutation) Status() (r alertroutes.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldStatus(ctx context.Context) (v alertroutes.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *AlertRoutesMutation) ResetStatus() {
	m.status = nil
}

// SetVersion sets the "version" field.
func (m *AlertRoutesMutation) SetVersion(i int32) {
	m.version = &i
	m.addversion = nil
}

// Version returns the value of the "version" field in the mutation.
func (m *AlertRoutesMutation) Version() (r int32, exists bool) {
	v := m.version
	if v == nil {
		return
	}
	return *v, true
}

// OldVersion returns the old "version" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldVersion(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVersion is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVersion requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVersion: %w", err)
	}
	return oldValue.Version, nil
}

// AddVersion adds i to the "version" field.
func (m *AlertRoutesMutation) AddVersion(i int32) {
	if m.addversion != nil {
		*m.addversion += i
	} else {
		m.addversion = &i
	}
}

// AddedVersion returns the value that was added to the "version" field in this mutation.
func (m *AlertRoutesMutation) AddedVersion() (r int32, exists bool) {
	v := m.addversion
	if v == nil {
		return
	}
	return *v, true
}

// ResetVersion resets all changes to the "version" field.
func (m *AlertRoutesMutation) ResetVersion() {
	m.version = nil
	m.addversion = nil
}

// SetUpdatedBy sets the "updated_by" field.
func (m *AlertRoutesMutation) SetUpdatedBy(i int64) {
	m.updated_by = &i
	m.addupdated_by = nil
}

// UpdatedBy returns the value of the "updated_by" field in the mutation.
func (m *AlertRoutesMutation) UpdatedBy() (r int64, exists bool) {
	v := m.updated_by
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedBy returns the old "updated_by" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldUpdatedBy(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedBy: %w", err)
	}
	return oldValue.UpdatedBy, nil
}

// AddUpdatedBy adds i to the "updated_by" field.
func (m *AlertRoutesMutation) AddUpdatedBy(i int64) {
	if m.addupdated_by != nil {
		*m.addupdated_by += i
	} else {
		m.addupdated_by = &i
	}
}

// AddedUpdatedBy returns the value that was added to the "updated_by" field in this mutation.
func (m *AlertRoutesMutation) AddedUpdatedBy() (r int64, exists bool) {
	v := m.addupdated_by
	if v == nil {
		return
	}
	return *v, true
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (m *AlertRoutesMutation) ClearUpdatedBy() {
	m.updated_by = nil
	m.addupdated_by = nil
	m.clearedFields[alertroutes.FieldUpdatedBy] = struct{}{}
}

// UpdatedByCleared returns if the "updated_by" field was cleared in this mutation.
func (m *AlertRoutesMutation) UpdatedByCleared() bool {
	_, ok := m.clearedFields[alertroutes.FieldUpdatedBy]
	return ok
}

// ResetUpdatedBy resets all changes to the "updated_by" field.
func (m *AlertRoutesMutation) ResetUpdatedBy() {
	m.updated_by = nil
	m.addupdated_by = nil
	delete(m.clearedFields, alertroutes.FieldUpdatedBy)
}

// SetCreatorBy sets the "creator_by" field.
func (m *AlertRoutesMutation) SetCreatorBy(i int64) {
	m.creator_by = &i
	m.addcreator_by = nil
}

// CreatorBy returns the value of the "creator_by" field in the mutation.
func (m *AlertRoutesMutation) CreatorBy() (r int64, exists bool) {
	v := m.creator_by
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatorBy returns the old "creator_by" field's value of the AlertRoutes entity.
// If the AlertRoutes object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRoutesMutation) OldCreatorBy(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatorBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatorBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatorBy: %w", err)
	}
	return oldValue.CreatorBy, nil
}

// AddCreatorBy adds i to the "creator_by" field.
func (m *AlertRoutesMutation) AddCreatorBy(i int64) {
	if m.addcreator_by != nil {
		*m.addcreator_by += i
	} else {
		m.addcreator_by = &i
	}
}

// AddedCreatorBy returns the value that was added to the "creator_by" field in this mutation.
func (m *AlertRoutesMutation) AddedCreatorBy() (r int64, exists bool) {
	v := m.addcreator_by
	if v == nil {
		return
	}
	return *v, true
}

// ClearCreatorBy clears the value of the "creator_by" field.
func (m *AlertRoutesMutation) ClearCreatorBy() {
	m.creator_by = nil
	m.addcreator_by = nil
	m.clearedFields[alertroutes.FieldCreatorBy] = struct{}{}
}

// CreatorByCleared returns if the "creator_by" field was cleared in this mutation.
func (m *AlertRoutesMutation) CreatorByCleared() bool {
	_, ok := m.clearedFields[alertroutes.FieldCreatorBy]
	return ok
}

// ResetCreatorBy resets all changes to the "creator_by" field.
func (m *AlertRoutesMutation) ResetCreatorBy() {
	m.creator_by = nil
	m.addcreator_by = nil
	delete(m.clearedFields, alertroutes.FieldCreatorBy)
}

// AddSpaceIDs adds the "spaces" edge to the Space entity by ids.
func (m *AlertRoutesMutation) AddSpaceIDs(ids ...uint64) {
	if m.spaces == nil {
		m.spaces = make(map[uint64]struct{})
	}
	for i := range ids {
		m.spaces[ids[i]] = struct{}{}
	}
}

// ClearSpaces clears the "spaces" edge to the Space entity.
func (m *AlertRoutesMutation) ClearSpaces() {
	m.clearedspaces = true
}

// SpacesCleared reports if the "spaces" edge to the Space entity was cleared.
func (m *AlertRoutesMutation) SpacesCleared() bool {
	return m.clearedspaces
}

// RemoveSpaceIDs removes the "spaces" edge to the Space entity by IDs.
func (m *AlertRoutesMutation) RemoveSpaceIDs(ids ...uint64) {
	if m.removedspaces == nil {
		m.removedspaces = make(map[uint64]struct{})
	}
	for i := range ids {
		delete(m.spaces, ids[i])
		m.removedspaces[ids[i]] = struct{}{}
	}
}

// RemovedSpaces returns the removed IDs of the "spaces" edge to the Space entity.
func (m *AlertRoutesMutation) RemovedSpacesIDs() (ids []uint64) {
	for id := range m.removedspaces {
		ids = append(ids, id)
	}
	return
}

// SpacesIDs returns the "spaces" edge IDs in the mutation.
func (m *AlertRoutesMutation) SpacesIDs() (ids []uint64) {
	for id := range m.spaces {
		ids = append(ids, id)
	}
	return
}

// ResetSpaces resets all changes to the "spaces" edge.
func (m *AlertRoutesMutation) ResetSpaces() {
	m.spaces = nil
	m.clearedspaces = false
	m.removedspaces = nil
}

// AddIntegrationIDs adds the "integrations" edge to the Integration entity by ids.
func (m *AlertRoutesMutation) AddIntegrationIDs(ids ...uuid.UUID) {
	if m.integrations == nil {
		m.integrations = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.integrations[ids[i]] = struct{}{}
	}
}

// ClearIntegrations clears the "integrations" edge to the Integration entity.
func (m *AlertRoutesMutation) ClearIntegrations() {
	m.clearedintegrations = true
}

// IntegrationsCleared reports if the "integrations" edge to the Integration entity was cleared.
func (m *AlertRoutesMutation) IntegrationsCleared() bool {
	return m.clearedintegrations
}

// RemoveIntegrationIDs removes the "integrations" edge to the Integration entity by IDs.
func (m *AlertRoutesMutation) RemoveIntegrationIDs(ids ...uuid.UUID) {
	if m.removedintegrations == nil {
		m.removedintegrations = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.integrations, ids[i])
		m.removedintegrations[ids[i]] = struct{}{}
	}
}

// RemovedIntegrations returns the removed IDs of the "integrations" edge to the Integration entity.
func (m *AlertRoutesMutation) RemovedIntegrationsIDs() (ids []uuid.UUID) {
	for id := range m.removedintegrations {
		ids = append(ids, id)
	}
	return
}

// IntegrationsIDs returns the "integrations" edge IDs in the mutation.
func (m *AlertRoutesMutation) IntegrationsIDs() (ids []uuid.UUID) {
	for id := range m.integrations {
		ids = append(ids, id)
	}
	return
}

// ResetIntegrations resets all changes to the "integrations" edge.
func (m *AlertRoutesMutation) ResetIntegrations() {
	m.integrations = nil
	m.clearedintegrations = false
	m.removedintegrations = nil
}

// Where appends a list predicates to the AlertRoutesMutation builder.
func (m *AlertRoutesMutation) Where(ps ...predicate.AlertRoutes) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AlertRoutesMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AlertRoutesMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.AlertRoutes, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AlertRoutesMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AlertRoutesMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (AlertRoutes).
func (m *AlertRoutesMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AlertRoutesMutation) Fields() []string {
	fields := make([]string, 0, 9)
	if m.created_at != nil {
		fields = append(fields, alertroutes.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, alertroutes.FieldUpdatedAt)
	}
	if m.integration_id != nil {
		fields = append(fields, alertroutes.FieldIntegrationID)
	}
	if m.cases != nil {
		fields = append(fields, alertroutes.FieldCases)
	}
	if m.default_route != nil {
		fields = append(fields, alertroutes.FieldDefaultRoute)
	}
	if m.status != nil {
		fields = append(fields, alertroutes.FieldStatus)
	}
	if m.version != nil {
		fields = append(fields, alertroutes.FieldVersion)
	}
	if m.updated_by != nil {
		fields = append(fields, alertroutes.FieldUpdatedBy)
	}
	if m.creator_by != nil {
		fields = append(fields, alertroutes.FieldCreatorBy)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AlertRoutesMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case alertroutes.FieldCreatedAt:
		return m.CreatedAt()
	case alertroutes.FieldUpdatedAt:
		return m.UpdatedAt()
	case alertroutes.FieldIntegrationID:
		return m.IntegrationID()
	case alertroutes.FieldCases:
		return m.Cases()
	case alertroutes.FieldDefaultRoute:
		return m.DefaultRoute()
	case alertroutes.FieldStatus:
		return m.Status()
	case alertroutes.FieldVersion:
		return m.Version()
	case alertroutes.FieldUpdatedBy:
		return m.UpdatedBy()
	case alertroutes.FieldCreatorBy:
		return m.CreatorBy()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AlertRoutesMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case alertroutes.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case alertroutes.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case alertroutes.FieldIntegrationID:
		return m.OldIntegrationID(ctx)
	case alertroutes.FieldCases:
		return m.OldCases(ctx)
	case alertroutes.FieldDefaultRoute:
		return m.OldDefaultRoute(ctx)
	case alertroutes.FieldStatus:
		return m.OldStatus(ctx)
	case alertroutes.FieldVersion:
		return m.OldVersion(ctx)
	case alertroutes.FieldUpdatedBy:
		return m.OldUpdatedBy(ctx)
	case alertroutes.FieldCreatorBy:
		return m.OldCreatorBy(ctx)
	}
	return nil, fmt.Errorf("unknown AlertRoutes field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AlertRoutesMutation) SetField(name string, value ent.Value) error {
	switch name {
	case alertroutes.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case alertroutes.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case alertroutes.FieldIntegrationID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIntegrationID(v)
		return nil
	case alertroutes.FieldCases:
		v, ok := value.([]map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCases(v)
		return nil
	case alertroutes.FieldDefaultRoute:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDefaultRoute(v)
		return nil
	case alertroutes.FieldStatus:
		v, ok := value.(alertroutes.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case alertroutes.FieldVersion:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVersion(v)
		return nil
	case alertroutes.FieldUpdatedBy:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedBy(v)
		return nil
	case alertroutes.FieldCreatorBy:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatorBy(v)
		return nil
	}
	return fmt.Errorf("unknown AlertRoutes field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AlertRoutesMutation) AddedFields() []string {
	var fields []string
	if m.addversion != nil {
		fields = append(fields, alertroutes.FieldVersion)
	}
	if m.addupdated_by != nil {
		fields = append(fields, alertroutes.FieldUpdatedBy)
	}
	if m.addcreator_by != nil {
		fields = append(fields, alertroutes.FieldCreatorBy)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AlertRoutesMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case alertroutes.FieldVersion:
		return m.AddedVersion()
	case alertroutes.FieldUpdatedBy:
		return m.AddedUpdatedBy()
	case alertroutes.FieldCreatorBy:
		return m.AddedCreatorBy()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AlertRoutesMutation) AddField(name string, value ent.Value) error {
	switch name {
	case alertroutes.FieldVersion:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddVersion(v)
		return nil
	case alertroutes.FieldUpdatedBy:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddUpdatedBy(v)
		return nil
	case alertroutes.FieldCreatorBy:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddCreatorBy(v)
		return nil
	}
	return fmt.Errorf("unknown AlertRoutes numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AlertRoutesMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(alertroutes.FieldCases) {
		fields = append(fields, alertroutes.FieldCases)
	}
	if m.FieldCleared(alertroutes.FieldDefaultRoute) {
		fields = append(fields, alertroutes.FieldDefaultRoute)
	}
	if m.FieldCleared(alertroutes.FieldUpdatedBy) {
		fields = append(fields, alertroutes.FieldUpdatedBy)
	}
	if m.FieldCleared(alertroutes.FieldCreatorBy) {
		fields = append(fields, alertroutes.FieldCreatorBy)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AlertRoutesMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AlertRoutesMutation) ClearField(name string) error {
	switch name {
	case alertroutes.FieldCases:
		m.ClearCases()
		return nil
	case alertroutes.FieldDefaultRoute:
		m.ClearDefaultRoute()
		return nil
	case alertroutes.FieldUpdatedBy:
		m.ClearUpdatedBy()
		return nil
	case alertroutes.FieldCreatorBy:
		m.ClearCreatorBy()
		return nil
	}
	return fmt.Errorf("unknown AlertRoutes nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AlertRoutesMutation) ResetField(name string) error {
	switch name {
	case alertroutes.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case alertroutes.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case alertroutes.FieldIntegrationID:
		m.ResetIntegrationID()
		return nil
	case alertroutes.FieldCases:
		m.ResetCases()
		return nil
	case alertroutes.FieldDefaultRoute:
		m.ResetDefaultRoute()
		return nil
	case alertroutes.FieldStatus:
		m.ResetStatus()
		return nil
	case alertroutes.FieldVersion:
		m.ResetVersion()
		return nil
	case alertroutes.FieldUpdatedBy:
		m.ResetUpdatedBy()
		return nil
	case alertroutes.FieldCreatorBy:
		m.ResetCreatorBy()
		return nil
	}
	return fmt.Errorf("unknown AlertRoutes field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AlertRoutesMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.spaces != nil {
		edges = append(edges, alertroutes.EdgeSpaces)
	}
	if m.integrations != nil {
		edges = append(edges, alertroutes.EdgeIntegrations)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AlertRoutesMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case alertroutes.EdgeSpaces:
		ids := make([]ent.Value, 0, len(m.spaces))
		for id := range m.spaces {
			ids = append(ids, id)
		}
		return ids
	case alertroutes.EdgeIntegrations:
		ids := make([]ent.Value, 0, len(m.integrations))
		for id := range m.integrations {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AlertRoutesMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removedspaces != nil {
		edges = append(edges, alertroutes.EdgeSpaces)
	}
	if m.removedintegrations != nil {
		edges = append(edges, alertroutes.EdgeIntegrations)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AlertRoutesMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case alertroutes.EdgeSpaces:
		ids := make([]ent.Value, 0, len(m.removedspaces))
		for id := range m.removedspaces {
			ids = append(ids, id)
		}
		return ids
	case alertroutes.EdgeIntegrations:
		ids := make([]ent.Value, 0, len(m.removedintegrations))
		for id := range m.removedintegrations {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AlertRoutesMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedspaces {
		edges = append(edges, alertroutes.EdgeSpaces)
	}
	if m.clearedintegrations {
		edges = append(edges, alertroutes.EdgeIntegrations)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AlertRoutesMutation) EdgeCleared(name string) bool {
	switch name {
	case alertroutes.EdgeSpaces:
		return m.clearedspaces
	case alertroutes.EdgeIntegrations:
		return m.clearedintegrations
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AlertRoutesMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown AlertRoutes unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AlertRoutesMutation) ResetEdge(name string) error {
	switch name {
	case alertroutes.EdgeSpaces:
		m.ResetSpaces()
		return nil
	case alertroutes.EdgeIntegrations:
		m.ResetIntegrations()
		return nil
	}
	return fmt.Errorf("unknown AlertRoutes edge %s", name)
}

// AlertRuleMutation represents an operation that mutates the AlertRule nodes in the graph.
type AlertRuleMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uint64
	created_at          *time.Time
	updated_at          *time.Time
	name                *string
	_type               *alertrule.Type
	key                 *string
	value               *string
	condition           *alertrule.Condition
	_config             *map[string]interface{}
	description         *string
	priority            *int
	addpriority         *int
	clearedFields       map[string]struct{}
	integrations        map[uuid.UUID]struct{}
	removedintegrations map[uuid.UUID]struct{}
	clearedintegrations bool
	done                bool
	oldValue            func(context.Context) (*AlertRule, error)
	predicates          []predicate.AlertRule
}

var _ ent.Mutation = (*AlertRuleMutation)(nil)

// alertruleOption allows management of the mutation configuration using functional options.
type alertruleOption func(*AlertRuleMutation)

// newAlertRuleMutation creates new mutation for the AlertRule entity.
func newAlertRuleMutation(c config, op Op, opts ...alertruleOption) *AlertRuleMutation {
	m := &AlertRuleMutation{
		config:        c,
		op:            op,
		typ:           TypeAlertRule,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAlertRuleID sets the ID field of the mutation.
func withAlertRuleID(id uint64) alertruleOption {
	return func(m *AlertRuleMutation) {
		var (
			err   error
			once  sync.Once
			value *AlertRule
		)
		m.oldValue = func(ctx context.Context) (*AlertRule, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().AlertRule.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAlertRule sets the old AlertRule of the mutation.
func withAlertRule(node *AlertRule) alertruleOption {
	return func(m *AlertRuleMutation) {
		m.oldValue = func(context.Context) (*AlertRule, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AlertRuleMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AlertRuleMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of AlertRule entities.
func (m *AlertRuleMutation) SetID(id uint64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AlertRuleMutation) ID() (id uint64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AlertRuleMutation) IDs(ctx context.Context) ([]uint64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uint64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().AlertRule.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *AlertRuleMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *AlertRuleMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *AlertRuleMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *AlertRuleMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *AlertRuleMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *AlertRuleMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetName sets the "name" field.
func (m *AlertRuleMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *AlertRuleMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *AlertRuleMutation) ResetName() {
	m.name = nil
}

// SetType sets the "type" field.
func (m *AlertRuleMutation) SetType(a alertrule.Type) {
	m._type = &a
}

// GetType returns the value of the "type" field in the mutation.
func (m *AlertRuleMutation) GetType() (r alertrule.Type, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldType(ctx context.Context) (v alertrule.Type, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *AlertRuleMutation) ResetType() {
	m._type = nil
}

// SetKey sets the "key" field.
func (m *AlertRuleMutation) SetKey(s string) {
	m.key = &s
}

// Key returns the value of the "key" field in the mutation.
func (m *AlertRuleMutation) Key() (r string, exists bool) {
	v := m.key
	if v == nil {
		return
	}
	return *v, true
}

// OldKey returns the old "key" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldKey(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldKey is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldKey requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldKey: %w", err)
	}
	return oldValue.Key, nil
}

// ClearKey clears the value of the "key" field.
func (m *AlertRuleMutation) ClearKey() {
	m.key = nil
	m.clearedFields[alertrule.FieldKey] = struct{}{}
}

// KeyCleared returns if the "key" field was cleared in this mutation.
func (m *AlertRuleMutation) KeyCleared() bool {
	_, ok := m.clearedFields[alertrule.FieldKey]
	return ok
}

// ResetKey resets all changes to the "key" field.
func (m *AlertRuleMutation) ResetKey() {
	m.key = nil
	delete(m.clearedFields, alertrule.FieldKey)
}

// SetValue sets the "value" field.
func (m *AlertRuleMutation) SetValue(s string) {
	m.value = &s
}

// Value returns the value of the "value" field in the mutation.
func (m *AlertRuleMutation) Value() (r string, exists bool) {
	v := m.value
	if v == nil {
		return
	}
	return *v, true
}

// OldValue returns the old "value" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldValue(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldValue is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldValue requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldValue: %w", err)
	}
	return oldValue.Value, nil
}

// ClearValue clears the value of the "value" field.
func (m *AlertRuleMutation) ClearValue() {
	m.value = nil
	m.clearedFields[alertrule.FieldValue] = struct{}{}
}

// ValueCleared returns if the "value" field was cleared in this mutation.
func (m *AlertRuleMutation) ValueCleared() bool {
	_, ok := m.clearedFields[alertrule.FieldValue]
	return ok
}

// ResetValue resets all changes to the "value" field.
func (m *AlertRuleMutation) ResetValue() {
	m.value = nil
	delete(m.clearedFields, alertrule.FieldValue)
}

// SetCondition sets the "condition" field.
func (m *AlertRuleMutation) SetCondition(a alertrule.Condition) {
	m.condition = &a
}

// Condition returns the value of the "condition" field in the mutation.
func (m *AlertRuleMutation) Condition() (r alertrule.Condition, exists bool) {
	v := m.condition
	if v == nil {
		return
	}
	return *v, true
}

// OldCondition returns the old "condition" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldCondition(ctx context.Context) (v alertrule.Condition, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCondition is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCondition requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCondition: %w", err)
	}
	return oldValue.Condition, nil
}

// ClearCondition clears the value of the "condition" field.
func (m *AlertRuleMutation) ClearCondition() {
	m.condition = nil
	m.clearedFields[alertrule.FieldCondition] = struct{}{}
}

// ConditionCleared returns if the "condition" field was cleared in this mutation.
func (m *AlertRuleMutation) ConditionCleared() bool {
	_, ok := m.clearedFields[alertrule.FieldCondition]
	return ok
}

// ResetCondition resets all changes to the "condition" field.
func (m *AlertRuleMutation) ResetCondition() {
	m.condition = nil
	delete(m.clearedFields, alertrule.FieldCondition)
}

// SetConfig sets the "config" field.
func (m *AlertRuleMutation) SetConfig(value map[string]interface{}) {
	m._config = &value
}

// Config returns the value of the "config" field in the mutation.
func (m *AlertRuleMutation) Config() (r map[string]interface{}, exists bool) {
	v := m._config
	if v == nil {
		return
	}
	return *v, true
}

// OldConfig returns the old "config" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldConfig(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConfig is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConfig requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConfig: %w", err)
	}
	return oldValue.Config, nil
}

// ClearConfig clears the value of the "config" field.
func (m *AlertRuleMutation) ClearConfig() {
	m._config = nil
	m.clearedFields[alertrule.FieldConfig] = struct{}{}
}

// ConfigCleared returns if the "config" field was cleared in this mutation.
func (m *AlertRuleMutation) ConfigCleared() bool {
	_, ok := m.clearedFields[alertrule.FieldConfig]
	return ok
}

// ResetConfig resets all changes to the "config" field.
func (m *AlertRuleMutation) ResetConfig() {
	m._config = nil
	delete(m.clearedFields, alertrule.FieldConfig)
}

// SetDescription sets the "description" field.
func (m *AlertRuleMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *AlertRuleMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *AlertRuleMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[alertrule.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *AlertRuleMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[alertrule.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *AlertRuleMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, alertrule.FieldDescription)
}

// SetPriority sets the "priority" field.
func (m *AlertRuleMutation) SetPriority(i int) {
	m.priority = &i
	m.addpriority = nil
}

// Priority returns the value of the "priority" field in the mutation.
func (m *AlertRuleMutation) Priority() (r int, exists bool) {
	v := m.priority
	if v == nil {
		return
	}
	return *v, true
}

// OldPriority returns the old "priority" field's value of the AlertRule entity.
// If the AlertRule object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AlertRuleMutation) OldPriority(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPriority is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPriority requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPriority: %w", err)
	}
	return oldValue.Priority, nil
}

// AddPriority adds i to the "priority" field.
func (m *AlertRuleMutation) AddPriority(i int) {
	if m.addpriority != nil {
		*m.addpriority += i
	} else {
		m.addpriority = &i
	}
}

// AddedPriority returns the value that was added to the "priority" field in this mutation.
func (m *AlertRuleMutation) AddedPriority() (r int, exists bool) {
	v := m.addpriority
	if v == nil {
		return
	}
	return *v, true
}

// ResetPriority resets all changes to the "priority" field.
func (m *AlertRuleMutation) ResetPriority() {
	m.priority = nil
	m.addpriority = nil
}

// AddIntegrationIDs adds the "integrations" edge to the Integration entity by ids.
func (m *AlertRuleMutation) AddIntegrationIDs(ids ...uuid.UUID) {
	if m.integrations == nil {
		m.integrations = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.integrations[ids[i]] = struct{}{}
	}
}

// ClearIntegrations clears the "integrations" edge to the Integration entity.
func (m *AlertRuleMutation) ClearIntegrations() {
	m.clearedintegrations = true
}

// IntegrationsCleared reports if the "integrations" edge to the Integration entity was cleared.
func (m *AlertRuleMutation) IntegrationsCleared() bool {
	return m.clearedintegrations
}

// RemoveIntegrationIDs removes the "integrations" edge to the Integration entity by IDs.
func (m *AlertRuleMutation) RemoveIntegrationIDs(ids ...uuid.UUID) {
	if m.removedintegrations == nil {
		m.removedintegrations = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.integrations, ids[i])
		m.removedintegrations[ids[i]] = struct{}{}
	}
}

// RemovedIntegrations returns the removed IDs of the "integrations" edge to the Integration entity.
func (m *AlertRuleMutation) RemovedIntegrationsIDs() (ids []uuid.UUID) {
	for id := range m.removedintegrations {
		ids = append(ids, id)
	}
	return
}

// IntegrationsIDs returns the "integrations" edge IDs in the mutation.
func (m *AlertRuleMutation) IntegrationsIDs() (ids []uuid.UUID) {
	for id := range m.integrations {
		ids = append(ids, id)
	}
	return
}

// ResetIntegrations resets all changes to the "integrations" edge.
func (m *AlertRuleMutation) ResetIntegrations() {
	m.integrations = nil
	m.clearedintegrations = false
	m.removedintegrations = nil
}

// Where appends a list predicates to the AlertRuleMutation builder.
func (m *AlertRuleMutation) Where(ps ...predicate.AlertRule) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AlertRuleMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AlertRuleMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.AlertRule, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AlertRuleMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AlertRuleMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (AlertRule).
func (m *AlertRuleMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AlertRuleMutation) Fields() []string {
	fields := make([]string, 0, 10)
	if m.created_at != nil {
		fields = append(fields, alertrule.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, alertrule.FieldUpdatedAt)
	}
	if m.name != nil {
		fields = append(fields, alertrule.FieldName)
	}
	if m._type != nil {
		fields = append(fields, alertrule.FieldType)
	}
	if m.key != nil {
		fields = append(fields, alertrule.FieldKey)
	}
	if m.value != nil {
		fields = append(fields, alertrule.FieldValue)
	}
	if m.condition != nil {
		fields = append(fields, alertrule.FieldCondition)
	}
	if m._config != nil {
		fields = append(fields, alertrule.FieldConfig)
	}
	if m.description != nil {
		fields = append(fields, alertrule.FieldDescription)
	}
	if m.priority != nil {
		fields = append(fields, alertrule.FieldPriority)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AlertRuleMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case alertrule.FieldCreatedAt:
		return m.CreatedAt()
	case alertrule.FieldUpdatedAt:
		return m.UpdatedAt()
	case alertrule.FieldName:
		return m.Name()
	case alertrule.FieldType:
		return m.GetType()
	case alertrule.FieldKey:
		return m.Key()
	case alertrule.FieldValue:
		return m.Value()
	case alertrule.FieldCondition:
		return m.Condition()
	case alertrule.FieldConfig:
		return m.Config()
	case alertrule.FieldDescription:
		return m.Description()
	case alertrule.FieldPriority:
		return m.Priority()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AlertRuleMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case alertrule.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case alertrule.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case alertrule.FieldName:
		return m.OldName(ctx)
	case alertrule.FieldType:
		return m.OldType(ctx)
	case alertrule.FieldKey:
		return m.OldKey(ctx)
	case alertrule.FieldValue:
		return m.OldValue(ctx)
	case alertrule.FieldCondition:
		return m.OldCondition(ctx)
	case alertrule.FieldConfig:
		return m.OldConfig(ctx)
	case alertrule.FieldDescription:
		return m.OldDescription(ctx)
	case alertrule.FieldPriority:
		return m.OldPriority(ctx)
	}
	return nil, fmt.Errorf("unknown AlertRule field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AlertRuleMutation) SetField(name string, value ent.Value) error {
	switch name {
	case alertrule.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case alertrule.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case alertrule.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case alertrule.FieldType:
		v, ok := value.(alertrule.Type)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case alertrule.FieldKey:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetKey(v)
		return nil
	case alertrule.FieldValue:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetValue(v)
		return nil
	case alertrule.FieldCondition:
		v, ok := value.(alertrule.Condition)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCondition(v)
		return nil
	case alertrule.FieldConfig:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConfig(v)
		return nil
	case alertrule.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case alertrule.FieldPriority:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPriority(v)
		return nil
	}
	return fmt.Errorf("unknown AlertRule field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AlertRuleMutation) AddedFields() []string {
	var fields []string
	if m.addpriority != nil {
		fields = append(fields, alertrule.FieldPriority)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AlertRuleMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case alertrule.FieldPriority:
		return m.AddedPriority()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AlertRuleMutation) AddField(name string, value ent.Value) error {
	switch name {
	case alertrule.FieldPriority:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPriority(v)
		return nil
	}
	return fmt.Errorf("unknown AlertRule numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AlertRuleMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(alertrule.FieldKey) {
		fields = append(fields, alertrule.FieldKey)
	}
	if m.FieldCleared(alertrule.FieldValue) {
		fields = append(fields, alertrule.FieldValue)
	}
	if m.FieldCleared(alertrule.FieldCondition) {
		fields = append(fields, alertrule.FieldCondition)
	}
	if m.FieldCleared(alertrule.FieldConfig) {
		fields = append(fields, alertrule.FieldConfig)
	}
	if m.FieldCleared(alertrule.FieldDescription) {
		fields = append(fields, alertrule.FieldDescription)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AlertRuleMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AlertRuleMutation) ClearField(name string) error {
	switch name {
	case alertrule.FieldKey:
		m.ClearKey()
		return nil
	case alertrule.FieldValue:
		m.ClearValue()
		return nil
	case alertrule.FieldCondition:
		m.ClearCondition()
		return nil
	case alertrule.FieldConfig:
		m.ClearConfig()
		return nil
	case alertrule.FieldDescription:
		m.ClearDescription()
		return nil
	}
	return fmt.Errorf("unknown AlertRule nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AlertRuleMutation) ResetField(name string) error {
	switch name {
	case alertrule.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case alertrule.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case alertrule.FieldName:
		m.ResetName()
		return nil
	case alertrule.FieldType:
		m.ResetType()
		return nil
	case alertrule.FieldKey:
		m.ResetKey()
		return nil
	case alertrule.FieldValue:
		m.ResetValue()
		return nil
	case alertrule.FieldCondition:
		m.ResetCondition()
		return nil
	case alertrule.FieldConfig:
		m.ResetConfig()
		return nil
	case alertrule.FieldDescription:
		m.ResetDescription()
		return nil
	case alertrule.FieldPriority:
		m.ResetPriority()
		return nil
	}
	return fmt.Errorf("unknown AlertRule field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AlertRuleMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.integrations != nil {
		edges = append(edges, alertrule.EdgeIntegrations)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AlertRuleMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case alertrule.EdgeIntegrations:
		ids := make([]ent.Value, 0, len(m.integrations))
		for id := range m.integrations {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AlertRuleMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedintegrations != nil {
		edges = append(edges, alertrule.EdgeIntegrations)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AlertRuleMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case alertrule.EdgeIntegrations:
		ids := make([]ent.Value, 0, len(m.removedintegrations))
		for id := range m.removedintegrations {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AlertRuleMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedintegrations {
		edges = append(edges, alertrule.EdgeIntegrations)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AlertRuleMutation) EdgeCleared(name string) bool {
	switch name {
	case alertrule.EdgeIntegrations:
		return m.clearedintegrations
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AlertRuleMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown AlertRule unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AlertRuleMutation) ResetEdge(name string) error {
	switch name {
	case alertrule.EdgeIntegrations:
		m.ResetIntegrations()
		return nil
	}
	return fmt.Errorf("unknown AlertRule edge %s", name)
}

// AssignmentPolicyMutation represents an operation that mutates the AssignmentPolicy nodes in the graph.
type AssignmentPolicyMutation struct {
	config
	op                 Op
	typ                string
	id                 *uuid.UUID
	created_at         *time.Time
	updated_at         *time.Time
	rule_id            *string
	rule_name          *string
	template_id        *string
	description        *string
	status             *assignmentpolicy.Status
	priority           *int32
	addpriority        *int32
	layers             *[]map[string]interface{}
	appendlayers       []map[string]interface{}
	aggr_window        *int32
	addaggr_window     *int32
	time_filters       *[]map[string]interface{}
	appendtime_filters []map[string]interface{}
	filters            *[]map[string]interface{}
	appendfilters      []map[string]interface{}
	updated_by         *int64
	addupdated_by      *int64
	clearedFields      map[string]struct{}
	space              *uint64
	clearedspace       bool
	done               bool
	oldValue           func(context.Context) (*AssignmentPolicy, error)
	predicates         []predicate.AssignmentPolicy
}

var _ ent.Mutation = (*AssignmentPolicyMutation)(nil)

// assignmentpolicyOption allows management of the mutation configuration using functional options.
type assignmentpolicyOption func(*AssignmentPolicyMutation)

// newAssignmentPolicyMutation creates new mutation for the AssignmentPolicy entity.
func newAssignmentPolicyMutation(c config, op Op, opts ...assignmentpolicyOption) *AssignmentPolicyMutation {
	m := &AssignmentPolicyMutation{
		config:        c,
		op:            op,
		typ:           TypeAssignmentPolicy,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAssignmentPolicyID sets the ID field of the mutation.
func withAssignmentPolicyID(id uuid.UUID) assignmentpolicyOption {
	return func(m *AssignmentPolicyMutation) {
		var (
			err   error
			once  sync.Once
			value *AssignmentPolicy
		)
		m.oldValue = func(ctx context.Context) (*AssignmentPolicy, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().AssignmentPolicy.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAssignmentPolicy sets the old AssignmentPolicy of the mutation.
func withAssignmentPolicy(node *AssignmentPolicy) assignmentpolicyOption {
	return func(m *AssignmentPolicyMutation) {
		m.oldValue = func(context.Context) (*AssignmentPolicy, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AssignmentPolicyMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AssignmentPolicyMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of AssignmentPolicy entities.
func (m *AssignmentPolicyMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AssignmentPolicyMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AssignmentPolicyMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().AssignmentPolicy.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *AssignmentPolicyMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *AssignmentPolicyMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *AssignmentPolicyMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *AssignmentPolicyMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *AssignmentPolicyMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *AssignmentPolicyMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetRuleID sets the "rule_id" field.
func (m *AssignmentPolicyMutation) SetRuleID(s string) {
	m.rule_id = &s
}

// RuleID returns the value of the "rule_id" field in the mutation.
func (m *AssignmentPolicyMutation) RuleID() (r string, exists bool) {
	v := m.rule_id
	if v == nil {
		return
	}
	return *v, true
}

// OldRuleID returns the old "rule_id" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldRuleID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRuleID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRuleID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRuleID: %w", err)
	}
	return oldValue.RuleID, nil
}

// ResetRuleID resets all changes to the "rule_id" field.
func (m *AssignmentPolicyMutation) ResetRuleID() {
	m.rule_id = nil
}

// SetRuleName sets the "rule_name" field.
func (m *AssignmentPolicyMutation) SetRuleName(s string) {
	m.rule_name = &s
}

// RuleName returns the value of the "rule_name" field in the mutation.
func (m *AssignmentPolicyMutation) RuleName() (r string, exists bool) {
	v := m.rule_name
	if v == nil {
		return
	}
	return *v, true
}

// OldRuleName returns the old "rule_name" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldRuleName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldRuleName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldRuleName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldRuleName: %w", err)
	}
	return oldValue.RuleName, nil
}

// ResetRuleName resets all changes to the "rule_name" field.
func (m *AssignmentPolicyMutation) ResetRuleName() {
	m.rule_name = nil
}

// SetTemplateID sets the "template_id" field.
func (m *AssignmentPolicyMutation) SetTemplateID(s string) {
	m.template_id = &s
}

// TemplateID returns the value of the "template_id" field in the mutation.
func (m *AssignmentPolicyMutation) TemplateID() (r string, exists bool) {
	v := m.template_id
	if v == nil {
		return
	}
	return *v, true
}

// OldTemplateID returns the old "template_id" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldTemplateID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTemplateID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTemplateID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTemplateID: %w", err)
	}
	return oldValue.TemplateID, nil
}

// ClearTemplateID clears the value of the "template_id" field.
func (m *AssignmentPolicyMutation) ClearTemplateID() {
	m.template_id = nil
	m.clearedFields[assignmentpolicy.FieldTemplateID] = struct{}{}
}

// TemplateIDCleared returns if the "template_id" field was cleared in this mutation.
func (m *AssignmentPolicyMutation) TemplateIDCleared() bool {
	_, ok := m.clearedFields[assignmentpolicy.FieldTemplateID]
	return ok
}

// ResetTemplateID resets all changes to the "template_id" field.
func (m *AssignmentPolicyMutation) ResetTemplateID() {
	m.template_id = nil
	delete(m.clearedFields, assignmentpolicy.FieldTemplateID)
}

// SetDescription sets the "description" field.
func (m *AssignmentPolicyMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *AssignmentPolicyMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *AssignmentPolicyMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[assignmentpolicy.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *AssignmentPolicyMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[assignmentpolicy.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *AssignmentPolicyMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, assignmentpolicy.FieldDescription)
}

// SetStatus sets the "status" field.
func (m *AssignmentPolicyMutation) SetStatus(a assignmentpolicy.Status) {
	m.status = &a
}

// Status returns the value of the "status" field in the mutation.
func (m *AssignmentPolicyMutation) Status() (r assignmentpolicy.Status, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldStatus(ctx context.Context) (v assignmentpolicy.Status, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// ResetStatus resets all changes to the "status" field.
func (m *AssignmentPolicyMutation) ResetStatus() {
	m.status = nil
}

// SetPriority sets the "priority" field.
func (m *AssignmentPolicyMutation) SetPriority(i int32) {
	m.priority = &i
	m.addpriority = nil
}

// Priority returns the value of the "priority" field in the mutation.
func (m *AssignmentPolicyMutation) Priority() (r int32, exists bool) {
	v := m.priority
	if v == nil {
		return
	}
	return *v, true
}

// OldPriority returns the old "priority" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldPriority(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPriority is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPriority requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPriority: %w", err)
	}
	return oldValue.Priority, nil
}

// AddPriority adds i to the "priority" field.
func (m *AssignmentPolicyMutation) AddPriority(i int32) {
	if m.addpriority != nil {
		*m.addpriority += i
	} else {
		m.addpriority = &i
	}
}

// AddedPriority returns the value that was added to the "priority" field in this mutation.
func (m *AssignmentPolicyMutation) AddedPriority() (r int32, exists bool) {
	v := m.addpriority
	if v == nil {
		return
	}
	return *v, true
}

// ResetPriority resets all changes to the "priority" field.
func (m *AssignmentPolicyMutation) ResetPriority() {
	m.priority = nil
	m.addpriority = nil
}

// SetLayers sets the "layers" field.
func (m *AssignmentPolicyMutation) SetLayers(value []map[string]interface{}) {
	m.layers = &value
	m.appendlayers = nil
}

// Layers returns the value of the "layers" field in the mutation.
func (m *AssignmentPolicyMutation) Layers() (r []map[string]interface{}, exists bool) {
	v := m.layers
	if v == nil {
		return
	}
	return *v, true
}

// OldLayers returns the old "layers" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldLayers(ctx context.Context) (v []map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLayers is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLayers requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLayers: %w", err)
	}
	return oldValue.Layers, nil
}

// AppendLayers adds value to the "layers" field.
func (m *AssignmentPolicyMutation) AppendLayers(value []map[string]interface{}) {
	m.appendlayers = append(m.appendlayers, value...)
}

// AppendedLayers returns the list of values that were appended to the "layers" field in this mutation.
func (m *AssignmentPolicyMutation) AppendedLayers() ([]map[string]interface{}, bool) {
	if len(m.appendlayers) == 0 {
		return nil, false
	}
	return m.appendlayers, true
}

// ClearLayers clears the value of the "layers" field.
func (m *AssignmentPolicyMutation) ClearLayers() {
	m.layers = nil
	m.appendlayers = nil
	m.clearedFields[assignmentpolicy.FieldLayers] = struct{}{}
}

// LayersCleared returns if the "layers" field was cleared in this mutation.
func (m *AssignmentPolicyMutation) LayersCleared() bool {
	_, ok := m.clearedFields[assignmentpolicy.FieldLayers]
	return ok
}

// ResetLayers resets all changes to the "layers" field.
func (m *AssignmentPolicyMutation) ResetLayers() {
	m.layers = nil
	m.appendlayers = nil
	delete(m.clearedFields, assignmentpolicy.FieldLayers)
}

// SetAggrWindow sets the "aggr_window" field.
func (m *AssignmentPolicyMutation) SetAggrWindow(i int32) {
	m.aggr_window = &i
	m.addaggr_window = nil
}

// AggrWindow returns the value of the "aggr_window" field in the mutation.
func (m *AssignmentPolicyMutation) AggrWindow() (r int32, exists bool) {
	v := m.aggr_window
	if v == nil {
		return
	}
	return *v, true
}

// OldAggrWindow returns the old "aggr_window" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldAggrWindow(ctx context.Context) (v int32, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAggrWindow is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAggrWindow requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAggrWindow: %w", err)
	}
	return oldValue.AggrWindow, nil
}

// AddAggrWindow adds i to the "aggr_window" field.
func (m *AssignmentPolicyMutation) AddAggrWindow(i int32) {
	if m.addaggr_window != nil {
		*m.addaggr_window += i
	} else {
		m.addaggr_window = &i
	}
}

// AddedAggrWindow returns the value that was added to the "aggr_window" field in this mutation.
func (m *AssignmentPolicyMutation) AddedAggrWindow() (r int32, exists bool) {
	v := m.addaggr_window
	if v == nil {
		return
	}
	return *v, true
}

// ResetAggrWindow resets all changes to the "aggr_window" field.
func (m *AssignmentPolicyMutation) ResetAggrWindow() {
	m.aggr_window = nil
	m.addaggr_window = nil
}

// SetTimeFilters sets the "time_filters" field.
func (m *AssignmentPolicyMutation) SetTimeFilters(value []map[string]interface{}) {
	m.time_filters = &value
	m.appendtime_filters = nil
}

// TimeFilters returns the value of the "time_filters" field in the mutation.
func (m *AssignmentPolicyMutation) TimeFilters() (r []map[string]interface{}, exists bool) {
	v := m.time_filters
	if v == nil {
		return
	}
	return *v, true
}

// OldTimeFilters returns the old "time_filters" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldTimeFilters(ctx context.Context) (v []map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTimeFilters is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTimeFilters requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTimeFilters: %w", err)
	}
	return oldValue.TimeFilters, nil
}

// AppendTimeFilters adds value to the "time_filters" field.
func (m *AssignmentPolicyMutation) AppendTimeFilters(value []map[string]interface{}) {
	m.appendtime_filters = append(m.appendtime_filters, value...)
}

// AppendedTimeFilters returns the list of values that were appended to the "time_filters" field in this mutation.
func (m *AssignmentPolicyMutation) AppendedTimeFilters() ([]map[string]interface{}, bool) {
	if len(m.appendtime_filters) == 0 {
		return nil, false
	}
	return m.appendtime_filters, true
}

// ClearTimeFilters clears the value of the "time_filters" field.
func (m *AssignmentPolicyMutation) ClearTimeFilters() {
	m.time_filters = nil
	m.appendtime_filters = nil
	m.clearedFields[assignmentpolicy.FieldTimeFilters] = struct{}{}
}

// TimeFiltersCleared returns if the "time_filters" field was cleared in this mutation.
func (m *AssignmentPolicyMutation) TimeFiltersCleared() bool {
	_, ok := m.clearedFields[assignmentpolicy.FieldTimeFilters]
	return ok
}

// ResetTimeFilters resets all changes to the "time_filters" field.
func (m *AssignmentPolicyMutation) ResetTimeFilters() {
	m.time_filters = nil
	m.appendtime_filters = nil
	delete(m.clearedFields, assignmentpolicy.FieldTimeFilters)
}

// SetFilters sets the "filters" field.
func (m *AssignmentPolicyMutation) SetFilters(value []map[string]interface{}) {
	m.filters = &value
	m.appendfilters = nil
}

// Filters returns the value of the "filters" field in the mutation.
func (m *AssignmentPolicyMutation) Filters() (r []map[string]interface{}, exists bool) {
	v := m.filters
	if v == nil {
		return
	}
	return *v, true
}

// OldFilters returns the old "filters" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldFilters(ctx context.Context) (v []map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldFilters is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldFilters requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldFilters: %w", err)
	}
	return oldValue.Filters, nil
}

// AppendFilters adds value to the "filters" field.
func (m *AssignmentPolicyMutation) AppendFilters(value []map[string]interface{}) {
	m.appendfilters = append(m.appendfilters, value...)
}

// AppendedFilters returns the list of values that were appended to the "filters" field in this mutation.
func (m *AssignmentPolicyMutation) AppendedFilters() ([]map[string]interface{}, bool) {
	if len(m.appendfilters) == 0 {
		return nil, false
	}
	return m.appendfilters, true
}

// ClearFilters clears the value of the "filters" field.
func (m *AssignmentPolicyMutation) ClearFilters() {
	m.filters = nil
	m.appendfilters = nil
	m.clearedFields[assignmentpolicy.FieldFilters] = struct{}{}
}

// FiltersCleared returns if the "filters" field was cleared in this mutation.
func (m *AssignmentPolicyMutation) FiltersCleared() bool {
	_, ok := m.clearedFields[assignmentpolicy.FieldFilters]
	return ok
}

// ResetFilters resets all changes to the "filters" field.
func (m *AssignmentPolicyMutation) ResetFilters() {
	m.filters = nil
	m.appendfilters = nil
	delete(m.clearedFields, assignmentpolicy.FieldFilters)
}

// SetUpdatedBy sets the "updated_by" field.
func (m *AssignmentPolicyMutation) SetUpdatedBy(i int64) {
	m.updated_by = &i
	m.addupdated_by = nil
}

// UpdatedBy returns the value of the "updated_by" field in the mutation.
func (m *AssignmentPolicyMutation) UpdatedBy() (r int64, exists bool) {
	v := m.updated_by
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedBy returns the old "updated_by" field's value of the AssignmentPolicy entity.
// If the AssignmentPolicy object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AssignmentPolicyMutation) OldUpdatedBy(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedBy is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedBy requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedBy: %w", err)
	}
	return oldValue.UpdatedBy, nil
}

// AddUpdatedBy adds i to the "updated_by" field.
func (m *AssignmentPolicyMutation) AddUpdatedBy(i int64) {
	if m.addupdated_by != nil {
		*m.addupdated_by += i
	} else {
		m.addupdated_by = &i
	}
}

// AddedUpdatedBy returns the value that was added to the "updated_by" field in this mutation.
func (m *AssignmentPolicyMutation) AddedUpdatedBy() (r int64, exists bool) {
	v := m.addupdated_by
	if v == nil {
		return
	}
	return *v, true
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (m *AssignmentPolicyMutation) ClearUpdatedBy() {
	m.updated_by = nil
	m.addupdated_by = nil
	m.clearedFields[assignmentpolicy.FieldUpdatedBy] = struct{}{}
}

// UpdatedByCleared returns if the "updated_by" field was cleared in this mutation.
func (m *AssignmentPolicyMutation) UpdatedByCleared() bool {
	_, ok := m.clearedFields[assignmentpolicy.FieldUpdatedBy]
	return ok
}

// ResetUpdatedBy resets all changes to the "updated_by" field.
func (m *AssignmentPolicyMutation) ResetUpdatedBy() {
	m.updated_by = nil
	m.addupdated_by = nil
	delete(m.clearedFields, assignmentpolicy.FieldUpdatedBy)
}

// SetSpaceID sets the "space" edge to the Space entity by id.
func (m *AssignmentPolicyMutation) SetSpaceID(id uint64) {
	m.space = &id
}

// ClearSpace clears the "space" edge to the Space entity.
func (m *AssignmentPolicyMutation) ClearSpace() {
	m.clearedspace = true
}

// SpaceCleared reports if the "space" edge to the Space entity was cleared.
func (m *AssignmentPolicyMutation) SpaceCleared() bool {
	return m.clearedspace
}

// SpaceID returns the "space" edge ID in the mutation.
func (m *AssignmentPolicyMutation) SpaceID() (id uint64, exists bool) {
	if m.space != nil {
		return *m.space, true
	}
	return
}

// SpaceIDs returns the "space" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// SpaceID instead. It exists only for internal usage by the builders.
func (m *AssignmentPolicyMutation) SpaceIDs() (ids []uint64) {
	if id := m.space; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetSpace resets all changes to the "space" edge.
func (m *AssignmentPolicyMutation) ResetSpace() {
	m.space = nil
	m.clearedspace = false
}

// Where appends a list predicates to the AssignmentPolicyMutation builder.
func (m *AssignmentPolicyMutation) Where(ps ...predicate.AssignmentPolicy) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AssignmentPolicyMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AssignmentPolicyMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.AssignmentPolicy, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AssignmentPolicyMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AssignmentPolicyMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (AssignmentPolicy).
func (m *AssignmentPolicyMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AssignmentPolicyMutation) Fields() []string {
	fields := make([]string, 0, 13)
	if m.created_at != nil {
		fields = append(fields, assignmentpolicy.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, assignmentpolicy.FieldUpdatedAt)
	}
	if m.rule_id != nil {
		fields = append(fields, assignmentpolicy.FieldRuleID)
	}
	if m.rule_name != nil {
		fields = append(fields, assignmentpolicy.FieldRuleName)
	}
	if m.template_id != nil {
		fields = append(fields, assignmentpolicy.FieldTemplateID)
	}
	if m.description != nil {
		fields = append(fields, assignmentpolicy.FieldDescription)
	}
	if m.status != nil {
		fields = append(fields, assignmentpolicy.FieldStatus)
	}
	if m.priority != nil {
		fields = append(fields, assignmentpolicy.FieldPriority)
	}
	if m.layers != nil {
		fields = append(fields, assignmentpolicy.FieldLayers)
	}
	if m.aggr_window != nil {
		fields = append(fields, assignmentpolicy.FieldAggrWindow)
	}
	if m.time_filters != nil {
		fields = append(fields, assignmentpolicy.FieldTimeFilters)
	}
	if m.filters != nil {
		fields = append(fields, assignmentpolicy.FieldFilters)
	}
	if m.updated_by != nil {
		fields = append(fields, assignmentpolicy.FieldUpdatedBy)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AssignmentPolicyMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case assignmentpolicy.FieldCreatedAt:
		return m.CreatedAt()
	case assignmentpolicy.FieldUpdatedAt:
		return m.UpdatedAt()
	case assignmentpolicy.FieldRuleID:
		return m.RuleID()
	case assignmentpolicy.FieldRuleName:
		return m.RuleName()
	case assignmentpolicy.FieldTemplateID:
		return m.TemplateID()
	case assignmentpolicy.FieldDescription:
		return m.Description()
	case assignmentpolicy.FieldStatus:
		return m.Status()
	case assignmentpolicy.FieldPriority:
		return m.Priority()
	case assignmentpolicy.FieldLayers:
		return m.Layers()
	case assignmentpolicy.FieldAggrWindow:
		return m.AggrWindow()
	case assignmentpolicy.FieldTimeFilters:
		return m.TimeFilters()
	case assignmentpolicy.FieldFilters:
		return m.Filters()
	case assignmentpolicy.FieldUpdatedBy:
		return m.UpdatedBy()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AssignmentPolicyMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case assignmentpolicy.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case assignmentpolicy.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case assignmentpolicy.FieldRuleID:
		return m.OldRuleID(ctx)
	case assignmentpolicy.FieldRuleName:
		return m.OldRuleName(ctx)
	case assignmentpolicy.FieldTemplateID:
		return m.OldTemplateID(ctx)
	case assignmentpolicy.FieldDescription:
		return m.OldDescription(ctx)
	case assignmentpolicy.FieldStatus:
		return m.OldStatus(ctx)
	case assignmentpolicy.FieldPriority:
		return m.OldPriority(ctx)
	case assignmentpolicy.FieldLayers:
		return m.OldLayers(ctx)
	case assignmentpolicy.FieldAggrWindow:
		return m.OldAggrWindow(ctx)
	case assignmentpolicy.FieldTimeFilters:
		return m.OldTimeFilters(ctx)
	case assignmentpolicy.FieldFilters:
		return m.OldFilters(ctx)
	case assignmentpolicy.FieldUpdatedBy:
		return m.OldUpdatedBy(ctx)
	}
	return nil, fmt.Errorf("unknown AssignmentPolicy field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AssignmentPolicyMutation) SetField(name string, value ent.Value) error {
	switch name {
	case assignmentpolicy.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case assignmentpolicy.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case assignmentpolicy.FieldRuleID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRuleID(v)
		return nil
	case assignmentpolicy.FieldRuleName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetRuleName(v)
		return nil
	case assignmentpolicy.FieldTemplateID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTemplateID(v)
		return nil
	case assignmentpolicy.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case assignmentpolicy.FieldStatus:
		v, ok := value.(assignmentpolicy.Status)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case assignmentpolicy.FieldPriority:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPriority(v)
		return nil
	case assignmentpolicy.FieldLayers:
		v, ok := value.([]map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLayers(v)
		return nil
	case assignmentpolicy.FieldAggrWindow:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAggrWindow(v)
		return nil
	case assignmentpolicy.FieldTimeFilters:
		v, ok := value.([]map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTimeFilters(v)
		return nil
	case assignmentpolicy.FieldFilters:
		v, ok := value.([]map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetFilters(v)
		return nil
	case assignmentpolicy.FieldUpdatedBy:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedBy(v)
		return nil
	}
	return fmt.Errorf("unknown AssignmentPolicy field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AssignmentPolicyMutation) AddedFields() []string {
	var fields []string
	if m.addpriority != nil {
		fields = append(fields, assignmentpolicy.FieldPriority)
	}
	if m.addaggr_window != nil {
		fields = append(fields, assignmentpolicy.FieldAggrWindow)
	}
	if m.addupdated_by != nil {
		fields = append(fields, assignmentpolicy.FieldUpdatedBy)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AssignmentPolicyMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case assignmentpolicy.FieldPriority:
		return m.AddedPriority()
	case assignmentpolicy.FieldAggrWindow:
		return m.AddedAggrWindow()
	case assignmentpolicy.FieldUpdatedBy:
		return m.AddedUpdatedBy()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AssignmentPolicyMutation) AddField(name string, value ent.Value) error {
	switch name {
	case assignmentpolicy.FieldPriority:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPriority(v)
		return nil
	case assignmentpolicy.FieldAggrWindow:
		v, ok := value.(int32)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddAggrWindow(v)
		return nil
	case assignmentpolicy.FieldUpdatedBy:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddUpdatedBy(v)
		return nil
	}
	return fmt.Errorf("unknown AssignmentPolicy numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AssignmentPolicyMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(assignmentpolicy.FieldTemplateID) {
		fields = append(fields, assignmentpolicy.FieldTemplateID)
	}
	if m.FieldCleared(assignmentpolicy.FieldDescription) {
		fields = append(fields, assignmentpolicy.FieldDescription)
	}
	if m.FieldCleared(assignmentpolicy.FieldLayers) {
		fields = append(fields, assignmentpolicy.FieldLayers)
	}
	if m.FieldCleared(assignmentpolicy.FieldTimeFilters) {
		fields = append(fields, assignmentpolicy.FieldTimeFilters)
	}
	if m.FieldCleared(assignmentpolicy.FieldFilters) {
		fields = append(fields, assignmentpolicy.FieldFilters)
	}
	if m.FieldCleared(assignmentpolicy.FieldUpdatedBy) {
		fields = append(fields, assignmentpolicy.FieldUpdatedBy)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AssignmentPolicyMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AssignmentPolicyMutation) ClearField(name string) error {
	switch name {
	case assignmentpolicy.FieldTemplateID:
		m.ClearTemplateID()
		return nil
	case assignmentpolicy.FieldDescription:
		m.ClearDescription()
		return nil
	case assignmentpolicy.FieldLayers:
		m.ClearLayers()
		return nil
	case assignmentpolicy.FieldTimeFilters:
		m.ClearTimeFilters()
		return nil
	case assignmentpolicy.FieldFilters:
		m.ClearFilters()
		return nil
	case assignmentpolicy.FieldUpdatedBy:
		m.ClearUpdatedBy()
		return nil
	}
	return fmt.Errorf("unknown AssignmentPolicy nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AssignmentPolicyMutation) ResetField(name string) error {
	switch name {
	case assignmentpolicy.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case assignmentpolicy.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case assignmentpolicy.FieldRuleID:
		m.ResetRuleID()
		return nil
	case assignmentpolicy.FieldRuleName:
		m.ResetRuleName()
		return nil
	case assignmentpolicy.FieldTemplateID:
		m.ResetTemplateID()
		return nil
	case assignmentpolicy.FieldDescription:
		m.ResetDescription()
		return nil
	case assignmentpolicy.FieldStatus:
		m.ResetStatus()
		return nil
	case assignmentpolicy.FieldPriority:
		m.ResetPriority()
		return nil
	case assignmentpolicy.FieldLayers:
		m.ResetLayers()
		return nil
	case assignmentpolicy.FieldAggrWindow:
		m.ResetAggrWindow()
		return nil
	case assignmentpolicy.FieldTimeFilters:
		m.ResetTimeFilters()
		return nil
	case assignmentpolicy.FieldFilters:
		m.ResetFilters()
		return nil
	case assignmentpolicy.FieldUpdatedBy:
		m.ResetUpdatedBy()
		return nil
	}
	return fmt.Errorf("unknown AssignmentPolicy field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AssignmentPolicyMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.space != nil {
		edges = append(edges, assignmentpolicy.EdgeSpace)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AssignmentPolicyMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case assignmentpolicy.EdgeSpace:
		if id := m.space; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AssignmentPolicyMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AssignmentPolicyMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AssignmentPolicyMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedspace {
		edges = append(edges, assignmentpolicy.EdgeSpace)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AssignmentPolicyMutation) EdgeCleared(name string) bool {
	switch name {
	case assignmentpolicy.EdgeSpace:
		return m.clearedspace
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AssignmentPolicyMutation) ClearEdge(name string) error {
	switch name {
	case assignmentpolicy.EdgeSpace:
		m.ClearSpace()
		return nil
	}
	return fmt.Errorf("unknown AssignmentPolicy unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AssignmentPolicyMutation) ResetEdge(name string) error {
	switch name {
	case assignmentpolicy.EdgeSpace:
		m.ResetSpace()
		return nil
	}
	return fmt.Errorf("unknown AssignmentPolicy edge %s", name)
}

// EnrichmentMutation represents an operation that mutates the Enrichment nodes in the graph.
type EnrichmentMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uint64
	created_at          *time.Time
	updated_at          *time.Time
	name                *string
	_type               *enrichment.Type
	key                 *string
	value               *string
	condition           *enrichment.Condition
	_config             *map[string]interface{}
	description         *string
	priority            *int
	addpriority         *int
	clearedFields       map[string]struct{}
	integrations        map[uuid.UUID]struct{}
	removedintegrations map[uuid.UUID]struct{}
	clearedintegrations bool
	done                bool
	oldValue            func(context.Context) (*Enrichment, error)
	predicates          []predicate.Enrichment
}

var _ ent.Mutation = (*EnrichmentMutation)(nil)

// enrichmentOption allows management of the mutation configuration using functional options.
type enrichmentOption func(*EnrichmentMutation)

// newEnrichmentMutation creates new mutation for the Enrichment entity.
func newEnrichmentMutation(c config, op Op, opts ...enrichmentOption) *EnrichmentMutation {
	m := &EnrichmentMutation{
		config:        c,
		op:            op,
		typ:           TypeEnrichment,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withEnrichmentID sets the ID field of the mutation.
func withEnrichmentID(id uint64) enrichmentOption {
	return func(m *EnrichmentMutation) {
		var (
			err   error
			once  sync.Once
			value *Enrichment
		)
		m.oldValue = func(ctx context.Context) (*Enrichment, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Enrichment.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withEnrichment sets the old Enrichment of the mutation.
func withEnrichment(node *Enrichment) enrichmentOption {
	return func(m *EnrichmentMutation) {
		m.oldValue = func(context.Context) (*Enrichment, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m EnrichmentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m EnrichmentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Enrichment entities.
func (m *EnrichmentMutation) SetID(id uint64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *EnrichmentMutation) ID() (id uint64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *EnrichmentMutation) IDs(ctx context.Context) ([]uint64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uint64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Enrichment.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *EnrichmentMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *EnrichmentMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *EnrichmentMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *EnrichmentMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *EnrichmentMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *EnrichmentMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetName sets the "name" field.
func (m *EnrichmentMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *EnrichmentMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *EnrichmentMutation) ResetName() {
	m.name = nil
}

// SetType sets the "type" field.
func (m *EnrichmentMutation) SetType(e enrichment.Type) {
	m._type = &e
}

// GetType returns the value of the "type" field in the mutation.
func (m *EnrichmentMutation) GetType() (r enrichment.Type, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldType(ctx context.Context) (v enrichment.Type, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *EnrichmentMutation) ResetType() {
	m._type = nil
}

// SetKey sets the "key" field.
func (m *EnrichmentMutation) SetKey(s string) {
	m.key = &s
}

// Key returns the value of the "key" field in the mutation.
func (m *EnrichmentMutation) Key() (r string, exists bool) {
	v := m.key
	if v == nil {
		return
	}
	return *v, true
}

// OldKey returns the old "key" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldKey(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldKey is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldKey requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldKey: %w", err)
	}
	return oldValue.Key, nil
}

// ClearKey clears the value of the "key" field.
func (m *EnrichmentMutation) ClearKey() {
	m.key = nil
	m.clearedFields[enrichment.FieldKey] = struct{}{}
}

// KeyCleared returns if the "key" field was cleared in this mutation.
func (m *EnrichmentMutation) KeyCleared() bool {
	_, ok := m.clearedFields[enrichment.FieldKey]
	return ok
}

// ResetKey resets all changes to the "key" field.
func (m *EnrichmentMutation) ResetKey() {
	m.key = nil
	delete(m.clearedFields, enrichment.FieldKey)
}

// SetValue sets the "value" field.
func (m *EnrichmentMutation) SetValue(s string) {
	m.value = &s
}

// Value returns the value of the "value" field in the mutation.
func (m *EnrichmentMutation) Value() (r string, exists bool) {
	v := m.value
	if v == nil {
		return
	}
	return *v, true
}

// OldValue returns the old "value" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldValue(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldValue is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldValue requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldValue: %w", err)
	}
	return oldValue.Value, nil
}

// ClearValue clears the value of the "value" field.
func (m *EnrichmentMutation) ClearValue() {
	m.value = nil
	m.clearedFields[enrichment.FieldValue] = struct{}{}
}

// ValueCleared returns if the "value" field was cleared in this mutation.
func (m *EnrichmentMutation) ValueCleared() bool {
	_, ok := m.clearedFields[enrichment.FieldValue]
	return ok
}

// ResetValue resets all changes to the "value" field.
func (m *EnrichmentMutation) ResetValue() {
	m.value = nil
	delete(m.clearedFields, enrichment.FieldValue)
}

// SetCondition sets the "condition" field.
func (m *EnrichmentMutation) SetCondition(e enrichment.Condition) {
	m.condition = &e
}

// Condition returns the value of the "condition" field in the mutation.
func (m *EnrichmentMutation) Condition() (r enrichment.Condition, exists bool) {
	v := m.condition
	if v == nil {
		return
	}
	return *v, true
}

// OldCondition returns the old "condition" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldCondition(ctx context.Context) (v enrichment.Condition, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCondition is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCondition requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCondition: %w", err)
	}
	return oldValue.Condition, nil
}

// ClearCondition clears the value of the "condition" field.
func (m *EnrichmentMutation) ClearCondition() {
	m.condition = nil
	m.clearedFields[enrichment.FieldCondition] = struct{}{}
}

// ConditionCleared returns if the "condition" field was cleared in this mutation.
func (m *EnrichmentMutation) ConditionCleared() bool {
	_, ok := m.clearedFields[enrichment.FieldCondition]
	return ok
}

// ResetCondition resets all changes to the "condition" field.
func (m *EnrichmentMutation) ResetCondition() {
	m.condition = nil
	delete(m.clearedFields, enrichment.FieldCondition)
}

// SetConfig sets the "config" field.
func (m *EnrichmentMutation) SetConfig(value map[string]interface{}) {
	m._config = &value
}

// Config returns the value of the "config" field in the mutation.
func (m *EnrichmentMutation) Config() (r map[string]interface{}, exists bool) {
	v := m._config
	if v == nil {
		return
	}
	return *v, true
}

// OldConfig returns the old "config" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldConfig(ctx context.Context) (v map[string]interface{}, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConfig is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConfig requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConfig: %w", err)
	}
	return oldValue.Config, nil
}

// ClearConfig clears the value of the "config" field.
func (m *EnrichmentMutation) ClearConfig() {
	m._config = nil
	m.clearedFields[enrichment.FieldConfig] = struct{}{}
}

// ConfigCleared returns if the "config" field was cleared in this mutation.
func (m *EnrichmentMutation) ConfigCleared() bool {
	_, ok := m.clearedFields[enrichment.FieldConfig]
	return ok
}

// ResetConfig resets all changes to the "config" field.
func (m *EnrichmentMutation) ResetConfig() {
	m._config = nil
	delete(m.clearedFields, enrichment.FieldConfig)
}

// SetDescription sets the "description" field.
func (m *EnrichmentMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *EnrichmentMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *EnrichmentMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[enrichment.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *EnrichmentMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[enrichment.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *EnrichmentMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, enrichment.FieldDescription)
}

// SetPriority sets the "priority" field.
func (m *EnrichmentMutation) SetPriority(i int) {
	m.priority = &i
	m.addpriority = nil
}

// Priority returns the value of the "priority" field in the mutation.
func (m *EnrichmentMutation) Priority() (r int, exists bool) {
	v := m.priority
	if v == nil {
		return
	}
	return *v, true
}

// OldPriority returns the old "priority" field's value of the Enrichment entity.
// If the Enrichment object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *EnrichmentMutation) OldPriority(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPriority is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPriority requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPriority: %w", err)
	}
	return oldValue.Priority, nil
}

// AddPriority adds i to the "priority" field.
func (m *EnrichmentMutation) AddPriority(i int) {
	if m.addpriority != nil {
		*m.addpriority += i
	} else {
		m.addpriority = &i
	}
}

// AddedPriority returns the value that was added to the "priority" field in this mutation.
func (m *EnrichmentMutation) AddedPriority() (r int, exists bool) {
	v := m.addpriority
	if v == nil {
		return
	}
	return *v, true
}

// ResetPriority resets all changes to the "priority" field.
func (m *EnrichmentMutation) ResetPriority() {
	m.priority = nil
	m.addpriority = nil
}

// AddIntegrationIDs adds the "integrations" edge to the Integration entity by ids.
func (m *EnrichmentMutation) AddIntegrationIDs(ids ...uuid.UUID) {
	if m.integrations == nil {
		m.integrations = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.integrations[ids[i]] = struct{}{}
	}
}

// ClearIntegrations clears the "integrations" edge to the Integration entity.
func (m *EnrichmentMutation) ClearIntegrations() {
	m.clearedintegrations = true
}

// IntegrationsCleared reports if the "integrations" edge to the Integration entity was cleared.
func (m *EnrichmentMutation) IntegrationsCleared() bool {
	return m.clearedintegrations
}

// RemoveIntegrationIDs removes the "integrations" edge to the Integration entity by IDs.
func (m *EnrichmentMutation) RemoveIntegrationIDs(ids ...uuid.UUID) {
	if m.removedintegrations == nil {
		m.removedintegrations = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.integrations, ids[i])
		m.removedintegrations[ids[i]] = struct{}{}
	}
}

// RemovedIntegrations returns the removed IDs of the "integrations" edge to the Integration entity.
func (m *EnrichmentMutation) RemovedIntegrationsIDs() (ids []uuid.UUID) {
	for id := range m.removedintegrations {
		ids = append(ids, id)
	}
	return
}

// IntegrationsIDs returns the "integrations" edge IDs in the mutation.
func (m *EnrichmentMutation) IntegrationsIDs() (ids []uuid.UUID) {
	for id := range m.integrations {
		ids = append(ids, id)
	}
	return
}

// ResetIntegrations resets all changes to the "integrations" edge.
func (m *EnrichmentMutation) ResetIntegrations() {
	m.integrations = nil
	m.clearedintegrations = false
	m.removedintegrations = nil
}

// Where appends a list predicates to the EnrichmentMutation builder.
func (m *EnrichmentMutation) Where(ps ...predicate.Enrichment) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the EnrichmentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *EnrichmentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Enrichment, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *EnrichmentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *EnrichmentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Enrichment).
func (m *EnrichmentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *EnrichmentMutation) Fields() []string {
	fields := make([]string, 0, 10)
	if m.created_at != nil {
		fields = append(fields, enrichment.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, enrichment.FieldUpdatedAt)
	}
	if m.name != nil {
		fields = append(fields, enrichment.FieldName)
	}
	if m._type != nil {
		fields = append(fields, enrichment.FieldType)
	}
	if m.key != nil {
		fields = append(fields, enrichment.FieldKey)
	}
	if m.value != nil {
		fields = append(fields, enrichment.FieldValue)
	}
	if m.condition != nil {
		fields = append(fields, enrichment.FieldCondition)
	}
	if m._config != nil {
		fields = append(fields, enrichment.FieldConfig)
	}
	if m.description != nil {
		fields = append(fields, enrichment.FieldDescription)
	}
	if m.priority != nil {
		fields = append(fields, enrichment.FieldPriority)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *EnrichmentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case enrichment.FieldCreatedAt:
		return m.CreatedAt()
	case enrichment.FieldUpdatedAt:
		return m.UpdatedAt()
	case enrichment.FieldName:
		return m.Name()
	case enrichment.FieldType:
		return m.GetType()
	case enrichment.FieldKey:
		return m.Key()
	case enrichment.FieldValue:
		return m.Value()
	case enrichment.FieldCondition:
		return m.Condition()
	case enrichment.FieldConfig:
		return m.Config()
	case enrichment.FieldDescription:
		return m.Description()
	case enrichment.FieldPriority:
		return m.Priority()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *EnrichmentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case enrichment.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case enrichment.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case enrichment.FieldName:
		return m.OldName(ctx)
	case enrichment.FieldType:
		return m.OldType(ctx)
	case enrichment.FieldKey:
		return m.OldKey(ctx)
	case enrichment.FieldValue:
		return m.OldValue(ctx)
	case enrichment.FieldCondition:
		return m.OldCondition(ctx)
	case enrichment.FieldConfig:
		return m.OldConfig(ctx)
	case enrichment.FieldDescription:
		return m.OldDescription(ctx)
	case enrichment.FieldPriority:
		return m.OldPriority(ctx)
	}
	return nil, fmt.Errorf("unknown Enrichment field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EnrichmentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case enrichment.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case enrichment.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case enrichment.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case enrichment.FieldType:
		v, ok := value.(enrichment.Type)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case enrichment.FieldKey:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetKey(v)
		return nil
	case enrichment.FieldValue:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetValue(v)
		return nil
	case enrichment.FieldCondition:
		v, ok := value.(enrichment.Condition)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCondition(v)
		return nil
	case enrichment.FieldConfig:
		v, ok := value.(map[string]interface{})
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConfig(v)
		return nil
	case enrichment.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case enrichment.FieldPriority:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPriority(v)
		return nil
	}
	return fmt.Errorf("unknown Enrichment field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *EnrichmentMutation) AddedFields() []string {
	var fields []string
	if m.addpriority != nil {
		fields = append(fields, enrichment.FieldPriority)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *EnrichmentMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case enrichment.FieldPriority:
		return m.AddedPriority()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *EnrichmentMutation) AddField(name string, value ent.Value) error {
	switch name {
	case enrichment.FieldPriority:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddPriority(v)
		return nil
	}
	return fmt.Errorf("unknown Enrichment numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *EnrichmentMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(enrichment.FieldKey) {
		fields = append(fields, enrichment.FieldKey)
	}
	if m.FieldCleared(enrichment.FieldValue) {
		fields = append(fields, enrichment.FieldValue)
	}
	if m.FieldCleared(enrichment.FieldCondition) {
		fields = append(fields, enrichment.FieldCondition)
	}
	if m.FieldCleared(enrichment.FieldConfig) {
		fields = append(fields, enrichment.FieldConfig)
	}
	if m.FieldCleared(enrichment.FieldDescription) {
		fields = append(fields, enrichment.FieldDescription)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *EnrichmentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *EnrichmentMutation) ClearField(name string) error {
	switch name {
	case enrichment.FieldKey:
		m.ClearKey()
		return nil
	case enrichment.FieldValue:
		m.ClearValue()
		return nil
	case enrichment.FieldCondition:
		m.ClearCondition()
		return nil
	case enrichment.FieldConfig:
		m.ClearConfig()
		return nil
	case enrichment.FieldDescription:
		m.ClearDescription()
		return nil
	}
	return fmt.Errorf("unknown Enrichment nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *EnrichmentMutation) ResetField(name string) error {
	switch name {
	case enrichment.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case enrichment.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case enrichment.FieldName:
		m.ResetName()
		return nil
	case enrichment.FieldType:
		m.ResetType()
		return nil
	case enrichment.FieldKey:
		m.ResetKey()
		return nil
	case enrichment.FieldValue:
		m.ResetValue()
		return nil
	case enrichment.FieldCondition:
		m.ResetCondition()
		return nil
	case enrichment.FieldConfig:
		m.ResetConfig()
		return nil
	case enrichment.FieldDescription:
		m.ResetDescription()
		return nil
	case enrichment.FieldPriority:
		m.ResetPriority()
		return nil
	}
	return fmt.Errorf("unknown Enrichment field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *EnrichmentMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.integrations != nil {
		edges = append(edges, enrichment.EdgeIntegrations)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *EnrichmentMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case enrichment.EdgeIntegrations:
		ids := make([]ent.Value, 0, len(m.integrations))
		for id := range m.integrations {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *EnrichmentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedintegrations != nil {
		edges = append(edges, enrichment.EdgeIntegrations)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *EnrichmentMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case enrichment.EdgeIntegrations:
		ids := make([]ent.Value, 0, len(m.removedintegrations))
		for id := range m.removedintegrations {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *EnrichmentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedintegrations {
		edges = append(edges, enrichment.EdgeIntegrations)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *EnrichmentMutation) EdgeCleared(name string) bool {
	switch name {
	case enrichment.EdgeIntegrations:
		return m.clearedintegrations
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *EnrichmentMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Enrichment unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *EnrichmentMutation) ResetEdge(name string) error {
	switch name {
	case enrichment.EdgeIntegrations:
		m.ResetIntegrations()
		return nil
	}
	return fmt.Errorf("unknown Enrichment edge %s", name)
}

// IntegrationMutation represents an operation that mutates the Integration nodes in the graph.
type IntegrationMutation struct {
	config
	op                  Op
	typ                 string
	id                  *uuid.UUID
	created_at          *time.Time
	updated_at          *time.Time
	status              *uint8
	addstatus           *int8
	name                *string
	kind                *integration.Kind
	_type               *string
	last_event_time     *string
	webhook             *string
	clearedFields       map[string]struct{}
	enrichments         map[uint64]struct{}
	removedenrichments  map[uint64]struct{}
	clearedenrichments  bool
	alert_rules         map[uint64]struct{}
	removedalert_rules  map[uint64]struct{}
	clearedalert_rules  bool
	alert_routes        map[uint64]struct{}
	removedalert_routes map[uint64]struct{}
	clearedalert_routes bool
	spaces              map[uint64]struct{}
	removedspaces       map[uint64]struct{}
	clearedspaces       bool
	done                bool
	oldValue            func(context.Context) (*Integration, error)
	predicates          []predicate.Integration
}

var _ ent.Mutation = (*IntegrationMutation)(nil)

// integrationOption allows management of the mutation configuration using functional options.
type integrationOption func(*IntegrationMutation)

// newIntegrationMutation creates new mutation for the Integration entity.
func newIntegrationMutation(c config, op Op, opts ...integrationOption) *IntegrationMutation {
	m := &IntegrationMutation{
		config:        c,
		op:            op,
		typ:           TypeIntegration,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withIntegrationID sets the ID field of the mutation.
func withIntegrationID(id uuid.UUID) integrationOption {
	return func(m *IntegrationMutation) {
		var (
			err   error
			once  sync.Once
			value *Integration
		)
		m.oldValue = func(ctx context.Context) (*Integration, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Integration.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withIntegration sets the old Integration of the mutation.
func withIntegration(node *Integration) integrationOption {
	return func(m *IntegrationMutation) {
		m.oldValue = func(context.Context) (*Integration, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m IntegrationMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m IntegrationMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Integration entities.
func (m *IntegrationMutation) SetID(id uuid.UUID) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *IntegrationMutation) ID() (id uuid.UUID, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *IntegrationMutation) IDs(ctx context.Context) ([]uuid.UUID, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uuid.UUID{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Integration.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *IntegrationMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *IntegrationMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Integration entity.
// If the Integration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IntegrationMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *IntegrationMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *IntegrationMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *IntegrationMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Integration entity.
// If the Integration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IntegrationMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *IntegrationMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetStatus sets the "status" field.
func (m *IntegrationMutation) SetStatus(u uint8) {
	m.status = &u
	m.addstatus = nil
}

// Status returns the value of the "status" field in the mutation.
func (m *IntegrationMutation) Status() (r uint8, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Integration entity.
// If the Integration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IntegrationMutation) OldStatus(ctx context.Context) (v uint8, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// AddStatus adds u to the "status" field.
func (m *IntegrationMutation) AddStatus(u int8) {
	if m.addstatus != nil {
		*m.addstatus += u
	} else {
		m.addstatus = &u
	}
}

// AddedStatus returns the value that was added to the "status" field in this mutation.
func (m *IntegrationMutation) AddedStatus() (r int8, exists bool) {
	v := m.addstatus
	if v == nil {
		return
	}
	return *v, true
}

// ClearStatus clears the value of the "status" field.
func (m *IntegrationMutation) ClearStatus() {
	m.status = nil
	m.addstatus = nil
	m.clearedFields[integration.FieldStatus] = struct{}{}
}

// StatusCleared returns if the "status" field was cleared in this mutation.
func (m *IntegrationMutation) StatusCleared() bool {
	_, ok := m.clearedFields[integration.FieldStatus]
	return ok
}

// ResetStatus resets all changes to the "status" field.
func (m *IntegrationMutation) ResetStatus() {
	m.status = nil
	m.addstatus = nil
	delete(m.clearedFields, integration.FieldStatus)
}

// SetName sets the "name" field.
func (m *IntegrationMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *IntegrationMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Integration entity.
// If the Integration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IntegrationMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *IntegrationMutation) ResetName() {
	m.name = nil
}

// SetKind sets the "kind" field.
func (m *IntegrationMutation) SetKind(i integration.Kind) {
	m.kind = &i
}

// Kind returns the value of the "kind" field in the mutation.
func (m *IntegrationMutation) Kind() (r integration.Kind, exists bool) {
	v := m.kind
	if v == nil {
		return
	}
	return *v, true
}

// OldKind returns the old "kind" field's value of the Integration entity.
// If the Integration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IntegrationMutation) OldKind(ctx context.Context) (v integration.Kind, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldKind is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldKind requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldKind: %w", err)
	}
	return oldValue.Kind, nil
}

// ResetKind resets all changes to the "kind" field.
func (m *IntegrationMutation) ResetKind() {
	m.kind = nil
}

// SetType sets the "type" field.
func (m *IntegrationMutation) SetType(s string) {
	m._type = &s
}

// GetType returns the value of the "type" field in the mutation.
func (m *IntegrationMutation) GetType() (r string, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Integration entity.
// If the Integration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IntegrationMutation) OldType(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// ResetType resets all changes to the "type" field.
func (m *IntegrationMutation) ResetType() {
	m._type = nil
}

// SetLastEventTime sets the "last_event_time" field.
func (m *IntegrationMutation) SetLastEventTime(s string) {
	m.last_event_time = &s
}

// LastEventTime returns the value of the "last_event_time" field in the mutation.
func (m *IntegrationMutation) LastEventTime() (r string, exists bool) {
	v := m.last_event_time
	if v == nil {
		return
	}
	return *v, true
}

// OldLastEventTime returns the old "last_event_time" field's value of the Integration entity.
// If the Integration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IntegrationMutation) OldLastEventTime(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldLastEventTime is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldLastEventTime requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldLastEventTime: %w", err)
	}
	return oldValue.LastEventTime, nil
}

// ClearLastEventTime clears the value of the "last_event_time" field.
func (m *IntegrationMutation) ClearLastEventTime() {
	m.last_event_time = nil
	m.clearedFields[integration.FieldLastEventTime] = struct{}{}
}

// LastEventTimeCleared returns if the "last_event_time" field was cleared in this mutation.
func (m *IntegrationMutation) LastEventTimeCleared() bool {
	_, ok := m.clearedFields[integration.FieldLastEventTime]
	return ok
}

// ResetLastEventTime resets all changes to the "last_event_time" field.
func (m *IntegrationMutation) ResetLastEventTime() {
	m.last_event_time = nil
	delete(m.clearedFields, integration.FieldLastEventTime)
}

// SetWebhook sets the "webhook" field.
func (m *IntegrationMutation) SetWebhook(s string) {
	m.webhook = &s
}

// Webhook returns the value of the "webhook" field in the mutation.
func (m *IntegrationMutation) Webhook() (r string, exists bool) {
	v := m.webhook
	if v == nil {
		return
	}
	return *v, true
}

// OldWebhook returns the old "webhook" field's value of the Integration entity.
// If the Integration object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *IntegrationMutation) OldWebhook(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldWebhook is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldWebhook requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldWebhook: %w", err)
	}
	return oldValue.Webhook, nil
}

// ClearWebhook clears the value of the "webhook" field.
func (m *IntegrationMutation) ClearWebhook() {
	m.webhook = nil
	m.clearedFields[integration.FieldWebhook] = struct{}{}
}

// WebhookCleared returns if the "webhook" field was cleared in this mutation.
func (m *IntegrationMutation) WebhookCleared() bool {
	_, ok := m.clearedFields[integration.FieldWebhook]
	return ok
}

// ResetWebhook resets all changes to the "webhook" field.
func (m *IntegrationMutation) ResetWebhook() {
	m.webhook = nil
	delete(m.clearedFields, integration.FieldWebhook)
}

// AddEnrichmentIDs adds the "enrichments" edge to the Enrichment entity by ids.
func (m *IntegrationMutation) AddEnrichmentIDs(ids ...uint64) {
	if m.enrichments == nil {
		m.enrichments = make(map[uint64]struct{})
	}
	for i := range ids {
		m.enrichments[ids[i]] = struct{}{}
	}
}

// ClearEnrichments clears the "enrichments" edge to the Enrichment entity.
func (m *IntegrationMutation) ClearEnrichments() {
	m.clearedenrichments = true
}

// EnrichmentsCleared reports if the "enrichments" edge to the Enrichment entity was cleared.
func (m *IntegrationMutation) EnrichmentsCleared() bool {
	return m.clearedenrichments
}

// RemoveEnrichmentIDs removes the "enrichments" edge to the Enrichment entity by IDs.
func (m *IntegrationMutation) RemoveEnrichmentIDs(ids ...uint64) {
	if m.removedenrichments == nil {
		m.removedenrichments = make(map[uint64]struct{})
	}
	for i := range ids {
		delete(m.enrichments, ids[i])
		m.removedenrichments[ids[i]] = struct{}{}
	}
}

// RemovedEnrichments returns the removed IDs of the "enrichments" edge to the Enrichment entity.
func (m *IntegrationMutation) RemovedEnrichmentsIDs() (ids []uint64) {
	for id := range m.removedenrichments {
		ids = append(ids, id)
	}
	return
}

// EnrichmentsIDs returns the "enrichments" edge IDs in the mutation.
func (m *IntegrationMutation) EnrichmentsIDs() (ids []uint64) {
	for id := range m.enrichments {
		ids = append(ids, id)
	}
	return
}

// ResetEnrichments resets all changes to the "enrichments" edge.
func (m *IntegrationMutation) ResetEnrichments() {
	m.enrichments = nil
	m.clearedenrichments = false
	m.removedenrichments = nil
}

// AddAlertRuleIDs adds the "alert_rules" edge to the AlertRule entity by ids.
func (m *IntegrationMutation) AddAlertRuleIDs(ids ...uint64) {
	if m.alert_rules == nil {
		m.alert_rules = make(map[uint64]struct{})
	}
	for i := range ids {
		m.alert_rules[ids[i]] = struct{}{}
	}
}

// ClearAlertRules clears the "alert_rules" edge to the AlertRule entity.
func (m *IntegrationMutation) ClearAlertRules() {
	m.clearedalert_rules = true
}

// AlertRulesCleared reports if the "alert_rules" edge to the AlertRule entity was cleared.
func (m *IntegrationMutation) AlertRulesCleared() bool {
	return m.clearedalert_rules
}

// RemoveAlertRuleIDs removes the "alert_rules" edge to the AlertRule entity by IDs.
func (m *IntegrationMutation) RemoveAlertRuleIDs(ids ...uint64) {
	if m.removedalert_rules == nil {
		m.removedalert_rules = make(map[uint64]struct{})
	}
	for i := range ids {
		delete(m.alert_rules, ids[i])
		m.removedalert_rules[ids[i]] = struct{}{}
	}
}

// RemovedAlertRules returns the removed IDs of the "alert_rules" edge to the AlertRule entity.
func (m *IntegrationMutation) RemovedAlertRulesIDs() (ids []uint64) {
	for id := range m.removedalert_rules {
		ids = append(ids, id)
	}
	return
}

// AlertRulesIDs returns the "alert_rules" edge IDs in the mutation.
func (m *IntegrationMutation) AlertRulesIDs() (ids []uint64) {
	for id := range m.alert_rules {
		ids = append(ids, id)
	}
	return
}

// ResetAlertRules resets all changes to the "alert_rules" edge.
func (m *IntegrationMutation) ResetAlertRules() {
	m.alert_rules = nil
	m.clearedalert_rules = false
	m.removedalert_rules = nil
}

// AddAlertRouteIDs adds the "alert_routes" edge to the AlertRoutes entity by ids.
func (m *IntegrationMutation) AddAlertRouteIDs(ids ...uint64) {
	if m.alert_routes == nil {
		m.alert_routes = make(map[uint64]struct{})
	}
	for i := range ids {
		m.alert_routes[ids[i]] = struct{}{}
	}
}

// ClearAlertRoutes clears the "alert_routes" edge to the AlertRoutes entity.
func (m *IntegrationMutation) ClearAlertRoutes() {
	m.clearedalert_routes = true
}

// AlertRoutesCleared reports if the "alert_routes" edge to the AlertRoutes entity was cleared.
func (m *IntegrationMutation) AlertRoutesCleared() bool {
	return m.clearedalert_routes
}

// RemoveAlertRouteIDs removes the "alert_routes" edge to the AlertRoutes entity by IDs.
func (m *IntegrationMutation) RemoveAlertRouteIDs(ids ...uint64) {
	if m.removedalert_routes == nil {
		m.removedalert_routes = make(map[uint64]struct{})
	}
	for i := range ids {
		delete(m.alert_routes, ids[i])
		m.removedalert_routes[ids[i]] = struct{}{}
	}
}

// RemovedAlertRoutes returns the removed IDs of the "alert_routes" edge to the AlertRoutes entity.
func (m *IntegrationMutation) RemovedAlertRoutesIDs() (ids []uint64) {
	for id := range m.removedalert_routes {
		ids = append(ids, id)
	}
	return
}

// AlertRoutesIDs returns the "alert_routes" edge IDs in the mutation.
func (m *IntegrationMutation) AlertRoutesIDs() (ids []uint64) {
	for id := range m.alert_routes {
		ids = append(ids, id)
	}
	return
}

// ResetAlertRoutes resets all changes to the "alert_routes" edge.
func (m *IntegrationMutation) ResetAlertRoutes() {
	m.alert_routes = nil
	m.clearedalert_routes = false
	m.removedalert_routes = nil
}

// AddSpaceIDs adds the "spaces" edge to the Space entity by ids.
func (m *IntegrationMutation) AddSpaceIDs(ids ...uint64) {
	if m.spaces == nil {
		m.spaces = make(map[uint64]struct{})
	}
	for i := range ids {
		m.spaces[ids[i]] = struct{}{}
	}
}

// ClearSpaces clears the "spaces" edge to the Space entity.
func (m *IntegrationMutation) ClearSpaces() {
	m.clearedspaces = true
}

// SpacesCleared reports if the "spaces" edge to the Space entity was cleared.
func (m *IntegrationMutation) SpacesCleared() bool {
	return m.clearedspaces
}

// RemoveSpaceIDs removes the "spaces" edge to the Space entity by IDs.
func (m *IntegrationMutation) RemoveSpaceIDs(ids ...uint64) {
	if m.removedspaces == nil {
		m.removedspaces = make(map[uint64]struct{})
	}
	for i := range ids {
		delete(m.spaces, ids[i])
		m.removedspaces[ids[i]] = struct{}{}
	}
}

// RemovedSpaces returns the removed IDs of the "spaces" edge to the Space entity.
func (m *IntegrationMutation) RemovedSpacesIDs() (ids []uint64) {
	for id := range m.removedspaces {
		ids = append(ids, id)
	}
	return
}

// SpacesIDs returns the "spaces" edge IDs in the mutation.
func (m *IntegrationMutation) SpacesIDs() (ids []uint64) {
	for id := range m.spaces {
		ids = append(ids, id)
	}
	return
}

// ResetSpaces resets all changes to the "spaces" edge.
func (m *IntegrationMutation) ResetSpaces() {
	m.spaces = nil
	m.clearedspaces = false
	m.removedspaces = nil
}

// Where appends a list predicates to the IntegrationMutation builder.
func (m *IntegrationMutation) Where(ps ...predicate.Integration) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the IntegrationMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *IntegrationMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Integration, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *IntegrationMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *IntegrationMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Integration).
func (m *IntegrationMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *IntegrationMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.created_at != nil {
		fields = append(fields, integration.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, integration.FieldUpdatedAt)
	}
	if m.status != nil {
		fields = append(fields, integration.FieldStatus)
	}
	if m.name != nil {
		fields = append(fields, integration.FieldName)
	}
	if m.kind != nil {
		fields = append(fields, integration.FieldKind)
	}
	if m._type != nil {
		fields = append(fields, integration.FieldType)
	}
	if m.last_event_time != nil {
		fields = append(fields, integration.FieldLastEventTime)
	}
	if m.webhook != nil {
		fields = append(fields, integration.FieldWebhook)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *IntegrationMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case integration.FieldCreatedAt:
		return m.CreatedAt()
	case integration.FieldUpdatedAt:
		return m.UpdatedAt()
	case integration.FieldStatus:
		return m.Status()
	case integration.FieldName:
		return m.Name()
	case integration.FieldKind:
		return m.Kind()
	case integration.FieldType:
		return m.GetType()
	case integration.FieldLastEventTime:
		return m.LastEventTime()
	case integration.FieldWebhook:
		return m.Webhook()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *IntegrationMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case integration.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case integration.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case integration.FieldStatus:
		return m.OldStatus(ctx)
	case integration.FieldName:
		return m.OldName(ctx)
	case integration.FieldKind:
		return m.OldKind(ctx)
	case integration.FieldType:
		return m.OldType(ctx)
	case integration.FieldLastEventTime:
		return m.OldLastEventTime(ctx)
	case integration.FieldWebhook:
		return m.OldWebhook(ctx)
	}
	return nil, fmt.Errorf("unknown Integration field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *IntegrationMutation) SetField(name string, value ent.Value) error {
	switch name {
	case integration.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case integration.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case integration.FieldStatus:
		v, ok := value.(uint8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case integration.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case integration.FieldKind:
		v, ok := value.(integration.Kind)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetKind(v)
		return nil
	case integration.FieldType:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case integration.FieldLastEventTime:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetLastEventTime(v)
		return nil
	case integration.FieldWebhook:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetWebhook(v)
		return nil
	}
	return fmt.Errorf("unknown Integration field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *IntegrationMutation) AddedFields() []string {
	var fields []string
	if m.addstatus != nil {
		fields = append(fields, integration.FieldStatus)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *IntegrationMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case integration.FieldStatus:
		return m.AddedStatus()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *IntegrationMutation) AddField(name string, value ent.Value) error {
	switch name {
	case integration.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStatus(v)
		return nil
	}
	return fmt.Errorf("unknown Integration numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *IntegrationMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(integration.FieldStatus) {
		fields = append(fields, integration.FieldStatus)
	}
	if m.FieldCleared(integration.FieldLastEventTime) {
		fields = append(fields, integration.FieldLastEventTime)
	}
	if m.FieldCleared(integration.FieldWebhook) {
		fields = append(fields, integration.FieldWebhook)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *IntegrationMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *IntegrationMutation) ClearField(name string) error {
	switch name {
	case integration.FieldStatus:
		m.ClearStatus()
		return nil
	case integration.FieldLastEventTime:
		m.ClearLastEventTime()
		return nil
	case integration.FieldWebhook:
		m.ClearWebhook()
		return nil
	}
	return fmt.Errorf("unknown Integration nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *IntegrationMutation) ResetField(name string) error {
	switch name {
	case integration.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case integration.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case integration.FieldStatus:
		m.ResetStatus()
		return nil
	case integration.FieldName:
		m.ResetName()
		return nil
	case integration.FieldKind:
		m.ResetKind()
		return nil
	case integration.FieldType:
		m.ResetType()
		return nil
	case integration.FieldLastEventTime:
		m.ResetLastEventTime()
		return nil
	case integration.FieldWebhook:
		m.ResetWebhook()
		return nil
	}
	return fmt.Errorf("unknown Integration field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *IntegrationMutation) AddedEdges() []string {
	edges := make([]string, 0, 4)
	if m.enrichments != nil {
		edges = append(edges, integration.EdgeEnrichments)
	}
	if m.alert_rules != nil {
		edges = append(edges, integration.EdgeAlertRules)
	}
	if m.alert_routes != nil {
		edges = append(edges, integration.EdgeAlertRoutes)
	}
	if m.spaces != nil {
		edges = append(edges, integration.EdgeSpaces)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *IntegrationMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case integration.EdgeEnrichments:
		ids := make([]ent.Value, 0, len(m.enrichments))
		for id := range m.enrichments {
			ids = append(ids, id)
		}
		return ids
	case integration.EdgeAlertRules:
		ids := make([]ent.Value, 0, len(m.alert_rules))
		for id := range m.alert_rules {
			ids = append(ids, id)
		}
		return ids
	case integration.EdgeAlertRoutes:
		ids := make([]ent.Value, 0, len(m.alert_routes))
		for id := range m.alert_routes {
			ids = append(ids, id)
		}
		return ids
	case integration.EdgeSpaces:
		ids := make([]ent.Value, 0, len(m.spaces))
		for id := range m.spaces {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *IntegrationMutation) RemovedEdges() []string {
	edges := make([]string, 0, 4)
	if m.removedenrichments != nil {
		edges = append(edges, integration.EdgeEnrichments)
	}
	if m.removedalert_rules != nil {
		edges = append(edges, integration.EdgeAlertRules)
	}
	if m.removedalert_routes != nil {
		edges = append(edges, integration.EdgeAlertRoutes)
	}
	if m.removedspaces != nil {
		edges = append(edges, integration.EdgeSpaces)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *IntegrationMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case integration.EdgeEnrichments:
		ids := make([]ent.Value, 0, len(m.removedenrichments))
		for id := range m.removedenrichments {
			ids = append(ids, id)
		}
		return ids
	case integration.EdgeAlertRules:
		ids := make([]ent.Value, 0, len(m.removedalert_rules))
		for id := range m.removedalert_rules {
			ids = append(ids, id)
		}
		return ids
	case integration.EdgeAlertRoutes:
		ids := make([]ent.Value, 0, len(m.removedalert_routes))
		for id := range m.removedalert_routes {
			ids = append(ids, id)
		}
		return ids
	case integration.EdgeSpaces:
		ids := make([]ent.Value, 0, len(m.removedspaces))
		for id := range m.removedspaces {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *IntegrationMutation) ClearedEdges() []string {
	edges := make([]string, 0, 4)
	if m.clearedenrichments {
		edges = append(edges, integration.EdgeEnrichments)
	}
	if m.clearedalert_rules {
		edges = append(edges, integration.EdgeAlertRules)
	}
	if m.clearedalert_routes {
		edges = append(edges, integration.EdgeAlertRoutes)
	}
	if m.clearedspaces {
		edges = append(edges, integration.EdgeSpaces)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *IntegrationMutation) EdgeCleared(name string) bool {
	switch name {
	case integration.EdgeEnrichments:
		return m.clearedenrichments
	case integration.EdgeAlertRules:
		return m.clearedalert_rules
	case integration.EdgeAlertRoutes:
		return m.clearedalert_routes
	case integration.EdgeSpaces:
		return m.clearedspaces
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *IntegrationMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Integration unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *IntegrationMutation) ResetEdge(name string) error {
	switch name {
	case integration.EdgeEnrichments:
		m.ResetEnrichments()
		return nil
	case integration.EdgeAlertRules:
		m.ResetAlertRules()
		return nil
	case integration.EdgeAlertRoutes:
		m.ResetAlertRoutes()
		return nil
	case integration.EdgeSpaces:
		m.ResetSpaces()
		return nil
	}
	return fmt.Errorf("unknown Integration edge %s", name)
}

// SpaceMutation represents an operation that mutates the Space nodes in the graph.
type SpaceMutation struct {
	config
	op                         Op
	typ                        string
	id                         *uint64
	created_at                 *time.Time
	updated_at                 *time.Time
	name                       *string
	team_id                    *int64
	addteam_id                 *int64
	team_name                  *string
	description                *string
	visibility                 *space.Visibility
	enabled                    *bool
	clearedFields              map[string]struct{}
	integrations               map[uuid.UUID]struct{}
	removedintegrations        map[uuid.UUID]struct{}
	clearedintegrations        bool
	alertroutes                map[uint64]struct{}
	removedalertroutes         map[uint64]struct{}
	clearedalertroutes         bool
	assignment_policies        map[uuid.UUID]struct{}
	removedassignment_policies map[uuid.UUID]struct{}
	clearedassignment_policies bool
	done                       bool
	oldValue                   func(context.Context) (*Space, error)
	predicates                 []predicate.Space
}

var _ ent.Mutation = (*SpaceMutation)(nil)

// spaceOption allows management of the mutation configuration using functional options.
type spaceOption func(*SpaceMutation)

// newSpaceMutation creates new mutation for the Space entity.
func newSpaceMutation(c config, op Op, opts ...spaceOption) *SpaceMutation {
	m := &SpaceMutation{
		config:        c,
		op:            op,
		typ:           TypeSpace,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withSpaceID sets the ID field of the mutation.
func withSpaceID(id uint64) spaceOption {
	return func(m *SpaceMutation) {
		var (
			err   error
			once  sync.Once
			value *Space
		)
		m.oldValue = func(ctx context.Context) (*Space, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Space.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withSpace sets the old Space of the mutation.
func withSpace(node *Space) spaceOption {
	return func(m *SpaceMutation) {
		m.oldValue = func(context.Context) (*Space, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m SpaceMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m SpaceMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// SetID sets the value of the id field. Note that this
// operation is only accepted on creation of Space entities.
func (m *SpaceMutation) SetID(id uint64) {
	m.id = &id
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *SpaceMutation) ID() (id uint64, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *SpaceMutation) IDs(ctx context.Context) ([]uint64, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []uint64{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Space.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *SpaceMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *SpaceMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Space entity.
// If the Space object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SpaceMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *SpaceMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *SpaceMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *SpaceMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Space entity.
// If the Space object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SpaceMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *SpaceMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetName sets the "name" field.
func (m *SpaceMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *SpaceMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Space entity.
// If the Space object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SpaceMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *SpaceMutation) ResetName() {
	m.name = nil
}

// SetTeamID sets the "team_id" field.
func (m *SpaceMutation) SetTeamID(i int64) {
	m.team_id = &i
	m.addteam_id = nil
}

// TeamID returns the value of the "team_id" field in the mutation.
func (m *SpaceMutation) TeamID() (r int64, exists bool) {
	v := m.team_id
	if v == nil {
		return
	}
	return *v, true
}

// OldTeamID returns the old "team_id" field's value of the Space entity.
// If the Space object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SpaceMutation) OldTeamID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTeamID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTeamID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTeamID: %w", err)
	}
	return oldValue.TeamID, nil
}

// AddTeamID adds i to the "team_id" field.
func (m *SpaceMutation) AddTeamID(i int64) {
	if m.addteam_id != nil {
		*m.addteam_id += i
	} else {
		m.addteam_id = &i
	}
}

// AddedTeamID returns the value that was added to the "team_id" field in this mutation.
func (m *SpaceMutation) AddedTeamID() (r int64, exists bool) {
	v := m.addteam_id
	if v == nil {
		return
	}
	return *v, true
}

// ClearTeamID clears the value of the "team_id" field.
func (m *SpaceMutation) ClearTeamID() {
	m.team_id = nil
	m.addteam_id = nil
	m.clearedFields[space.FieldTeamID] = struct{}{}
}

// TeamIDCleared returns if the "team_id" field was cleared in this mutation.
func (m *SpaceMutation) TeamIDCleared() bool {
	_, ok := m.clearedFields[space.FieldTeamID]
	return ok
}

// ResetTeamID resets all changes to the "team_id" field.
func (m *SpaceMutation) ResetTeamID() {
	m.team_id = nil
	m.addteam_id = nil
	delete(m.clearedFields, space.FieldTeamID)
}

// SetTeamName sets the "team_name" field.
func (m *SpaceMutation) SetTeamName(s string) {
	m.team_name = &s
}

// TeamName returns the value of the "team_name" field in the mutation.
func (m *SpaceMutation) TeamName() (r string, exists bool) {
	v := m.team_name
	if v == nil {
		return
	}
	return *v, true
}

// OldTeamName returns the old "team_name" field's value of the Space entity.
// If the Space object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SpaceMutation) OldTeamName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTeamName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTeamName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTeamName: %w", err)
	}
	return oldValue.TeamName, nil
}

// ClearTeamName clears the value of the "team_name" field.
func (m *SpaceMutation) ClearTeamName() {
	m.team_name = nil
	m.clearedFields[space.FieldTeamName] = struct{}{}
}

// TeamNameCleared returns if the "team_name" field was cleared in this mutation.
func (m *SpaceMutation) TeamNameCleared() bool {
	_, ok := m.clearedFields[space.FieldTeamName]
	return ok
}

// ResetTeamName resets all changes to the "team_name" field.
func (m *SpaceMutation) ResetTeamName() {
	m.team_name = nil
	delete(m.clearedFields, space.FieldTeamName)
}

// SetDescription sets the "description" field.
func (m *SpaceMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *SpaceMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the Space entity.
// If the Space object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SpaceMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ResetDescription resets all changes to the "description" field.
func (m *SpaceMutation) ResetDescription() {
	m.description = nil
}

// SetVisibility sets the "visibility" field.
func (m *SpaceMutation) SetVisibility(s space.Visibility) {
	m.visibility = &s
}

// Visibility returns the value of the "visibility" field in the mutation.
func (m *SpaceMutation) Visibility() (r space.Visibility, exists bool) {
	v := m.visibility
	if v == nil {
		return
	}
	return *v, true
}

// OldVisibility returns the old "visibility" field's value of the Space entity.
// If the Space object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SpaceMutation) OldVisibility(ctx context.Context) (v space.Visibility, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldVisibility is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldVisibility requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldVisibility: %w", err)
	}
	return oldValue.Visibility, nil
}

// ResetVisibility resets all changes to the "visibility" field.
func (m *SpaceMutation) ResetVisibility() {
	m.visibility = nil
}

// SetEnabled sets the "enabled" field.
func (m *SpaceMutation) SetEnabled(b bool) {
	m.enabled = &b
}

// Enabled returns the value of the "enabled" field in the mutation.
func (m *SpaceMutation) Enabled() (r bool, exists bool) {
	v := m.enabled
	if v == nil {
		return
	}
	return *v, true
}

// OldEnabled returns the old "enabled" field's value of the Space entity.
// If the Space object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *SpaceMutation) OldEnabled(ctx context.Context) (v bool, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEnabled is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEnabled requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEnabled: %w", err)
	}
	return oldValue.Enabled, nil
}

// ResetEnabled resets all changes to the "enabled" field.
func (m *SpaceMutation) ResetEnabled() {
	m.enabled = nil
}

// AddIntegrationIDs adds the "integrations" edge to the Integration entity by ids.
func (m *SpaceMutation) AddIntegrationIDs(ids ...uuid.UUID) {
	if m.integrations == nil {
		m.integrations = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.integrations[ids[i]] = struct{}{}
	}
}

// ClearIntegrations clears the "integrations" edge to the Integration entity.
func (m *SpaceMutation) ClearIntegrations() {
	m.clearedintegrations = true
}

// IntegrationsCleared reports if the "integrations" edge to the Integration entity was cleared.
func (m *SpaceMutation) IntegrationsCleared() bool {
	return m.clearedintegrations
}

// RemoveIntegrationIDs removes the "integrations" edge to the Integration entity by IDs.
func (m *SpaceMutation) RemoveIntegrationIDs(ids ...uuid.UUID) {
	if m.removedintegrations == nil {
		m.removedintegrations = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.integrations, ids[i])
		m.removedintegrations[ids[i]] = struct{}{}
	}
}

// RemovedIntegrations returns the removed IDs of the "integrations" edge to the Integration entity.
func (m *SpaceMutation) RemovedIntegrationsIDs() (ids []uuid.UUID) {
	for id := range m.removedintegrations {
		ids = append(ids, id)
	}
	return
}

// IntegrationsIDs returns the "integrations" edge IDs in the mutation.
func (m *SpaceMutation) IntegrationsIDs() (ids []uuid.UUID) {
	for id := range m.integrations {
		ids = append(ids, id)
	}
	return
}

// ResetIntegrations resets all changes to the "integrations" edge.
func (m *SpaceMutation) ResetIntegrations() {
	m.integrations = nil
	m.clearedintegrations = false
	m.removedintegrations = nil
}

// AddAlertrouteIDs adds the "alertroutes" edge to the AlertRoutes entity by ids.
func (m *SpaceMutation) AddAlertrouteIDs(ids ...uint64) {
	if m.alertroutes == nil {
		m.alertroutes = make(map[uint64]struct{})
	}
	for i := range ids {
		m.alertroutes[ids[i]] = struct{}{}
	}
}

// ClearAlertroutes clears the "alertroutes" edge to the AlertRoutes entity.
func (m *SpaceMutation) ClearAlertroutes() {
	m.clearedalertroutes = true
}

// AlertroutesCleared reports if the "alertroutes" edge to the AlertRoutes entity was cleared.
func (m *SpaceMutation) AlertroutesCleared() bool {
	return m.clearedalertroutes
}

// RemoveAlertrouteIDs removes the "alertroutes" edge to the AlertRoutes entity by IDs.
func (m *SpaceMutation) RemoveAlertrouteIDs(ids ...uint64) {
	if m.removedalertroutes == nil {
		m.removedalertroutes = make(map[uint64]struct{})
	}
	for i := range ids {
		delete(m.alertroutes, ids[i])
		m.removedalertroutes[ids[i]] = struct{}{}
	}
}

// RemovedAlertroutes returns the removed IDs of the "alertroutes" edge to the AlertRoutes entity.
func (m *SpaceMutation) RemovedAlertroutesIDs() (ids []uint64) {
	for id := range m.removedalertroutes {
		ids = append(ids, id)
	}
	return
}

// AlertroutesIDs returns the "alertroutes" edge IDs in the mutation.
func (m *SpaceMutation) AlertroutesIDs() (ids []uint64) {
	for id := range m.alertroutes {
		ids = append(ids, id)
	}
	return
}

// ResetAlertroutes resets all changes to the "alertroutes" edge.
func (m *SpaceMutation) ResetAlertroutes() {
	m.alertroutes = nil
	m.clearedalertroutes = false
	m.removedalertroutes = nil
}

// AddAssignmentPolicyIDs adds the "assignment_policies" edge to the AssignmentPolicy entity by ids.
func (m *SpaceMutation) AddAssignmentPolicyIDs(ids ...uuid.UUID) {
	if m.assignment_policies == nil {
		m.assignment_policies = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		m.assignment_policies[ids[i]] = struct{}{}
	}
}

// ClearAssignmentPolicies clears the "assignment_policies" edge to the AssignmentPolicy entity.
func (m *SpaceMutation) ClearAssignmentPolicies() {
	m.clearedassignment_policies = true
}

// AssignmentPoliciesCleared reports if the "assignment_policies" edge to the AssignmentPolicy entity was cleared.
func (m *SpaceMutation) AssignmentPoliciesCleared() bool {
	return m.clearedassignment_policies
}

// RemoveAssignmentPolicyIDs removes the "assignment_policies" edge to the AssignmentPolicy entity by IDs.
func (m *SpaceMutation) RemoveAssignmentPolicyIDs(ids ...uuid.UUID) {
	if m.removedassignment_policies == nil {
		m.removedassignment_policies = make(map[uuid.UUID]struct{})
	}
	for i := range ids {
		delete(m.assignment_policies, ids[i])
		m.removedassignment_policies[ids[i]] = struct{}{}
	}
}

// RemovedAssignmentPolicies returns the removed IDs of the "assignment_policies" edge to the AssignmentPolicy entity.
func (m *SpaceMutation) RemovedAssignmentPoliciesIDs() (ids []uuid.UUID) {
	for id := range m.removedassignment_policies {
		ids = append(ids, id)
	}
	return
}

// AssignmentPoliciesIDs returns the "assignment_policies" edge IDs in the mutation.
func (m *SpaceMutation) AssignmentPoliciesIDs() (ids []uuid.UUID) {
	for id := range m.assignment_policies {
		ids = append(ids, id)
	}
	return
}

// ResetAssignmentPolicies resets all changes to the "assignment_policies" edge.
func (m *SpaceMutation) ResetAssignmentPolicies() {
	m.assignment_policies = nil
	m.clearedassignment_policies = false
	m.removedassignment_policies = nil
}

// Where appends a list predicates to the SpaceMutation builder.
func (m *SpaceMutation) Where(ps ...predicate.Space) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the SpaceMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *SpaceMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Space, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *SpaceMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *SpaceMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Space).
func (m *SpaceMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *SpaceMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.created_at != nil {
		fields = append(fields, space.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, space.FieldUpdatedAt)
	}
	if m.name != nil {
		fields = append(fields, space.FieldName)
	}
	if m.team_id != nil {
		fields = append(fields, space.FieldTeamID)
	}
	if m.team_name != nil {
		fields = append(fields, space.FieldTeamName)
	}
	if m.description != nil {
		fields = append(fields, space.FieldDescription)
	}
	if m.visibility != nil {
		fields = append(fields, space.FieldVisibility)
	}
	if m.enabled != nil {
		fields = append(fields, space.FieldEnabled)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *SpaceMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case space.FieldCreatedAt:
		return m.CreatedAt()
	case space.FieldUpdatedAt:
		return m.UpdatedAt()
	case space.FieldName:
		return m.Name()
	case space.FieldTeamID:
		return m.TeamID()
	case space.FieldTeamName:
		return m.TeamName()
	case space.FieldDescription:
		return m.Description()
	case space.FieldVisibility:
		return m.Visibility()
	case space.FieldEnabled:
		return m.Enabled()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *SpaceMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case space.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case space.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case space.FieldName:
		return m.OldName(ctx)
	case space.FieldTeamID:
		return m.OldTeamID(ctx)
	case space.FieldTeamName:
		return m.OldTeamName(ctx)
	case space.FieldDescription:
		return m.OldDescription(ctx)
	case space.FieldVisibility:
		return m.OldVisibility(ctx)
	case space.FieldEnabled:
		return m.OldEnabled(ctx)
	}
	return nil, fmt.Errorf("unknown Space field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SpaceMutation) SetField(name string, value ent.Value) error {
	switch name {
	case space.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case space.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case space.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case space.FieldTeamID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTeamID(v)
		return nil
	case space.FieldTeamName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTeamName(v)
		return nil
	case space.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case space.FieldVisibility:
		v, ok := value.(space.Visibility)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetVisibility(v)
		return nil
	case space.FieldEnabled:
		v, ok := value.(bool)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEnabled(v)
		return nil
	}
	return fmt.Errorf("unknown Space field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *SpaceMutation) AddedFields() []string {
	var fields []string
	if m.addteam_id != nil {
		fields = append(fields, space.FieldTeamID)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *SpaceMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case space.FieldTeamID:
		return m.AddedTeamID()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *SpaceMutation) AddField(name string, value ent.Value) error {
	switch name {
	case space.FieldTeamID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddTeamID(v)
		return nil
	}
	return fmt.Errorf("unknown Space numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *SpaceMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(space.FieldTeamID) {
		fields = append(fields, space.FieldTeamID)
	}
	if m.FieldCleared(space.FieldTeamName) {
		fields = append(fields, space.FieldTeamName)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *SpaceMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *SpaceMutation) ClearField(name string) error {
	switch name {
	case space.FieldTeamID:
		m.ClearTeamID()
		return nil
	case space.FieldTeamName:
		m.ClearTeamName()
		return nil
	}
	return fmt.Errorf("unknown Space nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *SpaceMutation) ResetField(name string) error {
	switch name {
	case space.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case space.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case space.FieldName:
		m.ResetName()
		return nil
	case space.FieldTeamID:
		m.ResetTeamID()
		return nil
	case space.FieldTeamName:
		m.ResetTeamName()
		return nil
	case space.FieldDescription:
		m.ResetDescription()
		return nil
	case space.FieldVisibility:
		m.ResetVisibility()
		return nil
	case space.FieldEnabled:
		m.ResetEnabled()
		return nil
	}
	return fmt.Errorf("unknown Space field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *SpaceMutation) AddedEdges() []string {
	edges := make([]string, 0, 3)
	if m.integrations != nil {
		edges = append(edges, space.EdgeIntegrations)
	}
	if m.alertroutes != nil {
		edges = append(edges, space.EdgeAlertroutes)
	}
	if m.assignment_policies != nil {
		edges = append(edges, space.EdgeAssignmentPolicies)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *SpaceMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case space.EdgeIntegrations:
		ids := make([]ent.Value, 0, len(m.integrations))
		for id := range m.integrations {
			ids = append(ids, id)
		}
		return ids
	case space.EdgeAlertroutes:
		ids := make([]ent.Value, 0, len(m.alertroutes))
		for id := range m.alertroutes {
			ids = append(ids, id)
		}
		return ids
	case space.EdgeAssignmentPolicies:
		ids := make([]ent.Value, 0, len(m.assignment_policies))
		for id := range m.assignment_policies {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *SpaceMutation) RemovedEdges() []string {
	edges := make([]string, 0, 3)
	if m.removedintegrations != nil {
		edges = append(edges, space.EdgeIntegrations)
	}
	if m.removedalertroutes != nil {
		edges = append(edges, space.EdgeAlertroutes)
	}
	if m.removedassignment_policies != nil {
		edges = append(edges, space.EdgeAssignmentPolicies)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *SpaceMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case space.EdgeIntegrations:
		ids := make([]ent.Value, 0, len(m.removedintegrations))
		for id := range m.removedintegrations {
			ids = append(ids, id)
		}
		return ids
	case space.EdgeAlertroutes:
		ids := make([]ent.Value, 0, len(m.removedalertroutes))
		for id := range m.removedalertroutes {
			ids = append(ids, id)
		}
		return ids
	case space.EdgeAssignmentPolicies:
		ids := make([]ent.Value, 0, len(m.removedassignment_policies))
		for id := range m.removedassignment_policies {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *SpaceMutation) ClearedEdges() []string {
	edges := make([]string, 0, 3)
	if m.clearedintegrations {
		edges = append(edges, space.EdgeIntegrations)
	}
	if m.clearedalertroutes {
		edges = append(edges, space.EdgeAlertroutes)
	}
	if m.clearedassignment_policies {
		edges = append(edges, space.EdgeAssignmentPolicies)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *SpaceMutation) EdgeCleared(name string) bool {
	switch name {
	case space.EdgeIntegrations:
		return m.clearedintegrations
	case space.EdgeAlertroutes:
		return m.clearedalertroutes
	case space.EdgeAssignmentPolicies:
		return m.clearedassignment_policies
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *SpaceMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Space unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *SpaceMutation) ResetEdge(name string) error {
	switch name {
	case space.EdgeIntegrations:
		m.ResetIntegrations()
		return nil
	case space.EdgeAlertroutes:
		m.ResetAlertroutes()
		return nil
	case space.EdgeAssignmentPolicies:
		m.ResetAssignmentPolicies()
		return nil
	}
	return fmt.Errorf("unknown Space edge %s", name)
}
