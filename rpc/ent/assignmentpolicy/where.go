// Code generated by ent, DO NOT EDIT.

package assignmentpolicy

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id uuid.UUID) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldUpdatedAt, v))
}

// RuleID applies equality check predicate on the "rule_id" field. It's identical to RuleIDEQ.
func RuleID(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldRuleID, v))
}

// RuleName applies equality check predicate on the "rule_name" field. It's identical to RuleNameEQ.
func RuleName(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldRuleName, v))
}

// TemplateID applies equality check predicate on the "template_id" field. It's identical to TemplateIDEQ.
func TemplateID(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldTemplateID, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldDescription, v))
}

// Priority applies equality check predicate on the "priority" field. It's identical to PriorityEQ.
func Priority(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldPriority, v))
}

// AggrWindow applies equality check predicate on the "aggr_window" field. It's identical to AggrWindowEQ.
func AggrWindow(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldAggrWindow, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldUpdatedBy, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldUpdatedAt, v))
}

// RuleIDEQ applies the EQ predicate on the "rule_id" field.
func RuleIDEQ(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldRuleID, v))
}

// RuleIDNEQ applies the NEQ predicate on the "rule_id" field.
func RuleIDNEQ(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldRuleID, v))
}

// RuleIDIn applies the In predicate on the "rule_id" field.
func RuleIDIn(vs ...string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldRuleID, vs...))
}

// RuleIDNotIn applies the NotIn predicate on the "rule_id" field.
func RuleIDNotIn(vs ...string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldRuleID, vs...))
}

// RuleIDGT applies the GT predicate on the "rule_id" field.
func RuleIDGT(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldRuleID, v))
}

// RuleIDGTE applies the GTE predicate on the "rule_id" field.
func RuleIDGTE(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldRuleID, v))
}

// RuleIDLT applies the LT predicate on the "rule_id" field.
func RuleIDLT(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldRuleID, v))
}

// RuleIDLTE applies the LTE predicate on the "rule_id" field.
func RuleIDLTE(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldRuleID, v))
}

// RuleIDContains applies the Contains predicate on the "rule_id" field.
func RuleIDContains(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldContains(FieldRuleID, v))
}

// RuleIDHasPrefix applies the HasPrefix predicate on the "rule_id" field.
func RuleIDHasPrefix(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldHasPrefix(FieldRuleID, v))
}

// RuleIDHasSuffix applies the HasSuffix predicate on the "rule_id" field.
func RuleIDHasSuffix(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldHasSuffix(FieldRuleID, v))
}

// RuleIDEqualFold applies the EqualFold predicate on the "rule_id" field.
func RuleIDEqualFold(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEqualFold(FieldRuleID, v))
}

// RuleIDContainsFold applies the ContainsFold predicate on the "rule_id" field.
func RuleIDContainsFold(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldContainsFold(FieldRuleID, v))
}

// RuleNameEQ applies the EQ predicate on the "rule_name" field.
func RuleNameEQ(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldRuleName, v))
}

// RuleNameNEQ applies the NEQ predicate on the "rule_name" field.
func RuleNameNEQ(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldRuleName, v))
}

// RuleNameIn applies the In predicate on the "rule_name" field.
func RuleNameIn(vs ...string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldRuleName, vs...))
}

// RuleNameNotIn applies the NotIn predicate on the "rule_name" field.
func RuleNameNotIn(vs ...string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldRuleName, vs...))
}

// RuleNameGT applies the GT predicate on the "rule_name" field.
func RuleNameGT(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldRuleName, v))
}

// RuleNameGTE applies the GTE predicate on the "rule_name" field.
func RuleNameGTE(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldRuleName, v))
}

// RuleNameLT applies the LT predicate on the "rule_name" field.
func RuleNameLT(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldRuleName, v))
}

// RuleNameLTE applies the LTE predicate on the "rule_name" field.
func RuleNameLTE(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldRuleName, v))
}

// RuleNameContains applies the Contains predicate on the "rule_name" field.
func RuleNameContains(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldContains(FieldRuleName, v))
}

// RuleNameHasPrefix applies the HasPrefix predicate on the "rule_name" field.
func RuleNameHasPrefix(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldHasPrefix(FieldRuleName, v))
}

// RuleNameHasSuffix applies the HasSuffix predicate on the "rule_name" field.
func RuleNameHasSuffix(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldHasSuffix(FieldRuleName, v))
}

// RuleNameEqualFold applies the EqualFold predicate on the "rule_name" field.
func RuleNameEqualFold(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEqualFold(FieldRuleName, v))
}

// RuleNameContainsFold applies the ContainsFold predicate on the "rule_name" field.
func RuleNameContainsFold(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldContainsFold(FieldRuleName, v))
}

// TemplateIDEQ applies the EQ predicate on the "template_id" field.
func TemplateIDEQ(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldTemplateID, v))
}

// TemplateIDNEQ applies the NEQ predicate on the "template_id" field.
func TemplateIDNEQ(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldTemplateID, v))
}

// TemplateIDIn applies the In predicate on the "template_id" field.
func TemplateIDIn(vs ...string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldTemplateID, vs...))
}

// TemplateIDNotIn applies the NotIn predicate on the "template_id" field.
func TemplateIDNotIn(vs ...string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldTemplateID, vs...))
}

// TemplateIDGT applies the GT predicate on the "template_id" field.
func TemplateIDGT(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldTemplateID, v))
}

// TemplateIDGTE applies the GTE predicate on the "template_id" field.
func TemplateIDGTE(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldTemplateID, v))
}

// TemplateIDLT applies the LT predicate on the "template_id" field.
func TemplateIDLT(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldTemplateID, v))
}

// TemplateIDLTE applies the LTE predicate on the "template_id" field.
func TemplateIDLTE(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldTemplateID, v))
}

// TemplateIDContains applies the Contains predicate on the "template_id" field.
func TemplateIDContains(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldContains(FieldTemplateID, v))
}

// TemplateIDHasPrefix applies the HasPrefix predicate on the "template_id" field.
func TemplateIDHasPrefix(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldHasPrefix(FieldTemplateID, v))
}

// TemplateIDHasSuffix applies the HasSuffix predicate on the "template_id" field.
func TemplateIDHasSuffix(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldHasSuffix(FieldTemplateID, v))
}

// TemplateIDIsNil applies the IsNil predicate on the "template_id" field.
func TemplateIDIsNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIsNull(FieldTemplateID))
}

// TemplateIDNotNil applies the NotNil predicate on the "template_id" field.
func TemplateIDNotNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotNull(FieldTemplateID))
}

// TemplateIDEqualFold applies the EqualFold predicate on the "template_id" field.
func TemplateIDEqualFold(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEqualFold(FieldTemplateID, v))
}

// TemplateIDContainsFold applies the ContainsFold predicate on the "template_id" field.
func TemplateIDContainsFold(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldContainsFold(FieldTemplateID, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldContainsFold(FieldDescription, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v Status) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v Status) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...Status) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...Status) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldStatus, vs...))
}

// PriorityEQ applies the EQ predicate on the "priority" field.
func PriorityEQ(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldPriority, v))
}

// PriorityNEQ applies the NEQ predicate on the "priority" field.
func PriorityNEQ(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldPriority, v))
}

// PriorityIn applies the In predicate on the "priority" field.
func PriorityIn(vs ...int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldPriority, vs...))
}

// PriorityNotIn applies the NotIn predicate on the "priority" field.
func PriorityNotIn(vs ...int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldPriority, vs...))
}

// PriorityGT applies the GT predicate on the "priority" field.
func PriorityGT(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldPriority, v))
}

// PriorityGTE applies the GTE predicate on the "priority" field.
func PriorityGTE(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldPriority, v))
}

// PriorityLT applies the LT predicate on the "priority" field.
func PriorityLT(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldPriority, v))
}

// PriorityLTE applies the LTE predicate on the "priority" field.
func PriorityLTE(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldPriority, v))
}

// LayersIsNil applies the IsNil predicate on the "layers" field.
func LayersIsNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIsNull(FieldLayers))
}

// LayersNotNil applies the NotNil predicate on the "layers" field.
func LayersNotNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotNull(FieldLayers))
}

// AggrWindowEQ applies the EQ predicate on the "aggr_window" field.
func AggrWindowEQ(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldAggrWindow, v))
}

// AggrWindowNEQ applies the NEQ predicate on the "aggr_window" field.
func AggrWindowNEQ(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldAggrWindow, v))
}

// AggrWindowIn applies the In predicate on the "aggr_window" field.
func AggrWindowIn(vs ...int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldAggrWindow, vs...))
}

// AggrWindowNotIn applies the NotIn predicate on the "aggr_window" field.
func AggrWindowNotIn(vs ...int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldAggrWindow, vs...))
}

// AggrWindowGT applies the GT predicate on the "aggr_window" field.
func AggrWindowGT(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldAggrWindow, v))
}

// AggrWindowGTE applies the GTE predicate on the "aggr_window" field.
func AggrWindowGTE(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldAggrWindow, v))
}

// AggrWindowLT applies the LT predicate on the "aggr_window" field.
func AggrWindowLT(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldAggrWindow, v))
}

// AggrWindowLTE applies the LTE predicate on the "aggr_window" field.
func AggrWindowLTE(v int32) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldAggrWindow, v))
}

// TimeFiltersIsNil applies the IsNil predicate on the "time_filters" field.
func TimeFiltersIsNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIsNull(FieldTimeFilters))
}

// TimeFiltersNotNil applies the NotNil predicate on the "time_filters" field.
func TimeFiltersNotNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotNull(FieldTimeFilters))
}

// FiltersIsNil applies the IsNil predicate on the "filters" field.
func FiltersIsNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIsNull(FieldFilters))
}

// FiltersNotNil applies the NotNil predicate on the "filters" field.
func FiltersNotNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotNull(FieldFilters))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v int64) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldLTE(FieldUpdatedBy, v))
}

// UpdatedByIsNil applies the IsNil predicate on the "updated_by" field.
func UpdatedByIsNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldIsNull(FieldUpdatedBy))
}

// UpdatedByNotNil applies the NotNil predicate on the "updated_by" field.
func UpdatedByNotNil() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.FieldNotNull(FieldUpdatedBy))
}

// HasSpace applies the HasEdge predicate on the "space" edge.
func HasSpace() predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, SpaceTable, SpaceColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasSpaceWith applies the HasEdge predicate on the "space" edge with a given conditions (other predicates).
func HasSpaceWith(preds ...predicate.Space) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(func(s *sql.Selector) {
		step := newSpaceStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AssignmentPolicy) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AssignmentPolicy) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AssignmentPolicy) predicate.AssignmentPolicy {
	return predicate.AssignmentPolicy(sql.NotPredicates(p))
}
