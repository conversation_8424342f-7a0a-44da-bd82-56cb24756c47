// Code generated by ent, DO NOT EDIT.

package assignmentpolicy

import (
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	uuid "github.com/gofrs/uuid/v5"
)

const (
	// Label holds the string label denoting the assignmentpolicy type in the database.
	Label = "assignment_policy"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldRuleID holds the string denoting the rule_id field in the database.
	FieldRuleID = "rule_id"
	// FieldRuleName holds the string denoting the rule_name field in the database.
	FieldRuleName = "rule_name"
	// FieldTemplateID holds the string denoting the template_id field in the database.
	FieldTemplateID = "template_id"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldPriority holds the string denoting the priority field in the database.
	FieldPriority = "priority"
	// FieldLayers holds the string denoting the layers field in the database.
	FieldLayers = "layers"
	// FieldAggrWindow holds the string denoting the aggr_window field in the database.
	FieldAggrWindow = "aggr_window"
	// FieldTimeFilters holds the string denoting the time_filters field in the database.
	FieldTimeFilters = "time_filters"
	// FieldFilters holds the string denoting the filters field in the database.
	FieldFilters = "filters"
	// FieldUpdatedBy holds the string denoting the updated_by field in the database.
	FieldUpdatedBy = "updated_by"
	// EdgeSpace holds the string denoting the space edge name in mutations.
	EdgeSpace = "space"
	// Table holds the table name of the assignmentpolicy in the database.
	Table = "oncall_assignment_policies"
	// SpaceTable is the table that holds the space relation/edge.
	SpaceTable = "oncall_assignment_policies"
	// SpaceInverseTable is the table name for the Space entity.
	// It exists in this package in order to avoid circular dependency with the "space" package.
	SpaceInverseTable = "oncall_spaces"
	// SpaceColumn is the table column denoting the space relation/edge.
	SpaceColumn = "space_assignment_policies"
)

// Columns holds all SQL columns for assignmentpolicy fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldRuleID,
	FieldRuleName,
	FieldTemplateID,
	FieldDescription,
	FieldStatus,
	FieldPriority,
	FieldLayers,
	FieldAggrWindow,
	FieldTimeFilters,
	FieldFilters,
	FieldUpdatedBy,
}

// ForeignKeys holds the SQL foreign-keys that are owned by the "oncall_assignment_policies"
// table and are not defined as standalone fields in the schema.
var ForeignKeys = []string{
	"space_assignment_policies",
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	for i := range ForeignKeys {
		if column == ForeignKeys[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultPriority holds the default value on creation for the "priority" field.
	DefaultPriority int32
	// DefaultAggrWindow holds the default value on creation for the "aggr_window" field.
	DefaultAggrWindow int32
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() uuid.UUID
)

// Status defines the type for the "status" enum field.
type Status string

// StatusEnabled is the default value of the Status enum.
const DefaultStatus = StatusEnabled

// Status values.
const (
	StatusEnabled  Status = "enabled"
	StatusDisabled Status = "disabled"
)

func (s Status) String() string {
	return string(s)
}

// StatusValidator is a validator for the "status" field enum values. It is called by the builders before save.
func StatusValidator(s Status) error {
	switch s {
	case StatusEnabled, StatusDisabled:
		return nil
	default:
		return fmt.Errorf("assignmentpolicy: invalid enum value for status field: %q", s)
	}
}

// OrderOption defines the ordering options for the AssignmentPolicy queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByRuleID orders the results by the rule_id field.
func ByRuleID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRuleID, opts...).ToFunc()
}

// ByRuleName orders the results by the rule_name field.
func ByRuleName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRuleName, opts...).ToFunc()
}

// ByTemplateID orders the results by the template_id field.
func ByTemplateID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTemplateID, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByPriority orders the results by the priority field.
func ByPriority(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPriority, opts...).ToFunc()
}

// ByAggrWindow orders the results by the aggr_window field.
func ByAggrWindow(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAggrWindow, opts...).ToFunc()
}

// ByUpdatedBy orders the results by the updated_by field.
func ByUpdatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedBy, opts...).ToFunc()
}

// BySpaceField orders the results by space field.
func BySpaceField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newSpaceStep(), sql.OrderByField(field, opts...))
	}
}
func newSpaceStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(SpaceInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, SpaceTable, SpaceColumn),
	)
}
