// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/alertroutes"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/alertrule"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/enrichment"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/space"
)

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdate) SetNotNilUpdatedAt(value *time.Time) *AlertRoutesUpdate {
	if value != nil {
		return ar.SetUpdatedAt(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdateOne) SetNotNilUpdatedAt(value *time.Time) *AlertRoutesUpdateOne {
	if value != nil {
		return ar.SetUpdatedAt(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesCreate) SetNotNilUpdatedAt(value *time.Time) *AlertRoutesCreate {
	if value != nil {
		return ar.SetUpdatedAt(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdate) SetNotNilIntegrationID(value *string) *AlertRoutesUpdate {
	if value != nil {
		return ar.SetIntegrationID(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdateOne) SetNotNilIntegrationID(value *string) *AlertRoutesUpdateOne {
	if value != nil {
		return ar.SetIntegrationID(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesCreate) SetNotNilIntegrationID(value *string) *AlertRoutesCreate {
	if value != nil {
		return ar.SetIntegrationID(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdate) SetNotNilCases(value []map[string]interface{}) *AlertRoutesUpdate {
	if value != nil {
		return ar.SetCases(value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdateOne) SetNotNilCases(value []map[string]interface{}) *AlertRoutesUpdateOne {
	if value != nil {
		return ar.SetCases(value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesCreate) SetNotNilCases(value []map[string]interface{}) *AlertRoutesCreate {
	if value != nil {
		return ar.SetCases(value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdate) SetNotNilDefaultRoute(value *map[string]interface{}) *AlertRoutesUpdate {
	if value != nil {
		return ar.SetDefaultRoute(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdateOne) SetNotNilDefaultRoute(value *map[string]interface{}) *AlertRoutesUpdateOne {
	if value != nil {
		return ar.SetDefaultRoute(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesCreate) SetNotNilDefaultRoute(value *map[string]interface{}) *AlertRoutesCreate {
	if value != nil {
		return ar.SetDefaultRoute(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdate) SetNotNilStatus(value *alertroutes.Status) *AlertRoutesUpdate {
	if value != nil {
		return ar.SetStatus(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdateOne) SetNotNilStatus(value *alertroutes.Status) *AlertRoutesUpdateOne {
	if value != nil {
		return ar.SetStatus(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesCreate) SetNotNilStatus(value *alertroutes.Status) *AlertRoutesCreate {
	if value != nil {
		return ar.SetStatus(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdate) SetNotNilVersion(value *int32) *AlertRoutesUpdate {
	if value != nil {
		return ar.SetVersion(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdateOne) SetNotNilVersion(value *int32) *AlertRoutesUpdateOne {
	if value != nil {
		return ar.SetVersion(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesCreate) SetNotNilVersion(value *int32) *AlertRoutesCreate {
	if value != nil {
		return ar.SetVersion(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdate) SetNotNilUpdatedBy(value *int64) *AlertRoutesUpdate {
	if value != nil {
		return ar.SetUpdatedBy(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdateOne) SetNotNilUpdatedBy(value *int64) *AlertRoutesUpdateOne {
	if value != nil {
		return ar.SetUpdatedBy(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesCreate) SetNotNilUpdatedBy(value *int64) *AlertRoutesCreate {
	if value != nil {
		return ar.SetUpdatedBy(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdate) SetNotNilCreatorBy(value *int64) *AlertRoutesUpdate {
	if value != nil {
		return ar.SetCreatorBy(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesUpdateOne) SetNotNilCreatorBy(value *int64) *AlertRoutesUpdateOne {
	if value != nil {
		return ar.SetCreatorBy(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRoutesCreate) SetNotNilCreatorBy(value *int64) *AlertRoutesCreate {
	if value != nil {
		return ar.SetCreatorBy(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilUpdatedAt(value *time.Time) *AlertRuleUpdate {
	if value != nil {
		return ar.SetUpdatedAt(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilUpdatedAt(value *time.Time) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetUpdatedAt(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilUpdatedAt(value *time.Time) *AlertRuleCreate {
	if value != nil {
		return ar.SetUpdatedAt(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilName(value *string) *AlertRuleUpdate {
	if value != nil {
		return ar.SetName(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilName(value *string) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetName(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilName(value *string) *AlertRuleCreate {
	if value != nil {
		return ar.SetName(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilType(value *alertrule.Type) *AlertRuleUpdate {
	if value != nil {
		return ar.SetType(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilType(value *alertrule.Type) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetType(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilType(value *alertrule.Type) *AlertRuleCreate {
	if value != nil {
		return ar.SetType(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilKey(value *string) *AlertRuleUpdate {
	if value != nil {
		return ar.SetKey(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilKey(value *string) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetKey(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilKey(value *string) *AlertRuleCreate {
	if value != nil {
		return ar.SetKey(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilValue(value *string) *AlertRuleUpdate {
	if value != nil {
		return ar.SetValue(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilValue(value *string) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetValue(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilValue(value *string) *AlertRuleCreate {
	if value != nil {
		return ar.SetValue(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilCondition(value *alertrule.Condition) *AlertRuleUpdate {
	if value != nil {
		return ar.SetCondition(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilCondition(value *alertrule.Condition) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetCondition(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilCondition(value *alertrule.Condition) *AlertRuleCreate {
	if value != nil {
		return ar.SetCondition(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilConfig(value *map[string]interface{}) *AlertRuleUpdate {
	if value != nil {
		return ar.SetConfig(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilConfig(value *map[string]interface{}) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetConfig(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilConfig(value *map[string]interface{}) *AlertRuleCreate {
	if value != nil {
		return ar.SetConfig(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilDescription(value *string) *AlertRuleUpdate {
	if value != nil {
		return ar.SetDescription(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilDescription(value *string) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetDescription(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilDescription(value *string) *AlertRuleCreate {
	if value != nil {
		return ar.SetDescription(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdate) SetNotNilPriority(value *int) *AlertRuleUpdate {
	if value != nil {
		return ar.SetPriority(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleUpdateOne) SetNotNilPriority(value *int) *AlertRuleUpdateOne {
	if value != nil {
		return ar.SetPriority(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ar *AlertRuleCreate) SetNotNilPriority(value *int) *AlertRuleCreate {
	if value != nil {
		return ar.SetPriority(*value)
	}
	return ar
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilUpdatedAt(value *time.Time) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetUpdatedAt(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilUpdatedAt(value *time.Time) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetUpdatedAt(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilUpdatedAt(value *time.Time) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetUpdatedAt(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilRuleID(value *string) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetRuleID(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilRuleID(value *string) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetRuleID(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilRuleID(value *string) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetRuleID(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilRuleName(value *string) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetRuleName(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilRuleName(value *string) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetRuleName(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilRuleName(value *string) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetRuleName(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilTemplateID(value *string) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetTemplateID(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilTemplateID(value *string) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetTemplateID(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilTemplateID(value *string) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetTemplateID(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilDescription(value *string) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetDescription(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilDescription(value *string) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetDescription(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilDescription(value *string) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetDescription(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilStatus(value *assignmentpolicy.Status) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetStatus(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilStatus(value *assignmentpolicy.Status) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetStatus(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilStatus(value *assignmentpolicy.Status) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetStatus(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilPriority(value *int32) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetPriority(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilPriority(value *int32) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetPriority(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilPriority(value *int32) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetPriority(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilLayers(value []map[string]interface{}) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetLayers(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilLayers(value []map[string]interface{}) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetLayers(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilLayers(value []map[string]interface{}) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetLayers(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilAggrWindow(value *int32) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetAggrWindow(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilAggrWindow(value *int32) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetAggrWindow(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilAggrWindow(value *int32) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetAggrWindow(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilTimeFilters(value []map[string]interface{}) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetTimeFilters(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilTimeFilters(value []map[string]interface{}) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetTimeFilters(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilTimeFilters(value []map[string]interface{}) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetTimeFilters(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilFilters(value []map[string]interface{}) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetFilters(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilFilters(value []map[string]interface{}) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetFilters(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilFilters(value []map[string]interface{}) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetFilters(value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdate) SetNotNilUpdatedBy(value *int64) *AssignmentPolicyUpdate {
	if value != nil {
		return ap.SetUpdatedBy(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyUpdateOne) SetNotNilUpdatedBy(value *int64) *AssignmentPolicyUpdateOne {
	if value != nil {
		return ap.SetUpdatedBy(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (ap *AssignmentPolicyCreate) SetNotNilUpdatedBy(value *int64) *AssignmentPolicyCreate {
	if value != nil {
		return ap.SetUpdatedBy(*value)
	}
	return ap
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilUpdatedAt(value *time.Time) *EnrichmentUpdate {
	if value != nil {
		return e.SetUpdatedAt(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilUpdatedAt(value *time.Time) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetUpdatedAt(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilUpdatedAt(value *time.Time) *EnrichmentCreate {
	if value != nil {
		return e.SetUpdatedAt(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilName(value *string) *EnrichmentUpdate {
	if value != nil {
		return e.SetName(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilName(value *string) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetName(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilName(value *string) *EnrichmentCreate {
	if value != nil {
		return e.SetName(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilType(value *enrichment.Type) *EnrichmentUpdate {
	if value != nil {
		return e.SetType(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilType(value *enrichment.Type) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetType(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilType(value *enrichment.Type) *EnrichmentCreate {
	if value != nil {
		return e.SetType(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilKey(value *string) *EnrichmentUpdate {
	if value != nil {
		return e.SetKey(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilKey(value *string) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetKey(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilKey(value *string) *EnrichmentCreate {
	if value != nil {
		return e.SetKey(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilValue(value *string) *EnrichmentUpdate {
	if value != nil {
		return e.SetValue(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilValue(value *string) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetValue(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilValue(value *string) *EnrichmentCreate {
	if value != nil {
		return e.SetValue(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilCondition(value *enrichment.Condition) *EnrichmentUpdate {
	if value != nil {
		return e.SetCondition(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilCondition(value *enrichment.Condition) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetCondition(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilCondition(value *enrichment.Condition) *EnrichmentCreate {
	if value != nil {
		return e.SetCondition(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilConfig(value *map[string]interface{}) *EnrichmentUpdate {
	if value != nil {
		return e.SetConfig(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilConfig(value *map[string]interface{}) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetConfig(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilConfig(value *map[string]interface{}) *EnrichmentCreate {
	if value != nil {
		return e.SetConfig(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilDescription(value *string) *EnrichmentUpdate {
	if value != nil {
		return e.SetDescription(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilDescription(value *string) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetDescription(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilDescription(value *string) *EnrichmentCreate {
	if value != nil {
		return e.SetDescription(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdate) SetNotNilPriority(value *int) *EnrichmentUpdate {
	if value != nil {
		return e.SetPriority(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentUpdateOne) SetNotNilPriority(value *int) *EnrichmentUpdateOne {
	if value != nil {
		return e.SetPriority(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (e *EnrichmentCreate) SetNotNilPriority(value *int) *EnrichmentCreate {
	if value != nil {
		return e.SetPriority(*value)
	}
	return e
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdate) SetNotNilUpdatedAt(value *time.Time) *IntegrationUpdate {
	if value != nil {
		return i.SetUpdatedAt(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdateOne) SetNotNilUpdatedAt(value *time.Time) *IntegrationUpdateOne {
	if value != nil {
		return i.SetUpdatedAt(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationCreate) SetNotNilUpdatedAt(value *time.Time) *IntegrationCreate {
	if value != nil {
		return i.SetUpdatedAt(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdate) SetNotNilStatus(value *uint8) *IntegrationUpdate {
	if value != nil {
		return i.SetStatus(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdateOne) SetNotNilStatus(value *uint8) *IntegrationUpdateOne {
	if value != nil {
		return i.SetStatus(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationCreate) SetNotNilStatus(value *uint8) *IntegrationCreate {
	if value != nil {
		return i.SetStatus(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdate) SetNotNilName(value *string) *IntegrationUpdate {
	if value != nil {
		return i.SetName(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdateOne) SetNotNilName(value *string) *IntegrationUpdateOne {
	if value != nil {
		return i.SetName(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationCreate) SetNotNilName(value *string) *IntegrationCreate {
	if value != nil {
		return i.SetName(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdate) SetNotNilKind(value *integration.Kind) *IntegrationUpdate {
	if value != nil {
		return i.SetKind(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdateOne) SetNotNilKind(value *integration.Kind) *IntegrationUpdateOne {
	if value != nil {
		return i.SetKind(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationCreate) SetNotNilKind(value *integration.Kind) *IntegrationCreate {
	if value != nil {
		return i.SetKind(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdate) SetNotNilType(value *string) *IntegrationUpdate {
	if value != nil {
		return i.SetType(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdateOne) SetNotNilType(value *string) *IntegrationUpdateOne {
	if value != nil {
		return i.SetType(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationCreate) SetNotNilType(value *string) *IntegrationCreate {
	if value != nil {
		return i.SetType(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdate) SetNotNilLastEventTime(value *string) *IntegrationUpdate {
	if value != nil {
		return i.SetLastEventTime(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdateOne) SetNotNilLastEventTime(value *string) *IntegrationUpdateOne {
	if value != nil {
		return i.SetLastEventTime(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationCreate) SetNotNilLastEventTime(value *string) *IntegrationCreate {
	if value != nil {
		return i.SetLastEventTime(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdate) SetNotNilWebhook(value *string) *IntegrationUpdate {
	if value != nil {
		return i.SetWebhook(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationUpdateOne) SetNotNilWebhook(value *string) *IntegrationUpdateOne {
	if value != nil {
		return i.SetWebhook(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (i *IntegrationCreate) SetNotNilWebhook(value *string) *IntegrationCreate {
	if value != nil {
		return i.SetWebhook(*value)
	}
	return i
}

// set field if value's pointer is not nil.
func (s *SpaceUpdate) SetNotNilUpdatedAt(value *time.Time) *SpaceUpdate {
	if value != nil {
		return s.SetUpdatedAt(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdateOne) SetNotNilUpdatedAt(value *time.Time) *SpaceUpdateOne {
	if value != nil {
		return s.SetUpdatedAt(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceCreate) SetNotNilUpdatedAt(value *time.Time) *SpaceCreate {
	if value != nil {
		return s.SetUpdatedAt(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdate) SetNotNilName(value *string) *SpaceUpdate {
	if value != nil {
		return s.SetName(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdateOne) SetNotNilName(value *string) *SpaceUpdateOne {
	if value != nil {
		return s.SetName(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceCreate) SetNotNilName(value *string) *SpaceCreate {
	if value != nil {
		return s.SetName(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdate) SetNotNilTeamID(value *int64) *SpaceUpdate {
	if value != nil {
		return s.SetTeamID(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdateOne) SetNotNilTeamID(value *int64) *SpaceUpdateOne {
	if value != nil {
		return s.SetTeamID(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceCreate) SetNotNilTeamID(value *int64) *SpaceCreate {
	if value != nil {
		return s.SetTeamID(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdate) SetNotNilTeamName(value *string) *SpaceUpdate {
	if value != nil {
		return s.SetTeamName(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdateOne) SetNotNilTeamName(value *string) *SpaceUpdateOne {
	if value != nil {
		return s.SetTeamName(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceCreate) SetNotNilTeamName(value *string) *SpaceCreate {
	if value != nil {
		return s.SetTeamName(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdate) SetNotNilDescription(value *string) *SpaceUpdate {
	if value != nil {
		return s.SetDescription(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdateOne) SetNotNilDescription(value *string) *SpaceUpdateOne {
	if value != nil {
		return s.SetDescription(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceCreate) SetNotNilDescription(value *string) *SpaceCreate {
	if value != nil {
		return s.SetDescription(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdate) SetNotNilVisibility(value *space.Visibility) *SpaceUpdate {
	if value != nil {
		return s.SetVisibility(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdateOne) SetNotNilVisibility(value *space.Visibility) *SpaceUpdateOne {
	if value != nil {
		return s.SetVisibility(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceCreate) SetNotNilVisibility(value *space.Visibility) *SpaceCreate {
	if value != nil {
		return s.SetVisibility(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdate) SetNotNilEnabled(value *bool) *SpaceUpdate {
	if value != nil {
		return s.SetEnabled(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceUpdateOne) SetNotNilEnabled(value *bool) *SpaceUpdateOne {
	if value != nil {
		return s.SetEnabled(*value)
	}
	return s
}

// set field if value's pointer is not nil.
func (s *SpaceCreate) SetNotNilEnabled(value *bool) *SpaceCreate {
	if value != nil {
		return s.SetEnabled(*value)
	}
	return s
}
