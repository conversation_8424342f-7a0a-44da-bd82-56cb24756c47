// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// OncallAlertroutesColumns holds the columns for the "oncall_alertroutes" table.
	OncallAlertroutesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint64, Increment: true},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "integration_id", Type: field.TypeString, Comment: "集成ID"},
		{Name: "cases", Type: field.TypeJSON, Nullable: true, Comment: "条件规则数组"},
		{Name: "default_route", Type: field.TypeJSON, Nullable: true, Comment: "默认路由"},
		{Name: "status", Type: field.TypeEnum, Comment: "状态", Enums: []string{"enabled", "disabled"}, Default: "enabled"},
		{Name: "version", Type: field.TypeInt32, Comment: "版本", Default: 1},
		{Name: "updated_by", Type: field.TypeInt64, Nullable: true, Comment: "更新者ID"},
		{Name: "creator_by", Type: field.TypeInt64, Nullable: true, Comment: "创建者ID"},
	}
	// OncallAlertroutesTable holds the schema information for the "oncall_alertroutes" table.
	OncallAlertroutesTable = &schema.Table{
		Name:       "oncall_alertroutes",
		Columns:    OncallAlertroutesColumns,
		PrimaryKey: []*schema.Column{OncallAlertroutesColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "alertroutes_id",
				Unique:  false,
				Columns: []*schema.Column{OncallAlertroutesColumns[0]},
			},
		},
	}
	// OncallAlertrulesColumns holds the columns for the "oncall_alertrules" table.
	OncallAlertrulesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint64, Increment: true},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "name", Type: field.TypeString, Comment: "规则名称"},
		{Name: "type", Type: field.TypeEnum, Comment: "告警处理类型", Enums: []string{"update_title", "update_description", "update_severity", "alert_filter"}},
		{Name: "key", Type: field.TypeString, Nullable: true, Comment: "标签键/匹配字段"},
		{Name: "value", Type: field.TypeString, Nullable: true, Comment: "标签值/匹配值"},
		{Name: "condition", Type: field.TypeEnum, Nullable: true, Comment: "匹配条件", Enums: []string{"equals", "contains", "regex", "not_equals", "not_contains"}},
		{Name: "config", Type: field.TypeJSON, Nullable: true, Comment: "规则配置"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "规则描述"},
		{Name: "priority", Type: field.TypeInt, Comment: "优先级", Default: 0},
	}
	// OncallAlertrulesTable holds the schema information for the "oncall_alertrules" table.
	OncallAlertrulesTable = &schema.Table{
		Name:       "oncall_alertrules",
		Columns:    OncallAlertrulesColumns,
		PrimaryKey: []*schema.Column{OncallAlertrulesColumns[0]},
	}
	// OncallAssignmentPoliciesColumns holds the columns for the "oncall_assignment_policies" table.
	OncallAssignmentPoliciesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID, Comment: "UUID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "rule_id", Type: field.TypeString, Unique: true, Comment: "规则ID"},
		{Name: "rule_name", Type: field.TypeString, Comment: "规则名称"},
		{Name: "template_id", Type: field.TypeString, Nullable: true, Comment: "模板ID"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述"},
		{Name: "status", Type: field.TypeEnum, Comment: "状态", Enums: []string{"enabled", "disabled"}, Default: "enabled"},
		{Name: "priority", Type: field.TypeInt32, Comment: "优先级", Default: 1},
		{Name: "layers", Type: field.TypeJSON, Nullable: true, Comment: "分层通知"},
		{Name: "aggr_window", Type: field.TypeInt32, Comment: "聚合窗口(秒)", Default: 300},
		{Name: "time_filters", Type: field.TypeJSON, Nullable: true, Comment: "时间过滤器"},
		{Name: "filters", Type: field.TypeJSON, Nullable: true, Comment: "过滤器组"},
		{Name: "updated_by", Type: field.TypeInt64, Nullable: true, Comment: "更新者ID"},
		{Name: "space_assignment_policies", Type: field.TypeUint64, Nullable: true},
	}
	// OncallAssignmentPoliciesTable holds the schema information for the "oncall_assignment_policies" table.
	OncallAssignmentPoliciesTable = &schema.Table{
		Name:       "oncall_assignment_policies",
		Columns:    OncallAssignmentPoliciesColumns,
		PrimaryKey: []*schema.Column{OncallAssignmentPoliciesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "oncall_assignment_policies_oncall_spaces_assignment_policies",
				Columns:    []*schema.Column{OncallAssignmentPoliciesColumns[14]},
				RefColumns: []*schema.Column{OncallSpacesColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "assignmentpolicy_rule_id",
				Unique:  false,
				Columns: []*schema.Column{OncallAssignmentPoliciesColumns[3]},
			},
			{
				Name:    "assignmentpolicy_priority",
				Unique:  false,
				Columns: []*schema.Column{OncallAssignmentPoliciesColumns[8]},
			},
		},
	}
	// OncallEnrichmentsColumns holds the columns for the "oncall_enrichments" table.
	OncallEnrichmentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint64, Increment: true},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "name", Type: field.TypeString, Comment: "规则名称"},
		{Name: "type", Type: field.TypeEnum, Comment: "标签增强类型", Enums: []string{"extract_tag", "combine_tag", "delete_tag"}},
		{Name: "key", Type: field.TypeString, Nullable: true, Comment: "标签键/匹配字段"},
		{Name: "value", Type: field.TypeString, Nullable: true, Comment: "标签值/匹配值"},
		{Name: "condition", Type: field.TypeEnum, Nullable: true, Comment: "匹配条件", Enums: []string{"equals", "contains", "regex", "not_equals", "not_contains"}},
		{Name: "config", Type: field.TypeJSON, Nullable: true, Comment: "规则配置"},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "规则描述"},
		{Name: "priority", Type: field.TypeInt, Comment: "优先级", Default: 0},
	}
	// OncallEnrichmentsTable holds the schema information for the "oncall_enrichments" table.
	OncallEnrichmentsTable = &schema.Table{
		Name:       "oncall_enrichments",
		Columns:    OncallEnrichmentsColumns,
		PrimaryKey: []*schema.Column{OncallEnrichmentsColumns[0]},
	}
	// OncallIntegrationsColumns holds the columns for the "oncall_integrations" table.
	OncallIntegrationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUUID, Comment: "UUID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "status", Type: field.TypeUint8, Nullable: true, Comment: "Status 1: normal 2: ban | 状态 1 正常 2 禁用", Default: 1},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "kind", Type: field.TypeEnum, Comment: "集成种类", Enums: []string{"private", "public"}},
		{Name: "type", Type: field.TypeString, Comment: "事件类型"},
		{Name: "last_event_time", Type: field.TypeString, Nullable: true, Comment: "最后事件时间"},
		{Name: "webhook", Type: field.TypeString, Nullable: true, Comment: "webhook URL"},
	}
	// OncallIntegrationsTable holds the schema information for the "oncall_integrations" table.
	OncallIntegrationsTable = &schema.Table{
		Name:       "oncall_integrations",
		Columns:    OncallIntegrationsColumns,
		PrimaryKey: []*schema.Column{OncallIntegrationsColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "integration_name",
				Unique:  false,
				Columns: []*schema.Column{OncallIntegrationsColumns[4]},
			},
		},
	}
	// OncallSpacesColumns holds the columns for the "oncall_spaces" table.
	OncallSpacesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeUint64, Increment: true},
		{Name: "created_at", Type: field.TypeTime, Comment: "Create Time | 创建日期"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Update Time | 修改日期"},
		{Name: "name", Type: field.TypeString, Comment: "名称"},
		{Name: "team_id", Type: field.TypeInt64, Nullable: true, Comment: "团队ID"},
		{Name: "team_name", Type: field.TypeString, Nullable: true, Comment: "团队名称"},
		{Name: "description", Type: field.TypeString, Comment: "描述"},
		{Name: "visibility", Type: field.TypeEnum, Comment: "可见性", Enums: []string{"private", "public"}, Default: "private"},
		{Name: "enabled", Type: field.TypeBool, Comment: "启用状态", Default: true},
	}
	// OncallSpacesTable holds the schema information for the "oncall_spaces" table.
	OncallSpacesTable = &schema.Table{
		Name:       "oncall_spaces",
		Columns:    OncallSpacesColumns,
		PrimaryKey: []*schema.Column{OncallSpacesColumns[0]},
	}
	// AlertRoutesSpacesColumns holds the columns for the "alert_routes_spaces" table.
	AlertRoutesSpacesColumns = []*schema.Column{
		{Name: "alert_routes_id", Type: field.TypeUint64},
		{Name: "space_id", Type: field.TypeUint64},
	}
	// AlertRoutesSpacesTable holds the schema information for the "alert_routes_spaces" table.
	AlertRoutesSpacesTable = &schema.Table{
		Name:       "alert_routes_spaces",
		Columns:    AlertRoutesSpacesColumns,
		PrimaryKey: []*schema.Column{AlertRoutesSpacesColumns[0], AlertRoutesSpacesColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "alert_routes_spaces_alert_routes_id",
				Columns:    []*schema.Column{AlertRoutesSpacesColumns[0]},
				RefColumns: []*schema.Column{OncallAlertroutesColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "alert_routes_spaces_space_id",
				Columns:    []*schema.Column{AlertRoutesSpacesColumns[1]},
				RefColumns: []*schema.Column{OncallSpacesColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// IntegrationEnrichmentsColumns holds the columns for the "integration_enrichments" table.
	IntegrationEnrichmentsColumns = []*schema.Column{
		{Name: "integration_id", Type: field.TypeUUID},
		{Name: "enrichment_id", Type: field.TypeUint64},
	}
	// IntegrationEnrichmentsTable holds the schema information for the "integration_enrichments" table.
	IntegrationEnrichmentsTable = &schema.Table{
		Name:       "integration_enrichments",
		Columns:    IntegrationEnrichmentsColumns,
		PrimaryKey: []*schema.Column{IntegrationEnrichmentsColumns[0], IntegrationEnrichmentsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "integration_enrichments_integration_id",
				Columns:    []*schema.Column{IntegrationEnrichmentsColumns[0]},
				RefColumns: []*schema.Column{OncallIntegrationsColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "integration_enrichments_enrichment_id",
				Columns:    []*schema.Column{IntegrationEnrichmentsColumns[1]},
				RefColumns: []*schema.Column{OncallEnrichmentsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// IntegrationAlertRulesColumns holds the columns for the "integration_alert_rules" table.
	IntegrationAlertRulesColumns = []*schema.Column{
		{Name: "integration_id", Type: field.TypeUUID},
		{Name: "alert_rule_id", Type: field.TypeUint64},
	}
	// IntegrationAlertRulesTable holds the schema information for the "integration_alert_rules" table.
	IntegrationAlertRulesTable = &schema.Table{
		Name:       "integration_alert_rules",
		Columns:    IntegrationAlertRulesColumns,
		PrimaryKey: []*schema.Column{IntegrationAlertRulesColumns[0], IntegrationAlertRulesColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "integration_alert_rules_integration_id",
				Columns:    []*schema.Column{IntegrationAlertRulesColumns[0]},
				RefColumns: []*schema.Column{OncallIntegrationsColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "integration_alert_rules_alert_rule_id",
				Columns:    []*schema.Column{IntegrationAlertRulesColumns[1]},
				RefColumns: []*schema.Column{OncallAlertrulesColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// IntegrationAlertRoutesColumns holds the columns for the "integration_alert_routes" table.
	IntegrationAlertRoutesColumns = []*schema.Column{
		{Name: "integration_id", Type: field.TypeUUID},
		{Name: "alert_routes_id", Type: field.TypeUint64},
	}
	// IntegrationAlertRoutesTable holds the schema information for the "integration_alert_routes" table.
	IntegrationAlertRoutesTable = &schema.Table{
		Name:       "integration_alert_routes",
		Columns:    IntegrationAlertRoutesColumns,
		PrimaryKey: []*schema.Column{IntegrationAlertRoutesColumns[0], IntegrationAlertRoutesColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "integration_alert_routes_integration_id",
				Columns:    []*schema.Column{IntegrationAlertRoutesColumns[0]},
				RefColumns: []*schema.Column{OncallIntegrationsColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "integration_alert_routes_alert_routes_id",
				Columns:    []*schema.Column{IntegrationAlertRoutesColumns[1]},
				RefColumns: []*schema.Column{OncallAlertroutesColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// SpaceIntegrationsColumns holds the columns for the "space_integrations" table.
	SpaceIntegrationsColumns = []*schema.Column{
		{Name: "space_id", Type: field.TypeUint64},
		{Name: "integration_id", Type: field.TypeUUID},
	}
	// SpaceIntegrationsTable holds the schema information for the "space_integrations" table.
	SpaceIntegrationsTable = &schema.Table{
		Name:       "space_integrations",
		Columns:    SpaceIntegrationsColumns,
		PrimaryKey: []*schema.Column{SpaceIntegrationsColumns[0], SpaceIntegrationsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "space_integrations_space_id",
				Columns:    []*schema.Column{SpaceIntegrationsColumns[0]},
				RefColumns: []*schema.Column{OncallSpacesColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "space_integrations_integration_id",
				Columns:    []*schema.Column{SpaceIntegrationsColumns[1]},
				RefColumns: []*schema.Column{OncallIntegrationsColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		OncallAlertroutesTable,
		OncallAlertrulesTable,
		OncallAssignmentPoliciesTable,
		OncallEnrichmentsTable,
		OncallIntegrationsTable,
		OncallSpacesTable,
		AlertRoutesSpacesTable,
		IntegrationEnrichmentsTable,
		IntegrationAlertRulesTable,
		IntegrationAlertRoutesTable,
		SpaceIntegrationsTable,
	}
)

func init() {
	OncallAlertroutesTable.Annotation = &entsql.Annotation{
		Table: "oncall_alertroutes",
	}
	OncallAlertrulesTable.Annotation = &entsql.Annotation{
		Table: "oncall_alertrules",
	}
	OncallAssignmentPoliciesTable.ForeignKeys[0].RefTable = OncallSpacesTable
	OncallAssignmentPoliciesTable.Annotation = &entsql.Annotation{
		Table: "oncall_assignment_policies",
	}
	OncallEnrichmentsTable.Annotation = &entsql.Annotation{
		Table: "oncall_enrichments",
	}
	OncallIntegrationsTable.Annotation = &entsql.Annotation{
		Table: "oncall_integrations",
	}
	OncallSpacesTable.Annotation = &entsql.Annotation{
		Table: "oncall_spaces",
	}
	AlertRoutesSpacesTable.ForeignKeys[0].RefTable = OncallAlertroutesTable
	AlertRoutesSpacesTable.ForeignKeys[1].RefTable = OncallSpacesTable
	IntegrationEnrichmentsTable.ForeignKeys[0].RefTable = OncallIntegrationsTable
	IntegrationEnrichmentsTable.ForeignKeys[1].RefTable = OncallEnrichmentsTable
	IntegrationAlertRulesTable.ForeignKeys[0].RefTable = OncallIntegrationsTable
	IntegrationAlertRulesTable.ForeignKeys[1].RefTable = OncallAlertrulesTable
	IntegrationAlertRoutesTable.ForeignKeys[0].RefTable = OncallIntegrationsTable
	IntegrationAlertRoutesTable.ForeignKeys[1].RefTable = OncallAlertroutesTable
	SpaceIntegrationsTable.ForeignKeys[0].RefTable = OncallSpacesTable
	SpaceIntegrationsTable.ForeignKeys[1].RefTable = OncallIntegrationsTable
}
