// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/predicate"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/space"
)

// AssignmentPolicyUpdate is the builder for updating AssignmentPolicy entities.
type AssignmentPolicyUpdate struct {
	config
	hooks     []Hook
	mutation  *AssignmentPolicyMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AssignmentPolicyUpdate builder.
func (apu *AssignmentPolicyUpdate) Where(ps ...predicate.AssignmentPolicy) *AssignmentPolicyUpdate {
	apu.mutation.Where(ps...)
	return apu
}

// SetUpdatedAt sets the "updated_at" field.
func (apu *AssignmentPolicyUpdate) SetUpdatedAt(t time.Time) *AssignmentPolicyUpdate {
	apu.mutation.SetUpdatedAt(t)
	return apu
}

// SetRuleID sets the "rule_id" field.
func (apu *AssignmentPolicyUpdate) SetRuleID(s string) *AssignmentPolicyUpdate {
	apu.mutation.SetRuleID(s)
	return apu
}

// SetNillableRuleID sets the "rule_id" field if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillableRuleID(s *string) *AssignmentPolicyUpdate {
	if s != nil {
		apu.SetRuleID(*s)
	}
	return apu
}

// SetRuleName sets the "rule_name" field.
func (apu *AssignmentPolicyUpdate) SetRuleName(s string) *AssignmentPolicyUpdate {
	apu.mutation.SetRuleName(s)
	return apu
}

// SetNillableRuleName sets the "rule_name" field if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillableRuleName(s *string) *AssignmentPolicyUpdate {
	if s != nil {
		apu.SetRuleName(*s)
	}
	return apu
}

// SetTemplateID sets the "template_id" field.
func (apu *AssignmentPolicyUpdate) SetTemplateID(s string) *AssignmentPolicyUpdate {
	apu.mutation.SetTemplateID(s)
	return apu
}

// SetNillableTemplateID sets the "template_id" field if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillableTemplateID(s *string) *AssignmentPolicyUpdate {
	if s != nil {
		apu.SetTemplateID(*s)
	}
	return apu
}

// ClearTemplateID clears the value of the "template_id" field.
func (apu *AssignmentPolicyUpdate) ClearTemplateID() *AssignmentPolicyUpdate {
	apu.mutation.ClearTemplateID()
	return apu
}

// SetDescription sets the "description" field.
func (apu *AssignmentPolicyUpdate) SetDescription(s string) *AssignmentPolicyUpdate {
	apu.mutation.SetDescription(s)
	return apu
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillableDescription(s *string) *AssignmentPolicyUpdate {
	if s != nil {
		apu.SetDescription(*s)
	}
	return apu
}

// ClearDescription clears the value of the "description" field.
func (apu *AssignmentPolicyUpdate) ClearDescription() *AssignmentPolicyUpdate {
	apu.mutation.ClearDescription()
	return apu
}

// SetStatus sets the "status" field.
func (apu *AssignmentPolicyUpdate) SetStatus(a assignmentpolicy.Status) *AssignmentPolicyUpdate {
	apu.mutation.SetStatus(a)
	return apu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillableStatus(a *assignmentpolicy.Status) *AssignmentPolicyUpdate {
	if a != nil {
		apu.SetStatus(*a)
	}
	return apu
}

// SetPriority sets the "priority" field.
func (apu *AssignmentPolicyUpdate) SetPriority(i int32) *AssignmentPolicyUpdate {
	apu.mutation.ResetPriority()
	apu.mutation.SetPriority(i)
	return apu
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillablePriority(i *int32) *AssignmentPolicyUpdate {
	if i != nil {
		apu.SetPriority(*i)
	}
	return apu
}

// AddPriority adds i to the "priority" field.
func (apu *AssignmentPolicyUpdate) AddPriority(i int32) *AssignmentPolicyUpdate {
	apu.mutation.AddPriority(i)
	return apu
}

// SetLayers sets the "layers" field.
func (apu *AssignmentPolicyUpdate) SetLayers(m []map[string]interface{}) *AssignmentPolicyUpdate {
	apu.mutation.SetLayers(m)
	return apu
}

// AppendLayers appends m to the "layers" field.
func (apu *AssignmentPolicyUpdate) AppendLayers(m []map[string]interface{}) *AssignmentPolicyUpdate {
	apu.mutation.AppendLayers(m)
	return apu
}

// ClearLayers clears the value of the "layers" field.
func (apu *AssignmentPolicyUpdate) ClearLayers() *AssignmentPolicyUpdate {
	apu.mutation.ClearLayers()
	return apu
}

// SetAggrWindow sets the "aggr_window" field.
func (apu *AssignmentPolicyUpdate) SetAggrWindow(i int32) *AssignmentPolicyUpdate {
	apu.mutation.ResetAggrWindow()
	apu.mutation.SetAggrWindow(i)
	return apu
}

// SetNillableAggrWindow sets the "aggr_window" field if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillableAggrWindow(i *int32) *AssignmentPolicyUpdate {
	if i != nil {
		apu.SetAggrWindow(*i)
	}
	return apu
}

// AddAggrWindow adds i to the "aggr_window" field.
func (apu *AssignmentPolicyUpdate) AddAggrWindow(i int32) *AssignmentPolicyUpdate {
	apu.mutation.AddAggrWindow(i)
	return apu
}

// SetTimeFilters sets the "time_filters" field.
func (apu *AssignmentPolicyUpdate) SetTimeFilters(m []map[string]interface{}) *AssignmentPolicyUpdate {
	apu.mutation.SetTimeFilters(m)
	return apu
}

// AppendTimeFilters appends m to the "time_filters" field.
func (apu *AssignmentPolicyUpdate) AppendTimeFilters(m []map[string]interface{}) *AssignmentPolicyUpdate {
	apu.mutation.AppendTimeFilters(m)
	return apu
}

// ClearTimeFilters clears the value of the "time_filters" field.
func (apu *AssignmentPolicyUpdate) ClearTimeFilters() *AssignmentPolicyUpdate {
	apu.mutation.ClearTimeFilters()
	return apu
}

// SetFilters sets the "filters" field.
func (apu *AssignmentPolicyUpdate) SetFilters(m []map[string]interface{}) *AssignmentPolicyUpdate {
	apu.mutation.SetFilters(m)
	return apu
}

// AppendFilters appends m to the "filters" field.
func (apu *AssignmentPolicyUpdate) AppendFilters(m []map[string]interface{}) *AssignmentPolicyUpdate {
	apu.mutation.AppendFilters(m)
	return apu
}

// ClearFilters clears the value of the "filters" field.
func (apu *AssignmentPolicyUpdate) ClearFilters() *AssignmentPolicyUpdate {
	apu.mutation.ClearFilters()
	return apu
}

// SetUpdatedBy sets the "updated_by" field.
func (apu *AssignmentPolicyUpdate) SetUpdatedBy(i int64) *AssignmentPolicyUpdate {
	apu.mutation.ResetUpdatedBy()
	apu.mutation.SetUpdatedBy(i)
	return apu
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillableUpdatedBy(i *int64) *AssignmentPolicyUpdate {
	if i != nil {
		apu.SetUpdatedBy(*i)
	}
	return apu
}

// AddUpdatedBy adds i to the "updated_by" field.
func (apu *AssignmentPolicyUpdate) AddUpdatedBy(i int64) *AssignmentPolicyUpdate {
	apu.mutation.AddUpdatedBy(i)
	return apu
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (apu *AssignmentPolicyUpdate) ClearUpdatedBy() *AssignmentPolicyUpdate {
	apu.mutation.ClearUpdatedBy()
	return apu
}

// SetSpaceID sets the "space" edge to the Space entity by ID.
func (apu *AssignmentPolicyUpdate) SetSpaceID(id uint64) *AssignmentPolicyUpdate {
	apu.mutation.SetSpaceID(id)
	return apu
}

// SetNillableSpaceID sets the "space" edge to the Space entity by ID if the given value is not nil.
func (apu *AssignmentPolicyUpdate) SetNillableSpaceID(id *uint64) *AssignmentPolicyUpdate {
	if id != nil {
		apu = apu.SetSpaceID(*id)
	}
	return apu
}

// SetSpace sets the "space" edge to the Space entity.
func (apu *AssignmentPolicyUpdate) SetSpace(s *Space) *AssignmentPolicyUpdate {
	return apu.SetSpaceID(s.ID)
}

// Mutation returns the AssignmentPolicyMutation object of the builder.
func (apu *AssignmentPolicyUpdate) Mutation() *AssignmentPolicyMutation {
	return apu.mutation
}

// ClearSpace clears the "space" edge to the Space entity.
func (apu *AssignmentPolicyUpdate) ClearSpace() *AssignmentPolicyUpdate {
	apu.mutation.ClearSpace()
	return apu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (apu *AssignmentPolicyUpdate) Save(ctx context.Context) (int, error) {
	apu.defaults()
	return withHooks(ctx, apu.sqlSave, apu.mutation, apu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (apu *AssignmentPolicyUpdate) SaveX(ctx context.Context) int {
	affected, err := apu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (apu *AssignmentPolicyUpdate) Exec(ctx context.Context) error {
	_, err := apu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (apu *AssignmentPolicyUpdate) ExecX(ctx context.Context) {
	if err := apu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (apu *AssignmentPolicyUpdate) defaults() {
	if _, ok := apu.mutation.UpdatedAt(); !ok {
		v := assignmentpolicy.UpdateDefaultUpdatedAt()
		apu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (apu *AssignmentPolicyUpdate) check() error {
	if v, ok := apu.mutation.Status(); ok {
		if err := assignmentpolicy.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "AssignmentPolicy.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (apu *AssignmentPolicyUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AssignmentPolicyUpdate {
	apu.modifiers = append(apu.modifiers, modifiers...)
	return apu
}

func (apu *AssignmentPolicyUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := apu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(assignmentpolicy.Table, assignmentpolicy.Columns, sqlgraph.NewFieldSpec(assignmentpolicy.FieldID, field.TypeUUID))
	if ps := apu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := apu.mutation.UpdatedAt(); ok {
		_spec.SetField(assignmentpolicy.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := apu.mutation.RuleID(); ok {
		_spec.SetField(assignmentpolicy.FieldRuleID, field.TypeString, value)
	}
	if value, ok := apu.mutation.RuleName(); ok {
		_spec.SetField(assignmentpolicy.FieldRuleName, field.TypeString, value)
	}
	if value, ok := apu.mutation.TemplateID(); ok {
		_spec.SetField(assignmentpolicy.FieldTemplateID, field.TypeString, value)
	}
	if apu.mutation.TemplateIDCleared() {
		_spec.ClearField(assignmentpolicy.FieldTemplateID, field.TypeString)
	}
	if value, ok := apu.mutation.Description(); ok {
		_spec.SetField(assignmentpolicy.FieldDescription, field.TypeString, value)
	}
	if apu.mutation.DescriptionCleared() {
		_spec.ClearField(assignmentpolicy.FieldDescription, field.TypeString)
	}
	if value, ok := apu.mutation.Status(); ok {
		_spec.SetField(assignmentpolicy.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := apu.mutation.Priority(); ok {
		_spec.SetField(assignmentpolicy.FieldPriority, field.TypeInt32, value)
	}
	if value, ok := apu.mutation.AddedPriority(); ok {
		_spec.AddField(assignmentpolicy.FieldPriority, field.TypeInt32, value)
	}
	if value, ok := apu.mutation.Layers(); ok {
		_spec.SetField(assignmentpolicy.FieldLayers, field.TypeJSON, value)
	}
	if value, ok := apu.mutation.AppendedLayers(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, assignmentpolicy.FieldLayers, value)
		})
	}
	if apu.mutation.LayersCleared() {
		_spec.ClearField(assignmentpolicy.FieldLayers, field.TypeJSON)
	}
	if value, ok := apu.mutation.AggrWindow(); ok {
		_spec.SetField(assignmentpolicy.FieldAggrWindow, field.TypeInt32, value)
	}
	if value, ok := apu.mutation.AddedAggrWindow(); ok {
		_spec.AddField(assignmentpolicy.FieldAggrWindow, field.TypeInt32, value)
	}
	if value, ok := apu.mutation.TimeFilters(); ok {
		_spec.SetField(assignmentpolicy.FieldTimeFilters, field.TypeJSON, value)
	}
	if value, ok := apu.mutation.AppendedTimeFilters(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, assignmentpolicy.FieldTimeFilters, value)
		})
	}
	if apu.mutation.TimeFiltersCleared() {
		_spec.ClearField(assignmentpolicy.FieldTimeFilters, field.TypeJSON)
	}
	if value, ok := apu.mutation.Filters(); ok {
		_spec.SetField(assignmentpolicy.FieldFilters, field.TypeJSON, value)
	}
	if value, ok := apu.mutation.AppendedFilters(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, assignmentpolicy.FieldFilters, value)
		})
	}
	if apu.mutation.FiltersCleared() {
		_spec.ClearField(assignmentpolicy.FieldFilters, field.TypeJSON)
	}
	if value, ok := apu.mutation.UpdatedBy(); ok {
		_spec.SetField(assignmentpolicy.FieldUpdatedBy, field.TypeInt64, value)
	}
	if value, ok := apu.mutation.AddedUpdatedBy(); ok {
		_spec.AddField(assignmentpolicy.FieldUpdatedBy, field.TypeInt64, value)
	}
	if apu.mutation.UpdatedByCleared() {
		_spec.ClearField(assignmentpolicy.FieldUpdatedBy, field.TypeInt64)
	}
	if apu.mutation.SpaceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   assignmentpolicy.SpaceTable,
			Columns: []string{assignmentpolicy.SpaceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(space.FieldID, field.TypeUint64),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := apu.mutation.SpaceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   assignmentpolicy.SpaceTable,
			Columns: []string{assignmentpolicy.SpaceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(space.FieldID, field.TypeUint64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(apu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, apu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{assignmentpolicy.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	apu.mutation.done = true
	return n, nil
}

// AssignmentPolicyUpdateOne is the builder for updating a single AssignmentPolicy entity.
type AssignmentPolicyUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AssignmentPolicyMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (apuo *AssignmentPolicyUpdateOne) SetUpdatedAt(t time.Time) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetUpdatedAt(t)
	return apuo
}

// SetRuleID sets the "rule_id" field.
func (apuo *AssignmentPolicyUpdateOne) SetRuleID(s string) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetRuleID(s)
	return apuo
}

// SetNillableRuleID sets the "rule_id" field if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillableRuleID(s *string) *AssignmentPolicyUpdateOne {
	if s != nil {
		apuo.SetRuleID(*s)
	}
	return apuo
}

// SetRuleName sets the "rule_name" field.
func (apuo *AssignmentPolicyUpdateOne) SetRuleName(s string) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetRuleName(s)
	return apuo
}

// SetNillableRuleName sets the "rule_name" field if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillableRuleName(s *string) *AssignmentPolicyUpdateOne {
	if s != nil {
		apuo.SetRuleName(*s)
	}
	return apuo
}

// SetTemplateID sets the "template_id" field.
func (apuo *AssignmentPolicyUpdateOne) SetTemplateID(s string) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetTemplateID(s)
	return apuo
}

// SetNillableTemplateID sets the "template_id" field if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillableTemplateID(s *string) *AssignmentPolicyUpdateOne {
	if s != nil {
		apuo.SetTemplateID(*s)
	}
	return apuo
}

// ClearTemplateID clears the value of the "template_id" field.
func (apuo *AssignmentPolicyUpdateOne) ClearTemplateID() *AssignmentPolicyUpdateOne {
	apuo.mutation.ClearTemplateID()
	return apuo
}

// SetDescription sets the "description" field.
func (apuo *AssignmentPolicyUpdateOne) SetDescription(s string) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetDescription(s)
	return apuo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillableDescription(s *string) *AssignmentPolicyUpdateOne {
	if s != nil {
		apuo.SetDescription(*s)
	}
	return apuo
}

// ClearDescription clears the value of the "description" field.
func (apuo *AssignmentPolicyUpdateOne) ClearDescription() *AssignmentPolicyUpdateOne {
	apuo.mutation.ClearDescription()
	return apuo
}

// SetStatus sets the "status" field.
func (apuo *AssignmentPolicyUpdateOne) SetStatus(a assignmentpolicy.Status) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetStatus(a)
	return apuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillableStatus(a *assignmentpolicy.Status) *AssignmentPolicyUpdateOne {
	if a != nil {
		apuo.SetStatus(*a)
	}
	return apuo
}

// SetPriority sets the "priority" field.
func (apuo *AssignmentPolicyUpdateOne) SetPriority(i int32) *AssignmentPolicyUpdateOne {
	apuo.mutation.ResetPriority()
	apuo.mutation.SetPriority(i)
	return apuo
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillablePriority(i *int32) *AssignmentPolicyUpdateOne {
	if i != nil {
		apuo.SetPriority(*i)
	}
	return apuo
}

// AddPriority adds i to the "priority" field.
func (apuo *AssignmentPolicyUpdateOne) AddPriority(i int32) *AssignmentPolicyUpdateOne {
	apuo.mutation.AddPriority(i)
	return apuo
}

// SetLayers sets the "layers" field.
func (apuo *AssignmentPolicyUpdateOne) SetLayers(m []map[string]interface{}) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetLayers(m)
	return apuo
}

// AppendLayers appends m to the "layers" field.
func (apuo *AssignmentPolicyUpdateOne) AppendLayers(m []map[string]interface{}) *AssignmentPolicyUpdateOne {
	apuo.mutation.AppendLayers(m)
	return apuo
}

// ClearLayers clears the value of the "layers" field.
func (apuo *AssignmentPolicyUpdateOne) ClearLayers() *AssignmentPolicyUpdateOne {
	apuo.mutation.ClearLayers()
	return apuo
}

// SetAggrWindow sets the "aggr_window" field.
func (apuo *AssignmentPolicyUpdateOne) SetAggrWindow(i int32) *AssignmentPolicyUpdateOne {
	apuo.mutation.ResetAggrWindow()
	apuo.mutation.SetAggrWindow(i)
	return apuo
}

// SetNillableAggrWindow sets the "aggr_window" field if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillableAggrWindow(i *int32) *AssignmentPolicyUpdateOne {
	if i != nil {
		apuo.SetAggrWindow(*i)
	}
	return apuo
}

// AddAggrWindow adds i to the "aggr_window" field.
func (apuo *AssignmentPolicyUpdateOne) AddAggrWindow(i int32) *AssignmentPolicyUpdateOne {
	apuo.mutation.AddAggrWindow(i)
	return apuo
}

// SetTimeFilters sets the "time_filters" field.
func (apuo *AssignmentPolicyUpdateOne) SetTimeFilters(m []map[string]interface{}) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetTimeFilters(m)
	return apuo
}

// AppendTimeFilters appends m to the "time_filters" field.
func (apuo *AssignmentPolicyUpdateOne) AppendTimeFilters(m []map[string]interface{}) *AssignmentPolicyUpdateOne {
	apuo.mutation.AppendTimeFilters(m)
	return apuo
}

// ClearTimeFilters clears the value of the "time_filters" field.
func (apuo *AssignmentPolicyUpdateOne) ClearTimeFilters() *AssignmentPolicyUpdateOne {
	apuo.mutation.ClearTimeFilters()
	return apuo
}

// SetFilters sets the "filters" field.
func (apuo *AssignmentPolicyUpdateOne) SetFilters(m []map[string]interface{}) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetFilters(m)
	return apuo
}

// AppendFilters appends m to the "filters" field.
func (apuo *AssignmentPolicyUpdateOne) AppendFilters(m []map[string]interface{}) *AssignmentPolicyUpdateOne {
	apuo.mutation.AppendFilters(m)
	return apuo
}

// ClearFilters clears the value of the "filters" field.
func (apuo *AssignmentPolicyUpdateOne) ClearFilters() *AssignmentPolicyUpdateOne {
	apuo.mutation.ClearFilters()
	return apuo
}

// SetUpdatedBy sets the "updated_by" field.
func (apuo *AssignmentPolicyUpdateOne) SetUpdatedBy(i int64) *AssignmentPolicyUpdateOne {
	apuo.mutation.ResetUpdatedBy()
	apuo.mutation.SetUpdatedBy(i)
	return apuo
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillableUpdatedBy(i *int64) *AssignmentPolicyUpdateOne {
	if i != nil {
		apuo.SetUpdatedBy(*i)
	}
	return apuo
}

// AddUpdatedBy adds i to the "updated_by" field.
func (apuo *AssignmentPolicyUpdateOne) AddUpdatedBy(i int64) *AssignmentPolicyUpdateOne {
	apuo.mutation.AddUpdatedBy(i)
	return apuo
}

// ClearUpdatedBy clears the value of the "updated_by" field.
func (apuo *AssignmentPolicyUpdateOne) ClearUpdatedBy() *AssignmentPolicyUpdateOne {
	apuo.mutation.ClearUpdatedBy()
	return apuo
}

// SetSpaceID sets the "space" edge to the Space entity by ID.
func (apuo *AssignmentPolicyUpdateOne) SetSpaceID(id uint64) *AssignmentPolicyUpdateOne {
	apuo.mutation.SetSpaceID(id)
	return apuo
}

// SetNillableSpaceID sets the "space" edge to the Space entity by ID if the given value is not nil.
func (apuo *AssignmentPolicyUpdateOne) SetNillableSpaceID(id *uint64) *AssignmentPolicyUpdateOne {
	if id != nil {
		apuo = apuo.SetSpaceID(*id)
	}
	return apuo
}

// SetSpace sets the "space" edge to the Space entity.
func (apuo *AssignmentPolicyUpdateOne) SetSpace(s *Space) *AssignmentPolicyUpdateOne {
	return apuo.SetSpaceID(s.ID)
}

// Mutation returns the AssignmentPolicyMutation object of the builder.
func (apuo *AssignmentPolicyUpdateOne) Mutation() *AssignmentPolicyMutation {
	return apuo.mutation
}

// ClearSpace clears the "space" edge to the Space entity.
func (apuo *AssignmentPolicyUpdateOne) ClearSpace() *AssignmentPolicyUpdateOne {
	apuo.mutation.ClearSpace()
	return apuo
}

// Where appends a list predicates to the AssignmentPolicyUpdate builder.
func (apuo *AssignmentPolicyUpdateOne) Where(ps ...predicate.AssignmentPolicy) *AssignmentPolicyUpdateOne {
	apuo.mutation.Where(ps...)
	return apuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (apuo *AssignmentPolicyUpdateOne) Select(field string, fields ...string) *AssignmentPolicyUpdateOne {
	apuo.fields = append([]string{field}, fields...)
	return apuo
}

// Save executes the query and returns the updated AssignmentPolicy entity.
func (apuo *AssignmentPolicyUpdateOne) Save(ctx context.Context) (*AssignmentPolicy, error) {
	apuo.defaults()
	return withHooks(ctx, apuo.sqlSave, apuo.mutation, apuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (apuo *AssignmentPolicyUpdateOne) SaveX(ctx context.Context) *AssignmentPolicy {
	node, err := apuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (apuo *AssignmentPolicyUpdateOne) Exec(ctx context.Context) error {
	_, err := apuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (apuo *AssignmentPolicyUpdateOne) ExecX(ctx context.Context) {
	if err := apuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (apuo *AssignmentPolicyUpdateOne) defaults() {
	if _, ok := apuo.mutation.UpdatedAt(); !ok {
		v := assignmentpolicy.UpdateDefaultUpdatedAt()
		apuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (apuo *AssignmentPolicyUpdateOne) check() error {
	if v, ok := apuo.mutation.Status(); ok {
		if err := assignmentpolicy.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "AssignmentPolicy.status": %w`, err)}
		}
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (apuo *AssignmentPolicyUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AssignmentPolicyUpdateOne {
	apuo.modifiers = append(apuo.modifiers, modifiers...)
	return apuo
}

func (apuo *AssignmentPolicyUpdateOne) sqlSave(ctx context.Context) (_node *AssignmentPolicy, err error) {
	if err := apuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(assignmentpolicy.Table, assignmentpolicy.Columns, sqlgraph.NewFieldSpec(assignmentpolicy.FieldID, field.TypeUUID))
	id, ok := apuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AssignmentPolicy.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := apuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, assignmentpolicy.FieldID)
		for _, f := range fields {
			if !assignmentpolicy.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != assignmentpolicy.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := apuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := apuo.mutation.UpdatedAt(); ok {
		_spec.SetField(assignmentpolicy.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := apuo.mutation.RuleID(); ok {
		_spec.SetField(assignmentpolicy.FieldRuleID, field.TypeString, value)
	}
	if value, ok := apuo.mutation.RuleName(); ok {
		_spec.SetField(assignmentpolicy.FieldRuleName, field.TypeString, value)
	}
	if value, ok := apuo.mutation.TemplateID(); ok {
		_spec.SetField(assignmentpolicy.FieldTemplateID, field.TypeString, value)
	}
	if apuo.mutation.TemplateIDCleared() {
		_spec.ClearField(assignmentpolicy.FieldTemplateID, field.TypeString)
	}
	if value, ok := apuo.mutation.Description(); ok {
		_spec.SetField(assignmentpolicy.FieldDescription, field.TypeString, value)
	}
	if apuo.mutation.DescriptionCleared() {
		_spec.ClearField(assignmentpolicy.FieldDescription, field.TypeString)
	}
	if value, ok := apuo.mutation.Status(); ok {
		_spec.SetField(assignmentpolicy.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := apuo.mutation.Priority(); ok {
		_spec.SetField(assignmentpolicy.FieldPriority, field.TypeInt32, value)
	}
	if value, ok := apuo.mutation.AddedPriority(); ok {
		_spec.AddField(assignmentpolicy.FieldPriority, field.TypeInt32, value)
	}
	if value, ok := apuo.mutation.Layers(); ok {
		_spec.SetField(assignmentpolicy.FieldLayers, field.TypeJSON, value)
	}
	if value, ok := apuo.mutation.AppendedLayers(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, assignmentpolicy.FieldLayers, value)
		})
	}
	if apuo.mutation.LayersCleared() {
		_spec.ClearField(assignmentpolicy.FieldLayers, field.TypeJSON)
	}
	if value, ok := apuo.mutation.AggrWindow(); ok {
		_spec.SetField(assignmentpolicy.FieldAggrWindow, field.TypeInt32, value)
	}
	if value, ok := apuo.mutation.AddedAggrWindow(); ok {
		_spec.AddField(assignmentpolicy.FieldAggrWindow, field.TypeInt32, value)
	}
	if value, ok := apuo.mutation.TimeFilters(); ok {
		_spec.SetField(assignmentpolicy.FieldTimeFilters, field.TypeJSON, value)
	}
	if value, ok := apuo.mutation.AppendedTimeFilters(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, assignmentpolicy.FieldTimeFilters, value)
		})
	}
	if apuo.mutation.TimeFiltersCleared() {
		_spec.ClearField(assignmentpolicy.FieldTimeFilters, field.TypeJSON)
	}
	if value, ok := apuo.mutation.Filters(); ok {
		_spec.SetField(assignmentpolicy.FieldFilters, field.TypeJSON, value)
	}
	if value, ok := apuo.mutation.AppendedFilters(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, assignmentpolicy.FieldFilters, value)
		})
	}
	if apuo.mutation.FiltersCleared() {
		_spec.ClearField(assignmentpolicy.FieldFilters, field.TypeJSON)
	}
	if value, ok := apuo.mutation.UpdatedBy(); ok {
		_spec.SetField(assignmentpolicy.FieldUpdatedBy, field.TypeInt64, value)
	}
	if value, ok := apuo.mutation.AddedUpdatedBy(); ok {
		_spec.AddField(assignmentpolicy.FieldUpdatedBy, field.TypeInt64, value)
	}
	if apuo.mutation.UpdatedByCleared() {
		_spec.ClearField(assignmentpolicy.FieldUpdatedBy, field.TypeInt64)
	}
	if apuo.mutation.SpaceCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   assignmentpolicy.SpaceTable,
			Columns: []string{assignmentpolicy.SpaceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(space.FieldID, field.TypeUint64),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := apuo.mutation.SpaceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   assignmentpolicy.SpaceTable,
			Columns: []string{assignmentpolicy.SpaceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(space.FieldID, field.TypeUint64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(apuo.modifiers...)
	_node = &AssignmentPolicy{config: apuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, apuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{assignmentpolicy.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	apuo.mutation.done = true
	return _node, nil
}
