// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	uuid "github.com/gofrs/uuid/v5"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/space"
)

// AssignmentPolicyCreate is the builder for creating a AssignmentPolicy entity.
type AssignmentPolicyCreate struct {
	config
	mutation *AssignmentPolicyMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (apc *AssignmentPolicyCreate) SetCreatedAt(t time.Time) *AssignmentPolicyCreate {
	apc.mutation.SetCreatedAt(t)
	return apc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableCreatedAt(t *time.Time) *AssignmentPolicyCreate {
	if t != nil {
		apc.SetCreatedAt(*t)
	}
	return apc
}

// SetUpdatedAt sets the "updated_at" field.
func (apc *AssignmentPolicyCreate) SetUpdatedAt(t time.Time) *AssignmentPolicyCreate {
	apc.mutation.SetUpdatedAt(t)
	return apc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableUpdatedAt(t *time.Time) *AssignmentPolicyCreate {
	if t != nil {
		apc.SetUpdatedAt(*t)
	}
	return apc
}

// SetRuleID sets the "rule_id" field.
func (apc *AssignmentPolicyCreate) SetRuleID(s string) *AssignmentPolicyCreate {
	apc.mutation.SetRuleID(s)
	return apc
}

// SetRuleName sets the "rule_name" field.
func (apc *AssignmentPolicyCreate) SetRuleName(s string) *AssignmentPolicyCreate {
	apc.mutation.SetRuleName(s)
	return apc
}

// SetTemplateID sets the "template_id" field.
func (apc *AssignmentPolicyCreate) SetTemplateID(s string) *AssignmentPolicyCreate {
	apc.mutation.SetTemplateID(s)
	return apc
}

// SetNillableTemplateID sets the "template_id" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableTemplateID(s *string) *AssignmentPolicyCreate {
	if s != nil {
		apc.SetTemplateID(*s)
	}
	return apc
}

// SetDescription sets the "description" field.
func (apc *AssignmentPolicyCreate) SetDescription(s string) *AssignmentPolicyCreate {
	apc.mutation.SetDescription(s)
	return apc
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableDescription(s *string) *AssignmentPolicyCreate {
	if s != nil {
		apc.SetDescription(*s)
	}
	return apc
}

// SetStatus sets the "status" field.
func (apc *AssignmentPolicyCreate) SetStatus(a assignmentpolicy.Status) *AssignmentPolicyCreate {
	apc.mutation.SetStatus(a)
	return apc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableStatus(a *assignmentpolicy.Status) *AssignmentPolicyCreate {
	if a != nil {
		apc.SetStatus(*a)
	}
	return apc
}

// SetPriority sets the "priority" field.
func (apc *AssignmentPolicyCreate) SetPriority(i int32) *AssignmentPolicyCreate {
	apc.mutation.SetPriority(i)
	return apc
}

// SetNillablePriority sets the "priority" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillablePriority(i *int32) *AssignmentPolicyCreate {
	if i != nil {
		apc.SetPriority(*i)
	}
	return apc
}

// SetLayers sets the "layers" field.
func (apc *AssignmentPolicyCreate) SetLayers(m []map[string]interface{}) *AssignmentPolicyCreate {
	apc.mutation.SetLayers(m)
	return apc
}

// SetAggrWindow sets the "aggr_window" field.
func (apc *AssignmentPolicyCreate) SetAggrWindow(i int32) *AssignmentPolicyCreate {
	apc.mutation.SetAggrWindow(i)
	return apc
}

// SetNillableAggrWindow sets the "aggr_window" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableAggrWindow(i *int32) *AssignmentPolicyCreate {
	if i != nil {
		apc.SetAggrWindow(*i)
	}
	return apc
}

// SetTimeFilters sets the "time_filters" field.
func (apc *AssignmentPolicyCreate) SetTimeFilters(m []map[string]interface{}) *AssignmentPolicyCreate {
	apc.mutation.SetTimeFilters(m)
	return apc
}

// SetFilters sets the "filters" field.
func (apc *AssignmentPolicyCreate) SetFilters(m []map[string]interface{}) *AssignmentPolicyCreate {
	apc.mutation.SetFilters(m)
	return apc
}

// SetUpdatedBy sets the "updated_by" field.
func (apc *AssignmentPolicyCreate) SetUpdatedBy(i int64) *AssignmentPolicyCreate {
	apc.mutation.SetUpdatedBy(i)
	return apc
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableUpdatedBy(i *int64) *AssignmentPolicyCreate {
	if i != nil {
		apc.SetUpdatedBy(*i)
	}
	return apc
}

// SetID sets the "id" field.
func (apc *AssignmentPolicyCreate) SetID(u uuid.UUID) *AssignmentPolicyCreate {
	apc.mutation.SetID(u)
	return apc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableID(u *uuid.UUID) *AssignmentPolicyCreate {
	if u != nil {
		apc.SetID(*u)
	}
	return apc
}

// SetSpaceID sets the "space" edge to the Space entity by ID.
func (apc *AssignmentPolicyCreate) SetSpaceID(id uint64) *AssignmentPolicyCreate {
	apc.mutation.SetSpaceID(id)
	return apc
}

// SetNillableSpaceID sets the "space" edge to the Space entity by ID if the given value is not nil.
func (apc *AssignmentPolicyCreate) SetNillableSpaceID(id *uint64) *AssignmentPolicyCreate {
	if id != nil {
		apc = apc.SetSpaceID(*id)
	}
	return apc
}

// SetSpace sets the "space" edge to the Space entity.
func (apc *AssignmentPolicyCreate) SetSpace(s *Space) *AssignmentPolicyCreate {
	return apc.SetSpaceID(s.ID)
}

// Mutation returns the AssignmentPolicyMutation object of the builder.
func (apc *AssignmentPolicyCreate) Mutation() *AssignmentPolicyMutation {
	return apc.mutation
}

// Save creates the AssignmentPolicy in the database.
func (apc *AssignmentPolicyCreate) Save(ctx context.Context) (*AssignmentPolicy, error) {
	apc.defaults()
	return withHooks(ctx, apc.sqlSave, apc.mutation, apc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (apc *AssignmentPolicyCreate) SaveX(ctx context.Context) *AssignmentPolicy {
	v, err := apc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (apc *AssignmentPolicyCreate) Exec(ctx context.Context) error {
	_, err := apc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (apc *AssignmentPolicyCreate) ExecX(ctx context.Context) {
	if err := apc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (apc *AssignmentPolicyCreate) defaults() {
	if _, ok := apc.mutation.CreatedAt(); !ok {
		v := assignmentpolicy.DefaultCreatedAt()
		apc.mutation.SetCreatedAt(v)
	}
	if _, ok := apc.mutation.UpdatedAt(); !ok {
		v := assignmentpolicy.DefaultUpdatedAt()
		apc.mutation.SetUpdatedAt(v)
	}
	if _, ok := apc.mutation.Status(); !ok {
		v := assignmentpolicy.DefaultStatus
		apc.mutation.SetStatus(v)
	}
	if _, ok := apc.mutation.Priority(); !ok {
		v := assignmentpolicy.DefaultPriority
		apc.mutation.SetPriority(v)
	}
	if _, ok := apc.mutation.AggrWindow(); !ok {
		v := assignmentpolicy.DefaultAggrWindow
		apc.mutation.SetAggrWindow(v)
	}
	if _, ok := apc.mutation.ID(); !ok {
		v := assignmentpolicy.DefaultID()
		apc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (apc *AssignmentPolicyCreate) check() error {
	if _, ok := apc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AssignmentPolicy.created_at"`)}
	}
	if _, ok := apc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AssignmentPolicy.updated_at"`)}
	}
	if _, ok := apc.mutation.RuleID(); !ok {
		return &ValidationError{Name: "rule_id", err: errors.New(`ent: missing required field "AssignmentPolicy.rule_id"`)}
	}
	if _, ok := apc.mutation.RuleName(); !ok {
		return &ValidationError{Name: "rule_name", err: errors.New(`ent: missing required field "AssignmentPolicy.rule_name"`)}
	}
	if _, ok := apc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "AssignmentPolicy.status"`)}
	}
	if v, ok := apc.mutation.Status(); ok {
		if err := assignmentpolicy.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "AssignmentPolicy.status": %w`, err)}
		}
	}
	if _, ok := apc.mutation.Priority(); !ok {
		return &ValidationError{Name: "priority", err: errors.New(`ent: missing required field "AssignmentPolicy.priority"`)}
	}
	if _, ok := apc.mutation.AggrWindow(); !ok {
		return &ValidationError{Name: "aggr_window", err: errors.New(`ent: missing required field "AssignmentPolicy.aggr_window"`)}
	}
	return nil
}

func (apc *AssignmentPolicyCreate) sqlSave(ctx context.Context) (*AssignmentPolicy, error) {
	if err := apc.check(); err != nil {
		return nil, err
	}
	_node, _spec := apc.createSpec()
	if err := sqlgraph.CreateNode(ctx, apc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(*uuid.UUID); ok {
			_node.ID = *id
		} else if err := _node.ID.Scan(_spec.ID.Value); err != nil {
			return nil, err
		}
	}
	apc.mutation.id = &_node.ID
	apc.mutation.done = true
	return _node, nil
}

func (apc *AssignmentPolicyCreate) createSpec() (*AssignmentPolicy, *sqlgraph.CreateSpec) {
	var (
		_node = &AssignmentPolicy{config: apc.config}
		_spec = sqlgraph.NewCreateSpec(assignmentpolicy.Table, sqlgraph.NewFieldSpec(assignmentpolicy.FieldID, field.TypeUUID))
	)
	if id, ok := apc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = &id
	}
	if value, ok := apc.mutation.CreatedAt(); ok {
		_spec.SetField(assignmentpolicy.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := apc.mutation.UpdatedAt(); ok {
		_spec.SetField(assignmentpolicy.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := apc.mutation.RuleID(); ok {
		_spec.SetField(assignmentpolicy.FieldRuleID, field.TypeString, value)
		_node.RuleID = value
	}
	if value, ok := apc.mutation.RuleName(); ok {
		_spec.SetField(assignmentpolicy.FieldRuleName, field.TypeString, value)
		_node.RuleName = value
	}
	if value, ok := apc.mutation.TemplateID(); ok {
		_spec.SetField(assignmentpolicy.FieldTemplateID, field.TypeString, value)
		_node.TemplateID = value
	}
	if value, ok := apc.mutation.Description(); ok {
		_spec.SetField(assignmentpolicy.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := apc.mutation.Status(); ok {
		_spec.SetField(assignmentpolicy.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := apc.mutation.Priority(); ok {
		_spec.SetField(assignmentpolicy.FieldPriority, field.TypeInt32, value)
		_node.Priority = value
	}
	if value, ok := apc.mutation.Layers(); ok {
		_spec.SetField(assignmentpolicy.FieldLayers, field.TypeJSON, value)
		_node.Layers = value
	}
	if value, ok := apc.mutation.AggrWindow(); ok {
		_spec.SetField(assignmentpolicy.FieldAggrWindow, field.TypeInt32, value)
		_node.AggrWindow = value
	}
	if value, ok := apc.mutation.TimeFilters(); ok {
		_spec.SetField(assignmentpolicy.FieldTimeFilters, field.TypeJSON, value)
		_node.TimeFilters = value
	}
	if value, ok := apc.mutation.Filters(); ok {
		_spec.SetField(assignmentpolicy.FieldFilters, field.TypeJSON, value)
		_node.Filters = value
	}
	if value, ok := apc.mutation.UpdatedBy(); ok {
		_spec.SetField(assignmentpolicy.FieldUpdatedBy, field.TypeInt64, value)
		_node.UpdatedBy = value
	}
	if nodes := apc.mutation.SpaceIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   assignmentpolicy.SpaceTable,
			Columns: []string{assignmentpolicy.SpaceColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(space.FieldID, field.TypeUint64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.space_assignment_policies = &nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// AssignmentPolicyCreateBulk is the builder for creating many AssignmentPolicy entities in bulk.
type AssignmentPolicyCreateBulk struct {
	config
	err      error
	builders []*AssignmentPolicyCreate
}

// Save creates the AssignmentPolicy entities in the database.
func (apcb *AssignmentPolicyCreateBulk) Save(ctx context.Context) ([]*AssignmentPolicy, error) {
	if apcb.err != nil {
		return nil, apcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(apcb.builders))
	nodes := make([]*AssignmentPolicy, len(apcb.builders))
	mutators := make([]Mutator, len(apcb.builders))
	for i := range apcb.builders {
		func(i int, root context.Context) {
			builder := apcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AssignmentPolicyMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, apcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, apcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, apcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (apcb *AssignmentPolicyCreateBulk) SaveX(ctx context.Context) []*AssignmentPolicy {
	v, err := apcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (apcb *AssignmentPolicyCreateBulk) Exec(ctx context.Context) error {
	_, err := apcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (apcb *AssignmentPolicyCreateBulk) ExecX(ctx context.Context) {
	if err := apcb.Exec(ctx); err != nil {
		panic(err)
	}
}
