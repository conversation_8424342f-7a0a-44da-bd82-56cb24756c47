// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: oncall.proto

package server

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/internal/logic/integration"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"
)

type IntegrationServer struct {
	svcCtx *svc.ServiceContext
	oncall.UnimplementedIntegrationServer
}

func NewIntegrationServer(svcCtx *svc.ServiceContext) *IntegrationServer {
	return &IntegrationServer{
		svcCtx: svcCtx,
	}
}

// Integration management
func (s *IntegrationServer) CreateIntegration(ctx context.Context, in *oncall.CreateIntegrationReq) (*oncall.BaseUUIDResp, error) {
	l := integrationlogic.NewCreateIntegrationLogic(ctx, s.svcCtx)
	return l.CreateIntegration(in)
}

func (s *IntegrationServer) UpdateIntegration(ctx context.Context, in *oncall.UpdateIntegrationReq) (*oncall.BaseResp, error) {
	l := integrationlogic.NewUpdateIntegrationLogic(ctx, s.svcCtx)
	return l.UpdateIntegration(in)
}

func (s *IntegrationServer) GetIntegrationList(ctx context.Context, in *oncall.IntegrationListReq) (*oncall.IntegrationListResp, error) {
	l := integrationlogic.NewGetIntegrationListLogic(ctx, s.svcCtx)
	return l.GetIntegrationList(in)
}

func (s *IntegrationServer) GetIntegrationById(ctx context.Context, in *oncall.UUIDReq) (*oncall.IntegrationInfo, error) {
	l := integrationlogic.NewGetIntegrationByIdLogic(ctx, s.svcCtx)
	return l.GetIntegrationById(in)
}

func (s *IntegrationServer) DeleteIntegration(ctx context.Context, in *oncall.UUIDsReq) (*oncall.BaseResp, error) {
	l := integrationlogic.NewDeleteIntegrationLogic(ctx, s.svcCtx)
	return l.DeleteIntegration(in)
}

// AlertRoute management
func (s *IntegrationServer) CreateAlertRoute(ctx context.Context, in *oncall.CreateAlertRouteReq) (*oncall.BaseUUIDResp, error) {
	l := integrationlogic.NewCreateAlertRouteLogic(ctx, s.svcCtx)
	return l.CreateAlertRoute(in)
}

func (s *IntegrationServer) UpdateAlertRoute(ctx context.Context, in *oncall.UpdateAlertRouteReq) (*oncall.BaseResp, error) {
	l := integrationlogic.NewUpdateAlertRouteLogic(ctx, s.svcCtx)
	return l.UpdateAlertRoute(in)
}

func (s *IntegrationServer) GetAlertRouteList(ctx context.Context, in *oncall.AlertRouteListReq) (*oncall.AlertRouteListResp, error) {
	l := integrationlogic.NewGetAlertRouteListLogic(ctx, s.svcCtx)
	return l.GetAlertRouteList(in)
}

func (s *IntegrationServer) GetAlertRouteById(ctx context.Context, in *oncall.IDReq) (*oncall.AlertRouteInfo, error) {
	l := integrationlogic.NewGetAlertRouteByIdLogic(ctx, s.svcCtx)
	return l.GetAlertRouteById(in)
}

func (s *IntegrationServer) DeleteAlertRoute(ctx context.Context, in *oncall.IDsReq) (*oncall.BaseResp, error) {
	l := integrationlogic.NewDeleteAlertRouteLogic(ctx, s.svcCtx)
	return l.DeleteAlertRoute(in)
}

// 关联管理
func (s *IntegrationServer) AssociateAlertWithSpace(ctx context.Context, in *oncall.AssociateAlertWithSpaceReq) (*oncall.BaseResp, error) {
	l := integrationlogic.NewAssociateAlertWithSpaceLogic(ctx, s.svcCtx)
	return l.AssociateAlertWithSpace(in)
}

func (s *IntegrationServer) AssociateIntegrationWithSpace(ctx context.Context, in *oncall.AssociateIntegrationWithSpaceReq) (*oncall.BaseResp, error) {
	l := integrationlogic.NewAssociateIntegrationWithSpaceLogic(ctx, s.svcCtx)
	return l.AssociateIntegrationWithSpace(in)
}

// Assignment Policy management - 分配策略管理
func (s *IntegrationServer) CreateAssignmentPolicy(ctx context.Context, in *oncall.CreateAssignmentPolicyReq) (*oncall.BaseUUIDResp, error) {
	l := integrationlogic.NewCreateAssignmentPolicyLogic(ctx, s.svcCtx)
	return l.CreateAssignmentPolicy(in)
}

func (s *IntegrationServer) UpdateAssignmentPolicy(ctx context.Context, in *oncall.UpdateAssignmentPolicyReq) (*oncall.BaseResp, error) {
	l := integrationlogic.NewUpdateAssignmentPolicyLogic(ctx, s.svcCtx)
	return l.UpdateAssignmentPolicy(in)
}

func (s *IntegrationServer) GetAssignmentPolicyList(ctx context.Context, in *oncall.AssignmentPolicyListReq) (*oncall.AssignmentPolicyListResp, error) {
	l := integrationlogic.NewGetAssignmentPolicyListLogic(ctx, s.svcCtx)
	return l.GetAssignmentPolicyList(in)
}

func (s *IntegrationServer) GetAssignmentPolicyById(ctx context.Context, in *oncall.UUIDReq) (*oncall.AssignmentPolicyInfo, error) {
	l := integrationlogic.NewGetAssignmentPolicyByIdLogic(ctx, s.svcCtx)
	return l.GetAssignmentPolicyById(in)
}

func (s *IntegrationServer) DeleteAssignmentPolicy(ctx context.Context, in *oncall.UUIDsReq) (*oncall.BaseResp, error) {
	l := integrationlogic.NewDeleteAssignmentPolicyLogic(ctx, s.svcCtx)
	return l.DeleteAssignmentPolicy(in)
}
