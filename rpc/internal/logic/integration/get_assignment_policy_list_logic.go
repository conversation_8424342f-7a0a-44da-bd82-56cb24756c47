package integrationlogic

import (
	"context"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/predicate"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/space"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAssignmentPolicyListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetAssignmentPolicyListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAssignmentPolicyListLogic {
	return &GetAssignmentPolicyListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetAssignmentPolicyListLogic) GetAssignmentPolicyList(in *oncall.AssignmentPolicyListReq) (*oncall.AssignmentPolicyListResp, error) {
	// 构建查询条件
	var predicates []predicate.AssignmentPolicy

	// 添加过滤条件
	if in.SpaceId != nil && *in.SpaceId != 0 {
		// 通过空间关联查询分配策略
		predicates = append(predicates, assignmentpolicy.HasSpaceWith(space.IDEQ(uint64(*in.SpaceId))))
	}
	if in.Status != nil && *in.Status != "" {
		predicates = append(predicates, assignmentpolicy.StatusEQ(assignmentpolicy.Status(*in.Status)))
	}

	// 使用 ent 分页方法
	result, err := l.svcCtx.DB.AssignmentPolicy.Query().
		Where(predicates...).
		WithSpace(). // 预加载关联的空间数据
		Order(ent.Desc(assignmentpolicy.FieldCreatedAt)).
		Page(l.ctx, in.Page, in.PageSize)

	if err != nil {
		l.Errorf("查询分配策略列表失败: %v", err)
		return nil, fmt.Errorf("查询分配策略列表失败: %v", err)
	}

	// 转换为响应格式
	var list []*oncall.AssignmentPolicyInfo
	for _, item := range result.List {
		// 获取关联的空间ID
		var spaceId int64
		if item.Edges.Space != nil {
			spaceId = int64(item.Edges.Space.ID)
		}

		// 调试：打印原始数据
		l.Infof("调试 - 策略ID: %s, RuleID: %s", item.ID.String(), item.RuleID)
		l.Infof("调试 - Layers原始数据: %+v", item.Layers)
		l.Infof("调试 - TimeFilters原始数据: %+v", item.TimeFilters)
		l.Infof("调试 - Filters原始数据: %+v", item.Filters)

		// 解析JSON数据
		layers, err := ConvertJSONToLayers(item.Layers)
		if err != nil {
			l.Errorf("解析分层通知数据失败: %v", err)
			layers = []*oncall.NotificationLayer{} // 使用空数组作为默认值
		} else {
			l.Infof("调试 - 成功解析Layers，数量: %d", len(layers))
		}

		timeFilters, err := ConvertJSONToTimeFilters(item.TimeFilters)
		if err != nil {
			l.Errorf("解析时间过滤器数据失败: %v", err)
			timeFilters = []*oncall.TimeFilter{} // 使用空数组作为默认值
		} else {
			l.Infof("调试 - 成功解析TimeFilters，数量: %d", len(timeFilters))
		}

		filters, err := ConvertJSONToFilters(item.Filters)
		if err != nil {
			l.Errorf("解析过滤器组数据失败: %v", err)
			filters = []*oncall.FilterConditionGroup{} // 使用空数组作为默认值
		} else {
			l.Infof("调试 - 成功解析Filters，数量: %d", len(filters))
		}

		info := &oncall.AssignmentPolicyInfo{
			Id:          item.ID.String(),
			SpaceId:     spaceId,
			RuleId:      item.RuleID,
			RuleName:    item.RuleName,
			TemplateId:  item.TemplateID,
			Description: item.Description,
			Status:      string(item.Status),
			Priority:    item.Priority,
			CreatedAt:   item.CreatedAt.Unix(),
			UpdatedAt:   item.UpdatedAt.Unix(),
			UpdatedBy:   item.UpdatedBy,
			Layers:      layers,
			AggrWindow:  item.AggrWindow,
			TimeFilters: timeFilters,
			Filters:     filters,
		}
		list = append(list, info)
	}

	return &oncall.AssignmentPolicyListResp{
		Total: result.PageDetails.Total,
		List:  list,
	}, nil
}
