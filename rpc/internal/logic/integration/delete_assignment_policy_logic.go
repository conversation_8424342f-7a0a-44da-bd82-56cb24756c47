package integrationlogic

import (
	"context"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteAssignmentPolicyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteAssignmentPolicyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteAssignmentPolicyLogic {
	return &DeleteAssignmentPolicyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *DeleteAssignmentPolicyLogic) DeleteAssignmentPolicy(in *oncall.UUIDsReq) (*oncall.BaseResp, error) {
	// 参数验证
	if len(in.Ids) == 0 {
		l.<PERSON>("分配策略ID列表不能为空")
		return &oncall.BaseResp{
			Msg: "分配策略ID列表不能为空",
		}, nil
	}

	// 批量删除分配策略
	deletedCount, err := l.svcCtx.DB.AssignmentPolicy.Delete().
		Where(assignmentpolicy.RuleIDIn(in.Ids...)).
		Exec(l.ctx)

	if err != nil {
		l.Errorf("批量删除分配策略失败: %v", err)
		return &oncall.BaseResp{
			Msg: "删除分配策略失败",
		}, nil
	}

	l.Infof("成功删除分配策略，数量: %d", deletedCount)

	return &oncall.BaseResp{
		Msg: fmt.Sprintf("成功删除 %d 个分配策略", deletedCount),
	}, nil
}
