package integrationlogic

import (
	"fmt"
	"time"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"
)

// getStringFromMap 从map中安全获取字符串值
func getStringFromMap(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// GenerateRuleId 生成规则ID
func GenerateRuleId() string {
	return fmt.Sprintf("rule-%d", time.Now().UnixNano())
}

// ConvertLayersToJSON 转换分层通知数据为JSON
func ConvertLayersToJSON(layers []*oncall.NotificationLayer) ([]map[string]interface{}, error) {
	if layers == nil {
		return []map[string]interface{}{}, nil
	}

	result := make([]map[string]interface{}, 0, len(layers))
	for _, layer := range layers {
		layerMap := map[string]interface{}{
			"maxTimes":       layer.MaxTimes,
			"notifyStep":     layer.NotifyStep,
			"escalateWindow": layer.EscalateWindow,
			"forceEscalate":  layer.ForceEscalate,
		}

		if layer.Target != nil {
			target := map[string]interface{}{
				"teamIds":   layer.Target.TeamIds,
				"personIds": layer.Target.PersonIds,
			}

			// 转换排班角色映射
			if layer.Target.ScheduleToRoleIds != nil {
				scheduleToRoleIds := make(map[string][]int64)
				for k, v := range layer.Target.ScheduleToRoleIds {
					scheduleToRoleIds[k] = v.RoleIds
				}
				target["scheduleToRoleIds"] = scheduleToRoleIds
			}

			// 转换通知方式
			if layer.Target.By != nil {
				by := map[string]interface{}{
					"followPreference": layer.Target.By.FollowPreference,
					"critical":         layer.Target.By.Critical,
					"warning":          layer.Target.By.Warning,
					"info":             layer.Target.By.Info,
				}
				target["by"] = by
			}

			// 转换Webhooks
			if layer.Target.Webhooks != nil {
				webhooks := make([]map[string]interface{}, 0, len(layer.Target.Webhooks))
				for _, webhook := range layer.Target.Webhooks {
					webhookMap := map[string]interface{}{
						"type":     webhook.Type,
						"settings": webhook.Settings,
					}
					webhooks = append(webhooks, webhookMap)
				}
				target["webhooks"] = webhooks
			}

			layerMap["target"] = target
		}

		result = append(result, layerMap)
	}

	return result, nil
}

// ConvertTimeFiltersToJSON 转换时间过滤器数据为JSON
func ConvertTimeFiltersToJSON(timeFilters []*oncall.TimeFilter) ([]map[string]interface{}, error) {
	if timeFilters == nil {
		return []map[string]interface{}{}, nil
	}

	result := make([]map[string]interface{}, 0, len(timeFilters))
	for _, filter := range timeFilters {
		filterMap := map[string]interface{}{
			"start":  filter.Start,
			"end":    filter.End,
			"repeat": filter.Repeat,
			"calId":  filter.CalId,
			"isOff":  filter.IsOff,
		}
		result = append(result, filterMap)
	}

	return result, nil
}

// ConvertFiltersToJSON 转换过滤器组数据为JSON
func ConvertFiltersToJSON(filters []*oncall.FilterConditionGroup) ([]map[string]interface{}, error) {
	if filters == nil {
		return []map[string]interface{}{}, nil
	}

	result := make([]map[string]interface{}, 0, len(filters))
	for _, group := range filters {
		conditions := make([]map[string]interface{}, 0, len(group.Conditions))
		for _, condition := range group.Conditions {
			conditionMap := map[string]interface{}{
				"key":  condition.Key,
				"oper": condition.Oper,
				"vals": condition.Vals,
			}
			conditions = append(conditions, conditionMap)
		}
		result = append(result, map[string]interface{}{
			"conditions": conditions,
		})
	}

	return result, nil
}

// ConvertJSONToLayers 将JSON数据转换为NotificationLayer数组
func ConvertJSONToLayers(layersJSON []map[string]interface{}) ([]*oncall.NotificationLayer, error) {
	if layersJSON == nil {
		return []*oncall.NotificationLayer{}, nil
	}

	result := make([]*oncall.NotificationLayer, 0, len(layersJSON))
	for _, layerMap := range layersJSON {
		layer := &oncall.NotificationLayer{}

		// 解析基本字段
		if maxTimes, ok := layerMap["maxTimes"].(float64); ok {
			layer.MaxTimes = int32(maxTimes)
		}
		if notifyStep, ok := layerMap["notifyStep"].(float64); ok {
			layer.NotifyStep = int32(notifyStep)
		}
		if escalateWindow, ok := layerMap["escalateWindow"].(float64); ok {
			layer.EscalateWindow = int32(escalateWindow)
		}
		if forceEscalate, ok := layerMap["forceEscalate"].(bool); ok {
			layer.ForceEscalate = forceEscalate
		}

		// 解析target字段
		if targetMap, ok := layerMap["target"].(map[string]interface{}); ok {
			target := &oncall.NotificationTarget{}

			// 解析teamIds
			if teamIds, ok := targetMap["teamIds"].([]interface{}); ok {
				for _, id := range teamIds {
					if teamId, ok := id.(float64); ok {
						target.TeamIds = append(target.TeamIds, int64(teamId))
					}
				}
			}

			// 解析personIds
			if personIds, ok := targetMap["personIds"].([]interface{}); ok {
				for _, id := range personIds {
					if personId, ok := id.(float64); ok {
						target.PersonIds = append(target.PersonIds, int64(personId))
					}
				}
			}

			// 解析scheduleToRoleIds
			if scheduleToRoleIds, ok := targetMap["scheduleToRoleIds"].(map[string]interface{}); ok {
				target.ScheduleToRoleIds = make(map[string]*oncall.ScheduleRoles)
				for scheduleId, roleIdsInterface := range scheduleToRoleIds {
					if roleIdsList, ok := roleIdsInterface.([]interface{}); ok {
						scheduleRoles := &oncall.ScheduleRoles{}
						for _, roleId := range roleIdsList {
							if id, ok := roleId.(float64); ok {
								scheduleRoles.RoleIds = append(scheduleRoles.RoleIds, int64(id))
							}
						}
						target.ScheduleToRoleIds[scheduleId] = scheduleRoles
					}
				}
			}

			// 解析by字段
			if byMap, ok := targetMap["by"].(map[string]interface{}); ok {
				by := &oncall.NotificationBy{}
				if followPreference, ok := byMap["followPreference"].(bool); ok {
					by.FollowPreference = followPreference
				}
				if critical, ok := byMap["critical"].([]interface{}); ok {
					for _, method := range critical {
						if methodStr, ok := method.(string); ok {
							by.Critical = append(by.Critical, methodStr)
						}
					}
				}
				if warning, ok := byMap["warning"].([]interface{}); ok {
					for _, method := range warning {
						if methodStr, ok := method.(string); ok {
							by.Warning = append(by.Warning, methodStr)
						}
					}
				}
				if info, ok := byMap["info"].([]interface{}); ok {
					for _, method := range info {
						if methodStr, ok := method.(string); ok {
							by.Info = append(by.Info, methodStr)
						}
					}
				}
				target.By = by
			}

			// 解析webhooks字段
			if webhooks, ok := targetMap["webhooks"].([]interface{}); ok {
				for _, webhookInterface := range webhooks {
					if webhookMap, ok := webhookInterface.(map[string]interface{}); ok {
						webhook := &oncall.Webhook{}
						if webhookType, ok := webhookMap["type"].(string); ok {
							webhook.Type = webhookType
						}
						if settings, ok := webhookMap["settings"].(map[string]interface{}); ok {
							// 转换map[string]interface{}为map[string]string
							webhook.Settings = make(map[string]string)
							for k, v := range settings {
								if strVal, ok := v.(string); ok {
									webhook.Settings[k] = strVal
								} else {
									// 对于非字符串值，转换为字符串
									webhook.Settings[k] = fmt.Sprintf("%v", v)
								}
							}
						}
						target.Webhooks = append(target.Webhooks, webhook)
					}
				}
			}

			layer.Target = target
		}

		result = append(result, layer)
	}

	return result, nil
}

// ConvertJSONToTimeFilters 将JSON数据转换为TimeFilter数组
func ConvertJSONToTimeFilters(timeFiltersJSON []map[string]interface{}) ([]*oncall.TimeFilter, error) {
	if timeFiltersJSON == nil {
		return []*oncall.TimeFilter{}, nil
	}

	result := make([]*oncall.TimeFilter, 0, len(timeFiltersJSON))
	for _, filterMap := range timeFiltersJSON {
		filter := &oncall.TimeFilter{}

		if start, ok := filterMap["start"].(string); ok {
			filter.Start = start
		}
		if end, ok := filterMap["end"].(string); ok {
			filter.End = end
		}
		if calId, ok := filterMap["calId"].(string); ok {
			filter.CalId = calId
		}
		if isOff, ok := filterMap["isOff"].(bool); ok {
			filter.IsOff = isOff
		}
		if repeat, ok := filterMap["repeat"].([]interface{}); ok {
			for _, day := range repeat {
				if dayInt, ok := day.(float64); ok {
					filter.Repeat = append(filter.Repeat, int32(dayInt))
				}
			}
		}

		result = append(result, filter)
	}

	return result, nil
}

// ConvertJSONToFilters 将JSON数据转换为FilterConditionGroup数组
func ConvertJSONToFilters(filtersJSON []map[string]interface{}) ([]*oncall.FilterConditionGroup, error) {
	if filtersJSON == nil {
		return []*oncall.FilterConditionGroup{}, nil
	}

	result := make([]*oncall.FilterConditionGroup, 0, len(filtersJSON))
	for _, groupMap := range filtersJSON {
		group := &oncall.FilterConditionGroup{}

		// 检查是否有conditions字段（新格式）
		if conditions, ok := groupMap["conditions"].([]interface{}); ok {
			for _, conditionInterface := range conditions {
				if conditionMap, ok := conditionInterface.(map[string]interface{}); ok {
					condition := &oncall.FilterCondition{}
					if key, ok := conditionMap["key"].(string); ok {
						condition.Key = key
					}
					if oper, ok := conditionMap["oper"].(string); ok {
						condition.Oper = oper
					}
					if vals, ok := conditionMap["vals"].([]interface{}); ok {
						for _, val := range vals {
							if valStr, ok := val.(string); ok {
								condition.Vals = append(condition.Vals, valStr)
							}
						}
					}
					group.Conditions = append(group.Conditions, condition)
				}
			}
		} else {
			// 如果没有conditions字段，说明这个map本身就是一个condition（旧格式兼容）
			condition := &oncall.FilterCondition{}
			if key, ok := groupMap["key"].(string); ok {
				condition.Key = key
			}
			if oper, ok := groupMap["oper"].(string); ok {
				condition.Oper = oper
			}
			if vals, ok := groupMap["vals"].([]interface{}); ok {
				for _, val := range vals {
					if valStr, ok := val.(string); ok {
						condition.Vals = append(condition.Vals, valStr)
					}
				}
			}
			group.Conditions = append(group.Conditions, condition)
		}

		result = append(result, group)
	}

	return result, nil
}
