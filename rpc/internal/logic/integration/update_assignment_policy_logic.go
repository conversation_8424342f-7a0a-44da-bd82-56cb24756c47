package integrationlogic

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateAssignmentPolicyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateAssignmentPolicyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateAssignmentPolicyLogic {
	return &UpdateAssignmentPolicyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UpdateAssignmentPolicyLogic) UpdateAssignmentPolicy(in *oncall.UpdateAssignmentPolicyReq) (*oncall.BaseResp, error) {
	// 参数验证
	if in.RuleId == "" {
		l.<PERSON><PERSON>("规则ID不能为空")
		return &oncall.BaseResp{
			Msg: "规则ID不能为空",
		}, nil
	}

	// 查找要更新的分配策略
	policy, err := l.svcCtx.DB.AssignmentPolicy.Query().
		Where(assignmentpolicy.RuleIDEQ(in.RuleId)).
		First(l.ctx)

	if err != nil {
		l.Errorf("查找分配策略失败: %v", err)
		return &oncall.BaseResp{
			Msg: "分配策略不存在",
		}, nil
	}

	// 构建更新操作
	update := l.svcCtx.DB.AssignmentPolicy.UpdateOne(policy)

	// 使用SetNotNil方法进行条件更新
	if in.RuleName != "" {
		update = update.SetRuleName(in.RuleName)
	}
	if in.TemplateId != "" {
		update = update.SetTemplateID(in.TemplateId)
	}
	if in.Description != "" {
		update = update.SetDescription(in.Description)
	}
	if in.Status != "" {
		update = update.SetStatus(assignmentpolicy.Status(in.Status))
	}
	if in.Priority != 0 {
		update = update.SetPriority(in.Priority)
	}
	if in.AggrWindow != 0 {
		update = update.SetAggrWindow(in.AggrWindow)
	}
	if in.UpdatedBy != 0 {
		update = update.SetUpdatedBy(in.UpdatedBy)
	}

	// 处理复杂字段的更新
	if len(in.Layers) > 0 {
		layersJSON, err := ConvertLayersToJSON(in.Layers)
		if err != nil {
			l.Errorf("转换分层通知数据失败: %v", err)
			return &oncall.BaseResp{
				Msg: "转换分层通知数据失败",
			}, nil
		}
		update = update.SetLayers(layersJSON)
	}

	if len(in.TimeFilters) > 0 {
		timeFiltersJSON, err := ConvertTimeFiltersToJSON(in.TimeFilters)
		if err != nil {
			l.Errorf("转换时间过滤器数据失败: %v", err)
			return &oncall.BaseResp{
				Msg: "转换时间过滤器数据失败",
			}, nil
		}
		update = update.SetTimeFilters(timeFiltersJSON)
	}

	if len(in.Filters) > 0 {
		filtersJSON, err := ConvertFiltersToJSON(in.Filters)
		if err != nil {
			l.Errorf("转换过滤器组数据失败: %v", err)
			return &oncall.BaseResp{
				Msg: "转换过滤器组数据失败",
			}, nil
		}
		update = update.SetFilters(filtersJSON)
	}

	// 处理空间关联更新
	if in.SpaceId != 0 {
		space, err := l.svcCtx.DB.Space.Get(l.ctx, uint64(in.SpaceId))
		if err != nil {
			l.Errorf("获取空间失败: %v", err)
			return &oncall.BaseResp{
				Msg: "关联空间不存在",
			}, nil
		}
		update = update.SetSpace(space)
	}

	// 执行更新
	_, err = update.Save(l.ctx)
	if err != nil {
		l.Errorf("更新分配策略失败: %v", err)
		return &oncall.BaseResp{
			Msg: "更新分配策略失败",
		}, nil
	}

	l.Infof("成功更新分配策略: %s", in.RuleId)

	return &oncall.BaseResp{
		Msg: "更新分配策略成功",
	}, nil
}
