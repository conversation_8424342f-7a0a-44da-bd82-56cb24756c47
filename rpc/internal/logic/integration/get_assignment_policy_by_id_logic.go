package integrationlogic

import (
	"context"
	"fmt"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAssignmentPolicyByIdLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetAssignmentPolicyByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAssignmentPolicyByIdLogic {
	return &GetAssignmentPolicyByIdLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetAssignmentPolicyByIdLogic) GetAssignmentPolicyById(in *oncall.UUIDReq) (*oncall.AssignmentPolicyInfo, error) {
	// 参数验证
	if in.Id == "" {
		l.Errorf("分配策略ID不能为空")
		return nil, fmt.Errorf("分配策略ID不能为空")
	}

	// 根据规则ID查询分配策略 (in.Id 是 rule_id，不是UUID)
	policy, err := l.svcCtx.DB.AssignmentPolicy.Query().
		Where(assignmentpolicy.RuleIDEQ(in.Id)).
		WithSpace(). // 预加载关联的空间数据
		First(l.ctx)

	if err != nil {
		l.Errorf("查询分配策略失败: %v", err)
		return nil, fmt.Errorf("查询分配策略失败: %v", err)
	}

	// 获取关联的空间ID
	var spaceId int64
	if policy.Edges.Space != nil {
		spaceId = int64(policy.Edges.Space.ID)
	}

	// 解析JSON数据
	layers, err := ConvertJSONToLayers(policy.Layers)
	if err != nil {
		l.Errorf("解析分层通知数据失败: %v", err)
		layers = []*oncall.NotificationLayer{} // 使用空数组作为默认值
	}

	timeFilters, err := ConvertJSONToTimeFilters(policy.TimeFilters)
	if err != nil {
		l.Errorf("解析时间过滤器数据失败: %v", err)
		timeFilters = []*oncall.TimeFilter{} // 使用空数组作为默认值
	}

	filters, err := ConvertJSONToFilters(policy.Filters)
	if err != nil {
		l.Errorf("解析过滤器组数据失败: %v", err)
		filters = []*oncall.FilterConditionGroup{} // 使用空数组作为默认值
	}

	// 构建响应数据
	info := &oncall.AssignmentPolicyInfo{
		Id:          policy.ID.String(),
		SpaceId:     spaceId,
		RuleId:      policy.RuleID,
		RuleName:    policy.RuleName,
		TemplateId:  policy.TemplateID,
		Description: policy.Description,
		Status:      string(policy.Status),
		Priority:    policy.Priority,
		CreatedAt:   policy.CreatedAt.Unix(),
		UpdatedAt:   policy.UpdatedAt.Unix(),
		UpdatedBy:   policy.UpdatedBy,
		Layers:      layers,
		AggrWindow:  policy.AggrWindow,
		TimeFilters: timeFilters,
		Filters:     filters,
	}

	return info, nil
}
