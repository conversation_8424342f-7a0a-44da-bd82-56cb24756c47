package integrationlogic

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/ent/assignmentpolicy"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/internal/svc"
	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateAssignmentPolicyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateAssignmentPolicyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAssignmentPolicyLogic {
	return &CreateAssignmentPolicyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// Assignment Policy management - 分配策略管理
func (l *CreateAssignmentPolicyLogic) CreateAssignmentPolicy(in *oncall.CreateAssignmentPolicyReq) (*oncall.BaseUUIDResp, error) {
	// 参数验证
	if in.SpaceId == 0 {
		l.Errorf("空间ID不能为空")
		return &oncall.BaseUUIDResp{
			Msg: "空间ID不能为空",
		}, nil
	}
	if in.RuleName == "" {
		l.Errorf("规则名称不能为空")
		return &oncall.BaseUUIDResp{
			Msg: "规则名称不能为空",
		}, nil
	}

	// 生成规则ID (使用时间戳格式)
	ruleId := GenerateRuleId()

	// 调试：打印输入数据
	l.Infof("调试 - 创建策略输入数据:")
	l.Infof("调试 - Layers数量: %d", len(in.Layers))
	l.Infof("调试 - TimeFilters数量: %d", len(in.TimeFilters))
	l.Infof("调试 - Filters数量: %d", len(in.Filters))

	// 转换分层通知数据为JSON
	layersJSON, err := ConvertLayersToJSON(in.Layers)
	if err != nil {
		l.Errorf("转换分层通知数据失败: %v", err)
		return &oncall.BaseUUIDResp{
			Msg: "转换分层通知数据失败",
		}, nil
	}
	l.Infof("调试 - 转换后的LayersJSON: %+v", layersJSON)

	// 转换时间过滤器数据为JSON
	timeFiltersJSON, err := ConvertTimeFiltersToJSON(in.TimeFilters)
	if err != nil {
		l.Errorf("转换时间过滤器数据失败: %v", err)
		return &oncall.BaseUUIDResp{
			Msg: "转换时间过滤器数据失败",
		}, nil
	}
	l.Infof("调试 - 转换后的TimeFiltersJSON: %+v", timeFiltersJSON)

	// 转换过滤器组数据为JSON
	filtersJSON, err := ConvertFiltersToJSON(in.Filters)
	if err != nil {
		l.Errorf("转换过滤器组数据失败: %v", err)
		return &oncall.BaseUUIDResp{
			Msg: "转换过滤器组数据失败",
		}, nil
	}
	l.Infof("调试 - 转换后的FiltersJSON: %+v", filtersJSON)

	// 获取空间实体
	space, err := l.svcCtx.DB.Space.Get(l.ctx, uint64(in.SpaceId))
	if err != nil {
		l.Errorf("获取空间失败: %v", err)
		return &oncall.BaseUUIDResp{
			Msg: "关联空间不存在",
		}, nil
	}

	// 创建分配策略记录 (UUID会自动生成)
	policy, err := l.svcCtx.DB.AssignmentPolicy.Create().
		SetRuleID(ruleId).
		SetRuleName(in.RuleName).
		SetNillableTemplateID(&in.TemplateId).
		SetNillableDescription(&in.Description).
		SetStatus(assignmentpolicy.Status(in.Status)).
		SetPriority(in.Priority).
		SetLayers(layersJSON).
		SetAggrWindow(in.AggrWindow).
		SetTimeFilters(timeFiltersJSON).
		SetFilters(filtersJSON).
		SetNillableUpdatedBy(&in.UpdatedBy).
		SetSpace(space). // 设置空间关联
		Save(l.ctx)

	if err != nil {
		l.Errorf("创建分配策略失败: %v", err)
		return &oncall.BaseUUIDResp{
			Msg: "创建分配策略失败",
		}, nil
	}

	l.Infof("成功创建分配策略: %s, 规则ID: %s, 空间ID: %d", in.RuleName, ruleId, in.SpaceId)

	return &oncall.BaseUUIDResp{
		Id:  policy.ID.String(),
		Msg: "创建分配策略成功",
	}, nil
}
