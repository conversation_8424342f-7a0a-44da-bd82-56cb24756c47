// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: oncall.proto

package integration

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AlertInfo                        = oncall.AlertInfo
	AlertListReq                     = oncall.AlertListReq
	AlertListResp                    = oncall.AlertListResp
	AlertRouteInfo                   = oncall.AlertRouteInfo
	AlertRouteListReq                = oncall.AlertRouteListReq
	AlertRouteListResp               = oncall.AlertRouteListResp
	AssignmentPolicyInfo             = oncall.AssignmentPolicyInfo
	AssignmentPolicyListReq          = oncall.AssignmentPolicyListReq
	AssignmentPolicyListResp         = oncall.AssignmentPolicyListResp
	AssociateAlertWithSpaceReq       = oncall.AssociateAlertWithSpaceReq
	AssociateIntegrationWithSpaceReq = oncall.AssociateIntegrationWithSpaceReq
	BaseIDResp                       = oncall.BaseIDResp
	BaseMsg                          = oncall.BaseMsg
	BaseResp                         = oncall.BaseResp
	BaseUUIDResp                     = oncall.BaseUUIDResp
	CreateAlertRouteReq              = oncall.CreateAlertRouteReq
	CreateAssignmentPolicyReq        = oncall.CreateAssignmentPolicyReq
	CreateIntegrationReq             = oncall.CreateIntegrationReq
	CreateSpaceReq                   = oncall.CreateSpaceReq
	DataSourceInfo                   = oncall.DataSourceInfo
	DataSourceListReq                = oncall.DataSourceListReq
	DataSourceListResp               = oncall.DataSourceListResp
	Empty                            = oncall.Empty
	FilterCondition                  = oncall.FilterCondition
	FilterConditionGroup             = oncall.FilterConditionGroup
	IDReq                            = oncall.IDReq
	IDsReq                           = oncall.IDsReq
	IncidentInfo                     = oncall.IncidentInfo
	IncidentListReq                  = oncall.IncidentListReq
	IncidentListResp                 = oncall.IncidentListResp
	IntegrationInfo                  = oncall.IntegrationInfo
	IntegrationListReq               = oncall.IntegrationListReq
	IntegrationListResp              = oncall.IntegrationListResp
	IssueStats                       = oncall.IssueStats
	NotificationBy                   = oncall.NotificationBy
	NotificationLayer                = oncall.NotificationLayer
	NotificationTarget               = oncall.NotificationTarget
	RawAlertInfo                     = oncall.RawAlertInfo
	RawAlertListReq                  = oncall.RawAlertListReq
	RawAlertListResp                 = oncall.RawAlertListResp
	RouteCase                        = oncall.RouteCase
	RouteCondition                   = oncall.RouteCondition
	RouteDefault                     = oncall.RouteDefault
	ScheduleRoles                    = oncall.ScheduleRoles
	SpaceInfo                        = oncall.SpaceInfo
	SpaceListReq                     = oncall.SpaceListReq
	SpaceListResp                    = oncall.SpaceListResp
	TimeFilter                       = oncall.TimeFilter
	UUIDReq                          = oncall.UUIDReq
	UUIDsReq                         = oncall.UUIDsReq
	UpdateAlertRouteReq              = oncall.UpdateAlertRouteReq
	UpdateAssignmentPolicyReq        = oncall.UpdateAssignmentPolicyReq
	UpdateIntegrationReq             = oncall.UpdateIntegrationReq
	UpdateSpaceReq                   = oncall.UpdateSpaceReq
	Webhook                          = oncall.Webhook

	Integration interface {
		// Integration management
		CreateIntegration(ctx context.Context, in *CreateIntegrationReq, opts ...grpc.CallOption) (*BaseUUIDResp, error)
		UpdateIntegration(ctx context.Context, in *UpdateIntegrationReq, opts ...grpc.CallOption) (*BaseResp, error)
		GetIntegrationList(ctx context.Context, in *IntegrationListReq, opts ...grpc.CallOption) (*IntegrationListResp, error)
		GetIntegrationById(ctx context.Context, in *UUIDReq, opts ...grpc.CallOption) (*IntegrationInfo, error)
		DeleteIntegration(ctx context.Context, in *UUIDsReq, opts ...grpc.CallOption) (*BaseResp, error)
		// AlertRoute management
		CreateAlertRoute(ctx context.Context, in *CreateAlertRouteReq, opts ...grpc.CallOption) (*BaseUUIDResp, error)
		UpdateAlertRoute(ctx context.Context, in *UpdateAlertRouteReq, opts ...grpc.CallOption) (*BaseResp, error)
		GetAlertRouteList(ctx context.Context, in *AlertRouteListReq, opts ...grpc.CallOption) (*AlertRouteListResp, error)
		GetAlertRouteById(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*AlertRouteInfo, error)
		DeleteAlertRoute(ctx context.Context, in *IDsReq, opts ...grpc.CallOption) (*BaseResp, error)
		// 关联管理
		AssociateAlertWithSpace(ctx context.Context, in *AssociateAlertWithSpaceReq, opts ...grpc.CallOption) (*BaseResp, error)
		AssociateIntegrationWithSpace(ctx context.Context, in *AssociateIntegrationWithSpaceReq, opts ...grpc.CallOption) (*BaseResp, error)
		// Assignment Policy management - 分配策略管理
		CreateAssignmentPolicy(ctx context.Context, in *CreateAssignmentPolicyReq, opts ...grpc.CallOption) (*BaseUUIDResp, error)
		UpdateAssignmentPolicy(ctx context.Context, in *UpdateAssignmentPolicyReq, opts ...grpc.CallOption) (*BaseResp, error)
		GetAssignmentPolicyList(ctx context.Context, in *AssignmentPolicyListReq, opts ...grpc.CallOption) (*AssignmentPolicyListResp, error)
		GetAssignmentPolicyById(ctx context.Context, in *UUIDReq, opts ...grpc.CallOption) (*AssignmentPolicyInfo, error)
		DeleteAssignmentPolicy(ctx context.Context, in *UUIDsReq, opts ...grpc.CallOption) (*BaseResp, error)
	}

	defaultIntegration struct {
		cli zrpc.Client
	}
)

func NewIntegration(cli zrpc.Client) Integration {
	return &defaultIntegration{
		cli: cli,
	}
}

// Integration management
func (m *defaultIntegration) CreateIntegration(ctx context.Context, in *CreateIntegrationReq, opts ...grpc.CallOption) (*BaseUUIDResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.CreateIntegration(ctx, in, opts...)
}

func (m *defaultIntegration) UpdateIntegration(ctx context.Context, in *UpdateIntegrationReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.UpdateIntegration(ctx, in, opts...)
}

func (m *defaultIntegration) GetIntegrationList(ctx context.Context, in *IntegrationListReq, opts ...grpc.CallOption) (*IntegrationListResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.GetIntegrationList(ctx, in, opts...)
}

func (m *defaultIntegration) GetIntegrationById(ctx context.Context, in *UUIDReq, opts ...grpc.CallOption) (*IntegrationInfo, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.GetIntegrationById(ctx, in, opts...)
}

func (m *defaultIntegration) DeleteIntegration(ctx context.Context, in *UUIDsReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.DeleteIntegration(ctx, in, opts...)
}

// AlertRoute management
func (m *defaultIntegration) CreateAlertRoute(ctx context.Context, in *CreateAlertRouteReq, opts ...grpc.CallOption) (*BaseUUIDResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.CreateAlertRoute(ctx, in, opts...)
}

func (m *defaultIntegration) UpdateAlertRoute(ctx context.Context, in *UpdateAlertRouteReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.UpdateAlertRoute(ctx, in, opts...)
}

func (m *defaultIntegration) GetAlertRouteList(ctx context.Context, in *AlertRouteListReq, opts ...grpc.CallOption) (*AlertRouteListResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.GetAlertRouteList(ctx, in, opts...)
}

func (m *defaultIntegration) GetAlertRouteById(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*AlertRouteInfo, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.GetAlertRouteById(ctx, in, opts...)
}

func (m *defaultIntegration) DeleteAlertRoute(ctx context.Context, in *IDsReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.DeleteAlertRoute(ctx, in, opts...)
}

// 关联管理
func (m *defaultIntegration) AssociateAlertWithSpace(ctx context.Context, in *AssociateAlertWithSpaceReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.AssociateAlertWithSpace(ctx, in, opts...)
}

func (m *defaultIntegration) AssociateIntegrationWithSpace(ctx context.Context, in *AssociateIntegrationWithSpaceReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.AssociateIntegrationWithSpace(ctx, in, opts...)
}

// Assignment Policy management - 分配策略管理
func (m *defaultIntegration) CreateAssignmentPolicy(ctx context.Context, in *CreateAssignmentPolicyReq, opts ...grpc.CallOption) (*BaseUUIDResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.CreateAssignmentPolicy(ctx, in, opts...)
}

func (m *defaultIntegration) UpdateAssignmentPolicy(ctx context.Context, in *UpdateAssignmentPolicyReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.UpdateAssignmentPolicy(ctx, in, opts...)
}

func (m *defaultIntegration) GetAssignmentPolicyList(ctx context.Context, in *AssignmentPolicyListReq, opts ...grpc.CallOption) (*AssignmentPolicyListResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.GetAssignmentPolicyList(ctx, in, opts...)
}

func (m *defaultIntegration) GetAssignmentPolicyById(ctx context.Context, in *UUIDReq, opts ...grpc.CallOption) (*AssignmentPolicyInfo, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.GetAssignmentPolicyById(ctx, in, opts...)
}

func (m *defaultIntegration) DeleteAssignmentPolicy(ctx context.Context, in *UUIDsReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewIntegrationClient(m.cli.Conn())
	return client.DeleteAssignmentPolicy(ctx, in, opts...)
}
