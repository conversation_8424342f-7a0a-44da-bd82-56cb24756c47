// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.5
// Source: oncall.proto

package space

import (
	"context"

	"gitlab.xsjcs.cn/zero_duty/zero_duty_oncall/rpc/pb/oncall"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	AlertInfo                        = oncall.AlertInfo
	AlertListReq                     = oncall.AlertListReq
	AlertListResp                    = oncall.AlertListResp
	AlertRouteInfo                   = oncall.AlertRouteInfo
	AlertRouteListReq                = oncall.AlertRouteListReq
	AlertRouteListResp               = oncall.AlertRouteListResp
	AssignmentPolicyInfo             = oncall.AssignmentPolicyInfo
	AssignmentPolicyListReq          = oncall.AssignmentPolicyListReq
	AssignmentPolicyListResp         = oncall.AssignmentPolicyListResp
	AssociateAlertWithSpaceReq       = oncall.AssociateAlertWithSpaceReq
	AssociateIntegrationWithSpaceReq = oncall.AssociateIntegrationWithSpaceReq
	BaseIDResp                       = oncall.BaseIDResp
	BaseMsg                          = oncall.BaseMsg
	BaseResp                         = oncall.BaseResp
	BaseUUIDResp                     = oncall.BaseUUIDResp
	CreateAlertRouteReq              = oncall.CreateAlertRouteReq
	CreateAssignmentPolicyReq        = oncall.CreateAssignmentPolicyReq
	CreateIntegrationReq             = oncall.CreateIntegrationReq
	CreateSpaceReq                   = oncall.CreateSpaceReq
	DataSourceInfo                   = oncall.DataSourceInfo
	DataSourceListReq                = oncall.DataSourceListReq
	DataSourceListResp               = oncall.DataSourceListResp
	Empty                            = oncall.Empty
	FilterCondition                  = oncall.FilterCondition
	FilterConditionGroup             = oncall.FilterConditionGroup
	IDReq                            = oncall.IDReq
	IDsReq                           = oncall.IDsReq
	IncidentInfo                     = oncall.IncidentInfo
	IncidentListReq                  = oncall.IncidentListReq
	IncidentListResp                 = oncall.IncidentListResp
	IntegrationInfo                  = oncall.IntegrationInfo
	IntegrationListReq               = oncall.IntegrationListReq
	IntegrationListResp              = oncall.IntegrationListResp
	IssueStats                       = oncall.IssueStats
	NotificationBy                   = oncall.NotificationBy
	NotificationLayer                = oncall.NotificationLayer
	NotificationTarget               = oncall.NotificationTarget
	RawAlertInfo                     = oncall.RawAlertInfo
	RawAlertListReq                  = oncall.RawAlertListReq
	RawAlertListResp                 = oncall.RawAlertListResp
	RouteCase                        = oncall.RouteCase
	RouteCondition                   = oncall.RouteCondition
	RouteDefault                     = oncall.RouteDefault
	ScheduleRoles                    = oncall.ScheduleRoles
	SpaceInfo                        = oncall.SpaceInfo
	SpaceListReq                     = oncall.SpaceListReq
	SpaceListResp                    = oncall.SpaceListResp
	TimeFilter                       = oncall.TimeFilter
	UUIDReq                          = oncall.UUIDReq
	UUIDsReq                         = oncall.UUIDsReq
	UpdateAlertRouteReq              = oncall.UpdateAlertRouteReq
	UpdateAssignmentPolicyReq        = oncall.UpdateAssignmentPolicyReq
	UpdateIntegrationReq             = oncall.UpdateIntegrationReq
	UpdateSpaceReq                   = oncall.UpdateSpaceReq
	Webhook                          = oncall.Webhook

	Space interface {
		CreateSpace(ctx context.Context, in *CreateSpaceReq, opts ...grpc.CallOption) (*BaseUUIDResp, error)
		UpdateSpace(ctx context.Context, in *UpdateSpaceReq, opts ...grpc.CallOption) (*BaseResp, error)
		GetSpaceList(ctx context.Context, in *SpaceListReq, opts ...grpc.CallOption) (*SpaceListResp, error)
		GetSpaceById(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*SpaceInfo, error)
		DeleteSpace(ctx context.Context, in *IDsReq, opts ...grpc.CallOption) (*BaseResp, error)
	}

	defaultSpace struct {
		cli zrpc.Client
	}
)

func NewSpace(cli zrpc.Client) Space {
	return &defaultSpace{
		cli: cli,
	}
}

func (m *defaultSpace) CreateSpace(ctx context.Context, in *CreateSpaceReq, opts ...grpc.CallOption) (*BaseUUIDResp, error) {
	client := oncall.NewSpaceClient(m.cli.Conn())
	return client.CreateSpace(ctx, in, opts...)
}

func (m *defaultSpace) UpdateSpace(ctx context.Context, in *UpdateSpaceReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewSpaceClient(m.cli.Conn())
	return client.UpdateSpace(ctx, in, opts...)
}

func (m *defaultSpace) GetSpaceList(ctx context.Context, in *SpaceListReq, opts ...grpc.CallOption) (*SpaceListResp, error) {
	client := oncall.NewSpaceClient(m.cli.Conn())
	return client.GetSpaceList(ctx, in, opts...)
}

func (m *defaultSpace) GetSpaceById(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*SpaceInfo, error) {
	client := oncall.NewSpaceClient(m.cli.Conn())
	return client.GetSpaceById(ctx, in, opts...)
}

func (m *defaultSpace) DeleteSpace(ctx context.Context, in *IDsReq, opts ...grpc.CallOption) (*BaseResp, error) {
	client := oncall.NewSpaceClient(m.cli.Conn())
	return client.DeleteSpace(ctx, in, opts...)
}
