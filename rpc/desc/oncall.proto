syntax = "proto3";

package oncall;

option go_package = "./oncall";

//  base message
message BaseIDResp {
    uint64 id = 1;
    string msg = 2;
}

message BaseMsg {
    string msg = 1;
}

message BaseResp {
    string msg = 1;
}

message BaseUUIDResp {
    string id = 1;
    string msg = 2;
}

message Empty {}

message IDReq {
    uint64 id = 1;
}

message IDsReq {
    repeated uint64 ids = 1;
}

// Integration related messages
message IntegrationInfo {
    string id = 1;
    string name = 2;
    string kind = 3; // private/public
    string type = 4; // prometheus/grafana/alertmanager等
    bool status = 5;
    int64 created_at = 6;
    int64 updated_at = 7;
    string last_event_time = 8; // 最后事件时间
    string webhook = 9; // webhook URL
    string tag_rules = 10; // 标签规则 JSON
    string noise_rules = 11; // 噪音规则 JSON
    string alert_processing_rules = 12; // 告警处理规则 JSON
    string alerts = 13; // 告警示例 JSON
    string routing_rules = 14; // 路由规则 JSON
}

message CreateIntegrationReq {
    string id = 1; // Integration ID (可选)
    string name = 2;
    string kind = 3; // private/public，默认为private
    string type = 4; // prometheus/grafana/alertmanager等
    bool status = 5; // 状态，默认为true
}

message UpdateIntegrationReq {
    string id = 1;
    string name = 2;
    string kind = 3; // private/public
    string type = 4; // prometheus/grafana/alertmanager等
    bool status = 5;
}

message IntegrationListReq {
    uint64 page = 1;
    uint64 page_size = 2;
    optional string name = 3;
    optional string kind = 4;
    optional string type = 5;
}

message IntegrationListResp {
    uint64 total = 1;
    repeated IntegrationInfo list = 2;
}

// DataSource related messages
message DataSourceInfo {
    string id = 1;
    string type_name = 2;
    string type = 3;
    string url = 4;
    string logo = 5;
    int64 sort = 6;
    repeated string integration_ids = 7; // 关联的集成 ID 列表
}

message DataSourceListReq {
    uint64 page = 1;
    uint64 page_size = 2;
    optional string type_name = 3;
    optional string type = 4;
}

message DataSourceListResp {
    uint64 total = 1;
    repeated DataSourceInfo list = 2;
}

// 移除 Enrichment 和 AlertRule 相关消息，这些在当前系统中不使用

// AlertRoute related messages - 路由规则
message AlertRouteInfo {
    string id = 1;
    string integration_id = 2; // 集成ID
    repeated RouteCase cases = 3; // 条件规则数组
    RouteDefault default = 4; // 默认路由
    string status = 5; // 状态
    int32 version = 6; // 版本
    int64 updated_by = 7; // 更新者ID
    int64 creator_id = 8; // 创建者ID
    int64 created_at = 9;
    int64 updated_at = 10;
}

// 路由条件规则
message RouteCase {
    repeated RouteCondition if = 1; // 条件数组
    repeated int64 channel_ids = 2; // 通道ID数组
    bool fallthrough = 3; // 是否继续
}

// 路由条件
message RouteCondition {
    string key = 1; // 字段名
    string oper = 2; // 操作符 (IN, EQ, etc.)
    repeated string vals = 3; // 值数组
}

// 默认路由
message RouteDefault {
    repeated int64 channel_ids = 1; // 默认通道ID数组
}

message CreateAlertRouteReq {
    string integration_id = 1; // 集成ID
    repeated RouteCase cases = 2; // 条件规则数组
    RouteDefault default = 3; // 默认路由
    string status = 4; // 状态
    int64 creator_id = 5; // 创建者ID
}

message UpdateAlertRouteReq {
    string id = 1;
    string integration_id = 2; // 集成ID
    repeated RouteCase cases = 3; // 条件规则数组
    RouteDefault default = 4; // 默认路由
    string status = 5; // 状态
    int64 updated_by = 6; // 更新者ID
    int32 version = 7; // 版本
}

message AlertRouteListReq {
    uint64 page = 1;
    uint64 page_size = 2;
    optional string integration_id = 3; // 集成ID
    optional string status = 4; // 状态
}

message AlertRouteListResp {
    uint64 total = 1;
    repeated AlertRouteInfo list = 2;
}

// Alert related messages
message AlertInfo {
    string id = 1;
    string alert_id = 2;
    string dedup_key = 3;
    string title = 4;
    string description = 5;
    string alert_severity = 6;
    string alert_status = 7;
    string progress = 8;
    int64 start_time = 9;
    int64 last_time = 10;
    int64 end_time = 11;
    int64 ack_time = 12;
    int64 close_time = 13;
    map<string, string> labels = 14;
    repeated string fields = 15;
    bool ever_muted = 16;
    int64 created_at = 17;
    int64 updated_at = 18;
    string data_source_type = 19;
    string data_source_name = 20;
    string channel_name = 21;
    string channel_status = 22;
    int32 event_cnt = 23;
    map<string, string> raw_data = 24;
    string space_id = 25;
    string integration_id = 26;
}

message AlertListReq {
    uint64 page = 1;
    uint64 page_size = 2;
    optional string alert_id = 3;
    optional string dedup_key = 4;
    optional string alert_severity = 5;
    optional string alert_status = 6;
    optional int64 start_time_begin = 7;
    optional int64 start_time_end = 8;
    optional string integration_id = 9;
    optional string space_id = 10; // 添加空间 ID 查询参数
}

message AlertListResp {
    uint64 total = 1;
    repeated AlertInfo list = 2;
}

// Incident related messages
message IncidentInfo {
    string id = 1;
    string dedup_key = 2;
    string space_id = 3;
    string data_source_id = 4;
    string title = 5;
    string description = 6;
    string incident_severity = 7;
    string incident_status = 8;
    string progress = 9;
    int64 start_time = 10;
    int64 last_time = 11;
    int64 end_time = 12;
    int64 ack_time = 13;
    int64 close_time = 14;
    map<string, string> labels = 15;
    repeated string fields = 16;
    string impact = 17;
    string root_cause = 18;
    string resolution = 19;
    string frequency = 20;
    bool ever_muted = 21;
    int64 created_at = 22;
    int64 updated_at = 23;
    int64 snoozed_before = 24;
    string group_method = 25;
    string detail_url = 26;
    int32 alert_cnt = 27;
    map<string, int32> alert_cnts = 28;
    string data_source_name = 29;
    string data_source_type = 30;
}

message IncidentListReq {
    uint64 page = 1;
    uint64 page_size = 2;
    optional string dedup_key = 3;
    optional string incident_severity = 4;
    optional string incident_status = 5;
    optional string frequency = 6;
    optional int64 start_time_begin = 7;
    optional int64 start_time_end = 8;
    optional string space_id = 9;
}

message IncidentListResp {
    uint64 total = 1;
    repeated IncidentInfo list = 2;
}

// Space related messages - 工作空间
message SpaceInfo {
    string id = 1;
    string name = 2;
    int64 team_id = 3; // 团队ID
    string team_name = 4; // 团队名称
    string description = 5;
    string visibility = 6; // 可见性 private/public
    int64 created_at = 7;
    int64 updated_at = 8;
    IssueStats issue_stats = 9; // 问题统计
    bool enabled = 10; // 启用状态
    string status_changed_at = 11; // 状态变更时间
}

// 问题统计
message IssueStats {
    int32 processing = 1; // 处理中
    int32 pending = 2; // 待处理
    int32 completed = 3; // 已处理
}

message CreateSpaceReq {
    string name = 1;
    int64 team_id = 2; // 团队ID
    string team_name = 3; // 团队名称
    string description = 4;
    string visibility = 5; // 可见性 private/public
    bool enabled = 6; // 启用状态
}

message UpdateSpaceReq {
    string id = 1;
    string name = 2;
    int64 team_id = 3; // 团队ID
    string team_name = 4; // 团队名称
    string description = 5;
    string visibility = 6; // 可见性 private/public
    bool enabled = 7; // 启用状态
}

message SpaceListReq {
    uint64 page = 1;
    uint64 page_size = 2;
    optional string name = 3;
}

message SpaceListResp {
    uint64 total = 1;
    repeated SpaceInfo list = 2;
}

// 添加将告警与空间关联的服务
message AssociateAlertWithSpaceReq {
    string alert_id = 1;
    string space_id = 2;
}

// 添加将集成与空间关联的服务
message AssociateIntegrationWithSpaceReq {
    string integration_id = 1;
    string space_id = 2;
}

// Assignment Policy related messages - 分配策略
message AssignmentPolicyInfo {
    string id = 1; // UUID
    int64 space_id = 2; // 空间ID
    string rule_id = 3; // 规则ID
    string rule_name = 4; // 规则名称
    string template_id = 5; // 模板ID
    string description = 6; // 描述
    string status = 7; // 状态
    int32 priority = 8; // 优先级
    int64 created_at = 9; // 创建时间
    int64 updated_at = 10; // 更新时间
    int64 updated_by = 11; // 更新者ID
    repeated NotificationLayer layers = 12; // 分层通知
    int32 aggr_window = 13; // 聚合窗口(秒)
    repeated TimeFilter time_filters = 14; // 时间过滤器
    repeated FilterConditionGroup filters = 15; // 过滤器组 - 二维数组
}

// 通知层级
message NotificationLayer {
    int32 max_times = 1; // 最大通知次数
    int32 notify_step = 2; // 通知步骤
    int32 escalate_window = 3; // 升级窗口(分钟)
    bool force_escalate = 4; // 强制升级
    NotificationTarget target = 5; // 通知目标
}

// 通知目标
message NotificationTarget {
    repeated int64 team_ids = 1; // 团队ID列表
    repeated int64 person_ids = 2; // 个人ID列表
    map<string, ScheduleRoles> schedule_to_role_ids = 3; // 排班角色映射
    NotificationBy by = 4; // 通知方式
    repeated Webhook webhooks = 5; // Webhook列表
}

// 排班角色
message ScheduleRoles {
    repeated int64 role_ids = 1; // 角色ID列表
}

// 通知方式
message NotificationBy {
    bool follow_preference = 1; // 遵循偏好设置
    repeated string critical = 2; // 严重级别通知方式
    repeated string warning = 3; // 警告级别通知方式
    repeated string info = 4; // 信息级别通知方式
}

// Webhook配置
message Webhook {
    string type = 1; // 类型
    map<string, string> settings = 2; // 设置
}

// 时间过滤器
message TimeFilter {
    string start = 1; // 开始时间 HH:MM
    string end = 2; // 结束时间 HH:MM
    repeated int32 repeat = 3; // 重复日期 (1-7)
    string cal_id = 4; // 日历ID
    bool is_off = 5; // 是否关闭
}

// 过滤器条件
message FilterCondition {
    string key = 1; // 字段名
    string oper = 2; // 操作符
    repeated string vals = 3; // 值列表
}

// 过滤器条件组 - 用于支持二维数组结构
message FilterConditionGroup {
    repeated FilterCondition conditions = 1; // 条件列表
}

message CreateAssignmentPolicyReq {
    int64 space_id = 1; // 空间ID
    string rule_name = 2; // 规则名称
    string template_id = 3; // 模板ID
    string description = 4; // 描述
    string status = 5; // 状态
    int32 priority = 6; // 优先级
    repeated NotificationLayer layers = 7; // 分层通知
    int32 aggr_window = 8; // 聚合窗口(秒)
    repeated TimeFilter time_filters = 9; // 时间过滤器
    repeated FilterConditionGroup filters = 10; // 过滤器组
    int64 updated_by = 11; // 创建者ID
}

message UpdateAssignmentPolicyReq {
    string rule_id = 1; // 规则ID
    int64 space_id = 2; // 空间ID
    string rule_name = 3; // 规则名称
    string template_id = 4; // 模板ID
    string description = 5; // 描述
    string status = 6; // 状态
    int32 priority = 7; // 优先级
    repeated NotificationLayer layers = 8; // 分层通知
    int32 aggr_window = 9; // 聚合窗口(秒)
    repeated TimeFilter time_filters = 10; // 时间过滤器
    repeated FilterConditionGroup filters = 11; // 过滤器组
    int64 updated_by = 12; // 更新者ID
}

message AssignmentPolicyListReq {
    uint64 page = 1;
    uint64 page_size = 2;
    optional int64 space_id = 3; // 空间ID
    optional string status = 4; // 状态
}

message AssignmentPolicyListResp {
    uint64 total = 1;
    repeated AssignmentPolicyInfo list = 2;
}

// UUID请求消息
message UUIDReq {
    string id = 1;
}

message UUIDsReq {
    repeated string ids = 1;
}

// 移除不使用的 DataSource 操作消息

service Integration {
    //  Integration management
    rpc createIntegration(CreateIntegrationReq) returns (BaseUUIDResp);
    rpc updateIntegration(UpdateIntegrationReq) returns (BaseResp);
    rpc getIntegrationList(IntegrationListReq) returns (IntegrationListResp);
    rpc getIntegrationById(UUIDReq) returns (IntegrationInfo);
    rpc deleteIntegration(UUIDsReq) returns (BaseResp);

    // 移除 Enrichment 和 AlertRule 管理方法，当前系统不使用

    // AlertRoute management
    rpc createAlertRoute(CreateAlertRouteReq) returns (BaseUUIDResp);
    rpc updateAlertRoute(UpdateAlertRouteReq) returns (BaseResp);
    rpc getAlertRouteList(AlertRouteListReq) returns (AlertRouteListResp);
    rpc getAlertRouteById(IDReq) returns (AlertRouteInfo);
    rpc deleteAlertRoute(IDsReq) returns (BaseResp);

    // 移除注释掉的无用服务定义

    // 关联管理
    rpc associateAlertWithSpace(AssociateAlertWithSpaceReq) returns (BaseResp);
    rpc associateIntegrationWithSpace(AssociateIntegrationWithSpaceReq) returns (BaseResp);

    // Assignment Policy management - 分配策略管理
    rpc createAssignmentPolicy(CreateAssignmentPolicyReq) returns (BaseUUIDResp);
    rpc updateAssignmentPolicy(UpdateAssignmentPolicyReq) returns (BaseResp);
    rpc getAssignmentPolicyList(AssignmentPolicyListReq) returns (AssignmentPolicyListResp);
    rpc getAssignmentPolicyById(UUIDReq) returns (AssignmentPolicyInfo);
    rpc deleteAssignmentPolicy(UUIDsReq) returns (BaseResp);
}

// Space management
service Space {
    rpc createSpace(CreateSpaceReq) returns (BaseUUIDResp);
    rpc updateSpace(UpdateSpaceReq) returns (BaseResp);
    rpc getSpaceList(SpaceListReq) returns (SpaceListResp);
    rpc getSpaceById(IDReq) returns (SpaceInfo);
    rpc deleteSpace(IDsReq) returns (BaseResp);
}

// 添加新的 RawAlert 相关消息和服务
message RawAlertInfo {
    string id = 1;
    string raw_id = 2;
    string raw_data = 3; // 原始数据
    int64 created_at = 4;
}

message RawAlertListReq {
    uint64 page = 1;
    uint64 page_size = 2;
    optional string raw_id = 3;
    optional string severity = 4;
    optional string status = 5;
    optional int64 start_time_begin = 6;
    optional int64 start_time_end = 7;
}

message RawAlertListResp {
    uint64 total = 1;
    repeated RawAlertInfo list = 2;
}


