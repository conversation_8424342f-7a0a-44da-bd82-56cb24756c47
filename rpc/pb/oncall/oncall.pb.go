// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.4
// 	protoc        v4.23.3
// source: desc/oncall.proto

package oncall

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// base message
type BaseIDResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BaseIDResp) Reset() {
	*x = BaseIDResp{}
	mi := &file_desc_oncall_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseIDResp) ProtoMessage() {}

func (x *BaseIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseIDResp.ProtoReflect.Descriptor instead.
func (*BaseIDResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{0}
}

func (x *BaseIDResp) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BaseIDResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type BaseMsg struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Msg           string                 `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BaseMsg) Reset() {
	*x = BaseMsg{}
	mi := &file_desc_oncall_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseMsg) ProtoMessage() {}

func (x *BaseMsg) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseMsg.ProtoReflect.Descriptor instead.
func (*BaseMsg) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{1}
}

func (x *BaseMsg) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type BaseResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Msg           string                 `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BaseResp) Reset() {
	*x = BaseResp{}
	mi := &file_desc_oncall_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseResp) ProtoMessage() {}

func (x *BaseResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseResp.ProtoReflect.Descriptor instead.
func (*BaseResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{2}
}

func (x *BaseResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type BaseUUIDResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BaseUUIDResp) Reset() {
	*x = BaseUUIDResp{}
	mi := &file_desc_oncall_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseUUIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseUUIDResp) ProtoMessage() {}

func (x *BaseUUIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseUUIDResp.ProtoReflect.Descriptor instead.
func (*BaseUUIDResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{3}
}

func (x *BaseUUIDResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BaseUUIDResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_desc_oncall_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{4}
}

type IDReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IDReq) Reset() {
	*x = IDReq{}
	mi := &file_desc_oncall_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IDReq) ProtoMessage() {}

func (x *IDReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IDReq.ProtoReflect.Descriptor instead.
func (*IDReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{5}
}

func (x *IDReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type IDsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []uint64               `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IDsReq) Reset() {
	*x = IDsReq{}
	mi := &file_desc_oncall_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IDsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IDsReq) ProtoMessage() {}

func (x *IDsReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IDsReq.ProtoReflect.Descriptor instead.
func (*IDsReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{6}
}

func (x *IDsReq) GetIds() []uint64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// Integration related messages
type IntegrationInfo struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Kind                 string                 `protobuf:"bytes,3,opt,name=kind,proto3" json:"kind,omitempty"` // private/public
	Type                 string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"` // prometheus/grafana/alertmanager等
	Status               bool                   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt            int64                  `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            int64                  `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	LastEventTime        string                 `protobuf:"bytes,8,opt,name=last_event_time,json=lastEventTime,proto3" json:"last_event_time,omitempty"`                       // 最后事件时间
	Webhook              string                 `protobuf:"bytes,9,opt,name=webhook,proto3" json:"webhook,omitempty"`                                                          // webhook URL
	TagRules             string                 `protobuf:"bytes,10,opt,name=tag_rules,json=tagRules,proto3" json:"tag_rules,omitempty"`                                       // 标签规则 JSON
	NoiseRules           string                 `protobuf:"bytes,11,opt,name=noise_rules,json=noiseRules,proto3" json:"noise_rules,omitempty"`                                 // 噪音规则 JSON
	AlertProcessingRules string                 `protobuf:"bytes,12,opt,name=alert_processing_rules,json=alertProcessingRules,proto3" json:"alert_processing_rules,omitempty"` // 告警处理规则 JSON
	Alerts               string                 `protobuf:"bytes,13,opt,name=alerts,proto3" json:"alerts,omitempty"`                                                           // 告警示例 JSON
	RoutingRules         string                 `protobuf:"bytes,14,opt,name=routing_rules,json=routingRules,proto3" json:"routing_rules,omitempty"`                           // 路由规则 JSON
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *IntegrationInfo) Reset() {
	*x = IntegrationInfo{}
	mi := &file_desc_oncall_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntegrationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntegrationInfo) ProtoMessage() {}

func (x *IntegrationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntegrationInfo.ProtoReflect.Descriptor instead.
func (*IntegrationInfo) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{7}
}

func (x *IntegrationInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IntegrationInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IntegrationInfo) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *IntegrationInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *IntegrationInfo) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *IntegrationInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *IntegrationInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *IntegrationInfo) GetLastEventTime() string {
	if x != nil {
		return x.LastEventTime
	}
	return ""
}

func (x *IntegrationInfo) GetWebhook() string {
	if x != nil {
		return x.Webhook
	}
	return ""
}

func (x *IntegrationInfo) GetTagRules() string {
	if x != nil {
		return x.TagRules
	}
	return ""
}

func (x *IntegrationInfo) GetNoiseRules() string {
	if x != nil {
		return x.NoiseRules
	}
	return ""
}

func (x *IntegrationInfo) GetAlertProcessingRules() string {
	if x != nil {
		return x.AlertProcessingRules
	}
	return ""
}

func (x *IntegrationInfo) GetAlerts() string {
	if x != nil {
		return x.Alerts
	}
	return ""
}

func (x *IntegrationInfo) GetRoutingRules() string {
	if x != nil {
		return x.RoutingRules
	}
	return ""
}

type CreateIntegrationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // Integration ID (可选)
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Kind          string                 `protobuf:"bytes,3,opt,name=kind,proto3" json:"kind,omitempty"`      // private/public，默认为private
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`      // prometheus/grafana/alertmanager等
	Status        bool                   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"` // 状态，默认为true
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIntegrationReq) Reset() {
	*x = CreateIntegrationReq{}
	mi := &file_desc_oncall_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIntegrationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIntegrationReq) ProtoMessage() {}

func (x *CreateIntegrationReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIntegrationReq.ProtoReflect.Descriptor instead.
func (*CreateIntegrationReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{8}
}

func (x *CreateIntegrationReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreateIntegrationReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateIntegrationReq) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *CreateIntegrationReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateIntegrationReq) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type UpdateIntegrationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Kind          string                 `protobuf:"bytes,3,opt,name=kind,proto3" json:"kind,omitempty"` // private/public
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"` // prometheus/grafana/alertmanager等
	Status        bool                   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateIntegrationReq) Reset() {
	*x = UpdateIntegrationReq{}
	mi := &file_desc_oncall_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateIntegrationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIntegrationReq) ProtoMessage() {}

func (x *UpdateIntegrationReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIntegrationReq.ProtoReflect.Descriptor instead.
func (*UpdateIntegrationReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateIntegrationReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateIntegrationReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateIntegrationReq) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *UpdateIntegrationReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UpdateIntegrationReq) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type IntegrationListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          uint64                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	Kind          *string                `protobuf:"bytes,4,opt,name=kind,proto3,oneof" json:"kind,omitempty"`
	Type          *string                `protobuf:"bytes,5,opt,name=type,proto3,oneof" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntegrationListReq) Reset() {
	*x = IntegrationListReq{}
	mi := &file_desc_oncall_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntegrationListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntegrationListReq) ProtoMessage() {}

func (x *IntegrationListReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntegrationListReq.ProtoReflect.Descriptor instead.
func (*IntegrationListReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{10}
}

func (x *IntegrationListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *IntegrationListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *IntegrationListReq) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *IntegrationListReq) GetKind() string {
	if x != nil && x.Kind != nil {
		return *x.Kind
	}
	return ""
}

func (x *IntegrationListReq) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

type IntegrationListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*IntegrationInfo     `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntegrationListResp) Reset() {
	*x = IntegrationListResp{}
	mi := &file_desc_oncall_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntegrationListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntegrationListResp) ProtoMessage() {}

func (x *IntegrationListResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntegrationListResp.ProtoReflect.Descriptor instead.
func (*IntegrationListResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{11}
}

func (x *IntegrationListResp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *IntegrationListResp) GetList() []*IntegrationInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// DataSource related messages
type DataSourceInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TypeName       string                 `protobuf:"bytes,2,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"`
	Type           string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	Url            string                 `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	Logo           string                 `protobuf:"bytes,5,opt,name=logo,proto3" json:"logo,omitempty"`
	Sort           int64                  `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	IntegrationIds []string               `protobuf:"bytes,7,rep,name=integration_ids,json=integrationIds,proto3" json:"integration_ids,omitempty"` // 关联的集成 ID 列表
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DataSourceInfo) Reset() {
	*x = DataSourceInfo{}
	mi := &file_desc_oncall_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataSourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataSourceInfo) ProtoMessage() {}

func (x *DataSourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataSourceInfo.ProtoReflect.Descriptor instead.
func (*DataSourceInfo) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{12}
}

func (x *DataSourceInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DataSourceInfo) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

func (x *DataSourceInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DataSourceInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *DataSourceInfo) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *DataSourceInfo) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *DataSourceInfo) GetIntegrationIds() []string {
	if x != nil {
		return x.IntegrationIds
	}
	return nil
}

type DataSourceListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          uint64                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TypeName      *string                `protobuf:"bytes,3,opt,name=type_name,json=typeName,proto3,oneof" json:"type_name,omitempty"`
	Type          *string                `protobuf:"bytes,4,opt,name=type,proto3,oneof" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataSourceListReq) Reset() {
	*x = DataSourceListReq{}
	mi := &file_desc_oncall_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataSourceListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataSourceListReq) ProtoMessage() {}

func (x *DataSourceListReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataSourceListReq.ProtoReflect.Descriptor instead.
func (*DataSourceListReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{13}
}

func (x *DataSourceListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *DataSourceListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *DataSourceListReq) GetTypeName() string {
	if x != nil && x.TypeName != nil {
		return *x.TypeName
	}
	return ""
}

func (x *DataSourceListReq) GetType() string {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return ""
}

type DataSourceListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*DataSourceInfo      `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DataSourceListResp) Reset() {
	*x = DataSourceListResp{}
	mi := &file_desc_oncall_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DataSourceListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataSourceListResp) ProtoMessage() {}

func (x *DataSourceListResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataSourceListResp.ProtoReflect.Descriptor instead.
func (*DataSourceListResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{14}
}

func (x *DataSourceListResp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DataSourceListResp) GetList() []*DataSourceInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// AlertRoute related messages - 路由规则
type AlertRouteInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	IntegrationId string                 `protobuf:"bytes,2,opt,name=integration_id,json=integrationId,proto3" json:"integration_id,omitempty"` // 集成ID
	Cases         []*RouteCase           `protobuf:"bytes,3,rep,name=cases,proto3" json:"cases,omitempty"`                                      // 条件规则数组
	Default       *RouteDefault          `protobuf:"bytes,4,opt,name=default,proto3" json:"default,omitempty"`                                  // 默认路由
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                                    // 状态
	Version       int32                  `protobuf:"varint,6,opt,name=version,proto3" json:"version,omitempty"`                                 // 版本
	UpdatedBy     int64                  `protobuf:"varint,7,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`            // 更新者ID
	CreatorId     int64                  `protobuf:"varint,8,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`            // 创建者ID
	CreatedAt     int64                  `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlertRouteInfo) Reset() {
	*x = AlertRouteInfo{}
	mi := &file_desc_oncall_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlertRouteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertRouteInfo) ProtoMessage() {}

func (x *AlertRouteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertRouteInfo.ProtoReflect.Descriptor instead.
func (*AlertRouteInfo) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{15}
}

func (x *AlertRouteInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AlertRouteInfo) GetIntegrationId() string {
	if x != nil {
		return x.IntegrationId
	}
	return ""
}

func (x *AlertRouteInfo) GetCases() []*RouteCase {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *AlertRouteInfo) GetDefault() *RouteDefault {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *AlertRouteInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AlertRouteInfo) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *AlertRouteInfo) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *AlertRouteInfo) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *AlertRouteInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AlertRouteInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 路由条件规则
type RouteCase struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	If            []*RouteCondition      `protobuf:"bytes,1,rep,name=if,proto3" json:"if,omitempty"`                                           // 条件数组
	ChannelIds    []int64                `protobuf:"varint,2,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"` // 通道ID数组
	Fallthrough   bool                   `protobuf:"varint,3,opt,name=fallthrough,proto3" json:"fallthrough,omitempty"`                        // 是否继续
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteCase) Reset() {
	*x = RouteCase{}
	mi := &file_desc_oncall_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteCase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteCase) ProtoMessage() {}

func (x *RouteCase) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteCase.ProtoReflect.Descriptor instead.
func (*RouteCase) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{16}
}

func (x *RouteCase) GetIf() []*RouteCondition {
	if x != nil {
		return x.If
	}
	return nil
}

func (x *RouteCase) GetChannelIds() []int64 {
	if x != nil {
		return x.ChannelIds
	}
	return nil
}

func (x *RouteCase) GetFallthrough() bool {
	if x != nil {
		return x.Fallthrough
	}
	return false
}

// 路由条件
type RouteCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`   // 字段名
	Oper          string                 `protobuf:"bytes,2,opt,name=oper,proto3" json:"oper,omitempty"` // 操作符 (IN, EQ, etc.)
	Vals          []string               `protobuf:"bytes,3,rep,name=vals,proto3" json:"vals,omitempty"` // 值数组
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteCondition) Reset() {
	*x = RouteCondition{}
	mi := &file_desc_oncall_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteCondition) ProtoMessage() {}

func (x *RouteCondition) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteCondition.ProtoReflect.Descriptor instead.
func (*RouteCondition) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{17}
}

func (x *RouteCondition) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *RouteCondition) GetOper() string {
	if x != nil {
		return x.Oper
	}
	return ""
}

func (x *RouteCondition) GetVals() []string {
	if x != nil {
		return x.Vals
	}
	return nil
}

// 默认路由
type RouteDefault struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChannelIds    []int64                `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"` // 默认通道ID数组
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteDefault) Reset() {
	*x = RouteDefault{}
	mi := &file_desc_oncall_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteDefault) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteDefault) ProtoMessage() {}

func (x *RouteDefault) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteDefault.ProtoReflect.Descriptor instead.
func (*RouteDefault) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{18}
}

func (x *RouteDefault) GetChannelIds() []int64 {
	if x != nil {
		return x.ChannelIds
	}
	return nil
}

type CreateAlertRouteReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IntegrationId string                 `protobuf:"bytes,1,opt,name=integration_id,json=integrationId,proto3" json:"integration_id,omitempty"` // 集成ID
	Cases         []*RouteCase           `protobuf:"bytes,2,rep,name=cases,proto3" json:"cases,omitempty"`                                      // 条件规则数组
	Default       *RouteDefault          `protobuf:"bytes,3,opt,name=default,proto3" json:"default,omitempty"`                                  // 默认路由
	Status        string                 `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`                                    // 状态
	CreatorId     int64                  `protobuf:"varint,5,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`            // 创建者ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAlertRouteReq) Reset() {
	*x = CreateAlertRouteReq{}
	mi := &file_desc_oncall_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAlertRouteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAlertRouteReq) ProtoMessage() {}

func (x *CreateAlertRouteReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAlertRouteReq.ProtoReflect.Descriptor instead.
func (*CreateAlertRouteReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{19}
}

func (x *CreateAlertRouteReq) GetIntegrationId() string {
	if x != nil {
		return x.IntegrationId
	}
	return ""
}

func (x *CreateAlertRouteReq) GetCases() []*RouteCase {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *CreateAlertRouteReq) GetDefault() *RouteDefault {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *CreateAlertRouteReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateAlertRouteReq) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

type UpdateAlertRouteReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	IntegrationId string                 `protobuf:"bytes,2,opt,name=integration_id,json=integrationId,proto3" json:"integration_id,omitempty"` // 集成ID
	Cases         []*RouteCase           `protobuf:"bytes,3,rep,name=cases,proto3" json:"cases,omitempty"`                                      // 条件规则数组
	Default       *RouteDefault          `protobuf:"bytes,4,opt,name=default,proto3" json:"default,omitempty"`                                  // 默认路由
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                                    // 状态
	UpdatedBy     int64                  `protobuf:"varint,6,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`            // 更新者ID
	Version       int32                  `protobuf:"varint,7,opt,name=version,proto3" json:"version,omitempty"`                                 // 版本
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAlertRouteReq) Reset() {
	*x = UpdateAlertRouteReq{}
	mi := &file_desc_oncall_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAlertRouteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAlertRouteReq) ProtoMessage() {}

func (x *UpdateAlertRouteReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAlertRouteReq.ProtoReflect.Descriptor instead.
func (*UpdateAlertRouteReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateAlertRouteReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateAlertRouteReq) GetIntegrationId() string {
	if x != nil {
		return x.IntegrationId
	}
	return ""
}

func (x *UpdateAlertRouteReq) GetCases() []*RouteCase {
	if x != nil {
		return x.Cases
	}
	return nil
}

func (x *UpdateAlertRouteReq) GetDefault() *RouteDefault {
	if x != nil {
		return x.Default
	}
	return nil
}

func (x *UpdateAlertRouteReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdateAlertRouteReq) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *UpdateAlertRouteReq) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type AlertRouteListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          uint64                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	IntegrationId *string                `protobuf:"bytes,3,opt,name=integration_id,json=integrationId,proto3,oneof" json:"integration_id,omitempty"` // 集成ID
	Status        *string                `protobuf:"bytes,4,opt,name=status,proto3,oneof" json:"status,omitempty"`                                    // 状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlertRouteListReq) Reset() {
	*x = AlertRouteListReq{}
	mi := &file_desc_oncall_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlertRouteListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertRouteListReq) ProtoMessage() {}

func (x *AlertRouteListReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertRouteListReq.ProtoReflect.Descriptor instead.
func (*AlertRouteListReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{21}
}

func (x *AlertRouteListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *AlertRouteListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AlertRouteListReq) GetIntegrationId() string {
	if x != nil && x.IntegrationId != nil {
		return *x.IntegrationId
	}
	return ""
}

func (x *AlertRouteListReq) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

type AlertRouteListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*AlertRouteInfo      `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlertRouteListResp) Reset() {
	*x = AlertRouteListResp{}
	mi := &file_desc_oncall_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlertRouteListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertRouteListResp) ProtoMessage() {}

func (x *AlertRouteListResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertRouteListResp.ProtoReflect.Descriptor instead.
func (*AlertRouteListResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{22}
}

func (x *AlertRouteListResp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AlertRouteListResp) GetList() []*AlertRouteInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// Alert related messages
type AlertInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	AlertId        string                 `protobuf:"bytes,2,opt,name=alert_id,json=alertId,proto3" json:"alert_id,omitempty"`
	DedupKey       string                 `protobuf:"bytes,3,opt,name=dedup_key,json=dedupKey,proto3" json:"dedup_key,omitempty"`
	Title          string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Description    string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	AlertSeverity  string                 `protobuf:"bytes,6,opt,name=alert_severity,json=alertSeverity,proto3" json:"alert_severity,omitempty"`
	AlertStatus    string                 `protobuf:"bytes,7,opt,name=alert_status,json=alertStatus,proto3" json:"alert_status,omitempty"`
	Progress       string                 `protobuf:"bytes,8,opt,name=progress,proto3" json:"progress,omitempty"`
	StartTime      int64                  `protobuf:"varint,9,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	LastTime       int64                  `protobuf:"varint,10,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	EndTime        int64                  `protobuf:"varint,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AckTime        int64                  `protobuf:"varint,12,opt,name=ack_time,json=ackTime,proto3" json:"ack_time,omitempty"`
	CloseTime      int64                  `protobuf:"varint,13,opt,name=close_time,json=closeTime,proto3" json:"close_time,omitempty"`
	Labels         map[string]string      `protobuf:"bytes,14,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Fields         []string               `protobuf:"bytes,15,rep,name=fields,proto3" json:"fields,omitempty"`
	EverMuted      bool                   `protobuf:"varint,16,opt,name=ever_muted,json=everMuted,proto3" json:"ever_muted,omitempty"`
	CreatedAt      int64                  `protobuf:"varint,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt      int64                  `protobuf:"varint,18,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DataSourceType string                 `protobuf:"bytes,19,opt,name=data_source_type,json=dataSourceType,proto3" json:"data_source_type,omitempty"`
	DataSourceName string                 `protobuf:"bytes,20,opt,name=data_source_name,json=dataSourceName,proto3" json:"data_source_name,omitempty"`
	ChannelName    string                 `protobuf:"bytes,21,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ChannelStatus  string                 `protobuf:"bytes,22,opt,name=channel_status,json=channelStatus,proto3" json:"channel_status,omitempty"`
	EventCnt       int32                  `protobuf:"varint,23,opt,name=event_cnt,json=eventCnt,proto3" json:"event_cnt,omitempty"`
	RawData        map[string]string      `protobuf:"bytes,24,rep,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	SpaceId        string                 `protobuf:"bytes,25,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	IntegrationId  string                 `protobuf:"bytes,26,opt,name=integration_id,json=integrationId,proto3" json:"integration_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AlertInfo) Reset() {
	*x = AlertInfo{}
	mi := &file_desc_oncall_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlertInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertInfo) ProtoMessage() {}

func (x *AlertInfo) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertInfo.ProtoReflect.Descriptor instead.
func (*AlertInfo) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{23}
}

func (x *AlertInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AlertInfo) GetAlertId() string {
	if x != nil {
		return x.AlertId
	}
	return ""
}

func (x *AlertInfo) GetDedupKey() string {
	if x != nil {
		return x.DedupKey
	}
	return ""
}

func (x *AlertInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AlertInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AlertInfo) GetAlertSeverity() string {
	if x != nil {
		return x.AlertSeverity
	}
	return ""
}

func (x *AlertInfo) GetAlertStatus() string {
	if x != nil {
		return x.AlertStatus
	}
	return ""
}

func (x *AlertInfo) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *AlertInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *AlertInfo) GetLastTime() int64 {
	if x != nil {
		return x.LastTime
	}
	return 0
}

func (x *AlertInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *AlertInfo) GetAckTime() int64 {
	if x != nil {
		return x.AckTime
	}
	return 0
}

func (x *AlertInfo) GetCloseTime() int64 {
	if x != nil {
		return x.CloseTime
	}
	return 0
}

func (x *AlertInfo) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *AlertInfo) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *AlertInfo) GetEverMuted() bool {
	if x != nil {
		return x.EverMuted
	}
	return false
}

func (x *AlertInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AlertInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *AlertInfo) GetDataSourceType() string {
	if x != nil {
		return x.DataSourceType
	}
	return ""
}

func (x *AlertInfo) GetDataSourceName() string {
	if x != nil {
		return x.DataSourceName
	}
	return ""
}

func (x *AlertInfo) GetChannelName() string {
	if x != nil {
		return x.ChannelName
	}
	return ""
}

func (x *AlertInfo) GetChannelStatus() string {
	if x != nil {
		return x.ChannelStatus
	}
	return ""
}

func (x *AlertInfo) GetEventCnt() int32 {
	if x != nil {
		return x.EventCnt
	}
	return 0
}

func (x *AlertInfo) GetRawData() map[string]string {
	if x != nil {
		return x.RawData
	}
	return nil
}

func (x *AlertInfo) GetSpaceId() string {
	if x != nil {
		return x.SpaceId
	}
	return ""
}

func (x *AlertInfo) GetIntegrationId() string {
	if x != nil {
		return x.IntegrationId
	}
	return ""
}

type AlertListReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Page           uint64                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	AlertId        *string                `protobuf:"bytes,3,opt,name=alert_id,json=alertId,proto3,oneof" json:"alert_id,omitempty"`
	DedupKey       *string                `protobuf:"bytes,4,opt,name=dedup_key,json=dedupKey,proto3,oneof" json:"dedup_key,omitempty"`
	AlertSeverity  *string                `protobuf:"bytes,5,opt,name=alert_severity,json=alertSeverity,proto3,oneof" json:"alert_severity,omitempty"`
	AlertStatus    *string                `protobuf:"bytes,6,opt,name=alert_status,json=alertStatus,proto3,oneof" json:"alert_status,omitempty"`
	StartTimeBegin *int64                 `protobuf:"varint,7,opt,name=start_time_begin,json=startTimeBegin,proto3,oneof" json:"start_time_begin,omitempty"`
	StartTimeEnd   *int64                 `protobuf:"varint,8,opt,name=start_time_end,json=startTimeEnd,proto3,oneof" json:"start_time_end,omitempty"`
	IntegrationId  *string                `protobuf:"bytes,9,opt,name=integration_id,json=integrationId,proto3,oneof" json:"integration_id,omitempty"`
	SpaceId        *string                `protobuf:"bytes,10,opt,name=space_id,json=spaceId,proto3,oneof" json:"space_id,omitempty"` // 添加空间 ID 查询参数
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AlertListReq) Reset() {
	*x = AlertListReq{}
	mi := &file_desc_oncall_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlertListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertListReq) ProtoMessage() {}

func (x *AlertListReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertListReq.ProtoReflect.Descriptor instead.
func (*AlertListReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{24}
}

func (x *AlertListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *AlertListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AlertListReq) GetAlertId() string {
	if x != nil && x.AlertId != nil {
		return *x.AlertId
	}
	return ""
}

func (x *AlertListReq) GetDedupKey() string {
	if x != nil && x.DedupKey != nil {
		return *x.DedupKey
	}
	return ""
}

func (x *AlertListReq) GetAlertSeverity() string {
	if x != nil && x.AlertSeverity != nil {
		return *x.AlertSeverity
	}
	return ""
}

func (x *AlertListReq) GetAlertStatus() string {
	if x != nil && x.AlertStatus != nil {
		return *x.AlertStatus
	}
	return ""
}

func (x *AlertListReq) GetStartTimeBegin() int64 {
	if x != nil && x.StartTimeBegin != nil {
		return *x.StartTimeBegin
	}
	return 0
}

func (x *AlertListReq) GetStartTimeEnd() int64 {
	if x != nil && x.StartTimeEnd != nil {
		return *x.StartTimeEnd
	}
	return 0
}

func (x *AlertListReq) GetIntegrationId() string {
	if x != nil && x.IntegrationId != nil {
		return *x.IntegrationId
	}
	return ""
}

func (x *AlertListReq) GetSpaceId() string {
	if x != nil && x.SpaceId != nil {
		return *x.SpaceId
	}
	return ""
}

type AlertListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*AlertInfo           `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AlertListResp) Reset() {
	*x = AlertListResp{}
	mi := &file_desc_oncall_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlertListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertListResp) ProtoMessage() {}

func (x *AlertListResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertListResp.ProtoReflect.Descriptor instead.
func (*AlertListResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{25}
}

func (x *AlertListResp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AlertListResp) GetList() []*AlertInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// Incident related messages
type IncidentInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DedupKey         string                 `protobuf:"bytes,2,opt,name=dedup_key,json=dedupKey,proto3" json:"dedup_key,omitempty"`
	SpaceId          string                 `protobuf:"bytes,3,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	DataSourceId     string                 `protobuf:"bytes,4,opt,name=data_source_id,json=dataSourceId,proto3" json:"data_source_id,omitempty"`
	Title            string                 `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Description      string                 `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	IncidentSeverity string                 `protobuf:"bytes,7,opt,name=incident_severity,json=incidentSeverity,proto3" json:"incident_severity,omitempty"`
	IncidentStatus   string                 `protobuf:"bytes,8,opt,name=incident_status,json=incidentStatus,proto3" json:"incident_status,omitempty"`
	Progress         string                 `protobuf:"bytes,9,opt,name=progress,proto3" json:"progress,omitempty"`
	StartTime        int64                  `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	LastTime         int64                  `protobuf:"varint,11,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	EndTime          int64                  `protobuf:"varint,12,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	AckTime          int64                  `protobuf:"varint,13,opt,name=ack_time,json=ackTime,proto3" json:"ack_time,omitempty"`
	CloseTime        int64                  `protobuf:"varint,14,opt,name=close_time,json=closeTime,proto3" json:"close_time,omitempty"`
	Labels           map[string]string      `protobuf:"bytes,15,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Fields           []string               `protobuf:"bytes,16,rep,name=fields,proto3" json:"fields,omitempty"`
	Impact           string                 `protobuf:"bytes,17,opt,name=impact,proto3" json:"impact,omitempty"`
	RootCause        string                 `protobuf:"bytes,18,opt,name=root_cause,json=rootCause,proto3" json:"root_cause,omitempty"`
	Resolution       string                 `protobuf:"bytes,19,opt,name=resolution,proto3" json:"resolution,omitempty"`
	Frequency        string                 `protobuf:"bytes,20,opt,name=frequency,proto3" json:"frequency,omitempty"`
	EverMuted        bool                   `protobuf:"varint,21,opt,name=ever_muted,json=everMuted,proto3" json:"ever_muted,omitempty"`
	CreatedAt        int64                  `protobuf:"varint,22,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt        int64                  `protobuf:"varint,23,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	SnoozedBefore    int64                  `protobuf:"varint,24,opt,name=snoozed_before,json=snoozedBefore,proto3" json:"snoozed_before,omitempty"`
	GroupMethod      string                 `protobuf:"bytes,25,opt,name=group_method,json=groupMethod,proto3" json:"group_method,omitempty"`
	DetailUrl        string                 `protobuf:"bytes,26,opt,name=detail_url,json=detailUrl,proto3" json:"detail_url,omitempty"`
	AlertCnt         int32                  `protobuf:"varint,27,opt,name=alert_cnt,json=alertCnt,proto3" json:"alert_cnt,omitempty"`
	AlertCnts        map[string]int32       `protobuf:"bytes,28,rep,name=alert_cnts,json=alertCnts,proto3" json:"alert_cnts,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	DataSourceName   string                 `protobuf:"bytes,29,opt,name=data_source_name,json=dataSourceName,proto3" json:"data_source_name,omitempty"`
	DataSourceType   string                 `protobuf:"bytes,30,opt,name=data_source_type,json=dataSourceType,proto3" json:"data_source_type,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *IncidentInfo) Reset() {
	*x = IncidentInfo{}
	mi := &file_desc_oncall_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncidentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncidentInfo) ProtoMessage() {}

func (x *IncidentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncidentInfo.ProtoReflect.Descriptor instead.
func (*IncidentInfo) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{26}
}

func (x *IncidentInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IncidentInfo) GetDedupKey() string {
	if x != nil {
		return x.DedupKey
	}
	return ""
}

func (x *IncidentInfo) GetSpaceId() string {
	if x != nil {
		return x.SpaceId
	}
	return ""
}

func (x *IncidentInfo) GetDataSourceId() string {
	if x != nil {
		return x.DataSourceId
	}
	return ""
}

func (x *IncidentInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *IncidentInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *IncidentInfo) GetIncidentSeverity() string {
	if x != nil {
		return x.IncidentSeverity
	}
	return ""
}

func (x *IncidentInfo) GetIncidentStatus() string {
	if x != nil {
		return x.IncidentStatus
	}
	return ""
}

func (x *IncidentInfo) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *IncidentInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *IncidentInfo) GetLastTime() int64 {
	if x != nil {
		return x.LastTime
	}
	return 0
}

func (x *IncidentInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *IncidentInfo) GetAckTime() int64 {
	if x != nil {
		return x.AckTime
	}
	return 0
}

func (x *IncidentInfo) GetCloseTime() int64 {
	if x != nil {
		return x.CloseTime
	}
	return 0
}

func (x *IncidentInfo) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *IncidentInfo) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *IncidentInfo) GetImpact() string {
	if x != nil {
		return x.Impact
	}
	return ""
}

func (x *IncidentInfo) GetRootCause() string {
	if x != nil {
		return x.RootCause
	}
	return ""
}

func (x *IncidentInfo) GetResolution() string {
	if x != nil {
		return x.Resolution
	}
	return ""
}

func (x *IncidentInfo) GetFrequency() string {
	if x != nil {
		return x.Frequency
	}
	return ""
}

func (x *IncidentInfo) GetEverMuted() bool {
	if x != nil {
		return x.EverMuted
	}
	return false
}

func (x *IncidentInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *IncidentInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *IncidentInfo) GetSnoozedBefore() int64 {
	if x != nil {
		return x.SnoozedBefore
	}
	return 0
}

func (x *IncidentInfo) GetGroupMethod() string {
	if x != nil {
		return x.GroupMethod
	}
	return ""
}

func (x *IncidentInfo) GetDetailUrl() string {
	if x != nil {
		return x.DetailUrl
	}
	return ""
}

func (x *IncidentInfo) GetAlertCnt() int32 {
	if x != nil {
		return x.AlertCnt
	}
	return 0
}

func (x *IncidentInfo) GetAlertCnts() map[string]int32 {
	if x != nil {
		return x.AlertCnts
	}
	return nil
}

func (x *IncidentInfo) GetDataSourceName() string {
	if x != nil {
		return x.DataSourceName
	}
	return ""
}

func (x *IncidentInfo) GetDataSourceType() string {
	if x != nil {
		return x.DataSourceType
	}
	return ""
}

type IncidentListReq struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Page             uint64                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize         uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	DedupKey         *string                `protobuf:"bytes,3,opt,name=dedup_key,json=dedupKey,proto3,oneof" json:"dedup_key,omitempty"`
	IncidentSeverity *string                `protobuf:"bytes,4,opt,name=incident_severity,json=incidentSeverity,proto3,oneof" json:"incident_severity,omitempty"`
	IncidentStatus   *string                `protobuf:"bytes,5,opt,name=incident_status,json=incidentStatus,proto3,oneof" json:"incident_status,omitempty"`
	Frequency        *string                `protobuf:"bytes,6,opt,name=frequency,proto3,oneof" json:"frequency,omitempty"`
	StartTimeBegin   *int64                 `protobuf:"varint,7,opt,name=start_time_begin,json=startTimeBegin,proto3,oneof" json:"start_time_begin,omitempty"`
	StartTimeEnd     *int64                 `protobuf:"varint,8,opt,name=start_time_end,json=startTimeEnd,proto3,oneof" json:"start_time_end,omitempty"`
	SpaceId          *string                `protobuf:"bytes,9,opt,name=space_id,json=spaceId,proto3,oneof" json:"space_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *IncidentListReq) Reset() {
	*x = IncidentListReq{}
	mi := &file_desc_oncall_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncidentListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncidentListReq) ProtoMessage() {}

func (x *IncidentListReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncidentListReq.ProtoReflect.Descriptor instead.
func (*IncidentListReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{27}
}

func (x *IncidentListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *IncidentListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *IncidentListReq) GetDedupKey() string {
	if x != nil && x.DedupKey != nil {
		return *x.DedupKey
	}
	return ""
}

func (x *IncidentListReq) GetIncidentSeverity() string {
	if x != nil && x.IncidentSeverity != nil {
		return *x.IncidentSeverity
	}
	return ""
}

func (x *IncidentListReq) GetIncidentStatus() string {
	if x != nil && x.IncidentStatus != nil {
		return *x.IncidentStatus
	}
	return ""
}

func (x *IncidentListReq) GetFrequency() string {
	if x != nil && x.Frequency != nil {
		return *x.Frequency
	}
	return ""
}

func (x *IncidentListReq) GetStartTimeBegin() int64 {
	if x != nil && x.StartTimeBegin != nil {
		return *x.StartTimeBegin
	}
	return 0
}

func (x *IncidentListReq) GetStartTimeEnd() int64 {
	if x != nil && x.StartTimeEnd != nil {
		return *x.StartTimeEnd
	}
	return 0
}

func (x *IncidentListReq) GetSpaceId() string {
	if x != nil && x.SpaceId != nil {
		return *x.SpaceId
	}
	return ""
}

type IncidentListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*IncidentInfo        `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IncidentListResp) Reset() {
	*x = IncidentListResp{}
	mi := &file_desc_oncall_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncidentListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncidentListResp) ProtoMessage() {}

func (x *IncidentListResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncidentListResp.ProtoReflect.Descriptor instead.
func (*IncidentListResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{28}
}

func (x *IncidentListResp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *IncidentListResp) GetList() []*IncidentInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// Space related messages - 工作空间
type SpaceInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TeamId          int64                  `protobuf:"varint,3,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`      // 团队ID
	TeamName        string                 `protobuf:"bytes,4,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"` // 团队名称
	Description     string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Visibility      string                 `protobuf:"bytes,6,opt,name=visibility,proto3" json:"visibility,omitempty"` // 可见性 private/public
	CreatedAt       int64                  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       int64                  `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	IssueStats      *IssueStats            `protobuf:"bytes,9,opt,name=issue_stats,json=issueStats,proto3" json:"issue_stats,omitempty"`                   // 问题统计
	Enabled         bool                   `protobuf:"varint,10,opt,name=enabled,proto3" json:"enabled,omitempty"`                                         // 启用状态
	StatusChangedAt string                 `protobuf:"bytes,11,opt,name=status_changed_at,json=statusChangedAt,proto3" json:"status_changed_at,omitempty"` // 状态变更时间
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SpaceInfo) Reset() {
	*x = SpaceInfo{}
	mi := &file_desc_oncall_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpaceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpaceInfo) ProtoMessage() {}

func (x *SpaceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpaceInfo.ProtoReflect.Descriptor instead.
func (*SpaceInfo) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{29}
}

func (x *SpaceInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SpaceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SpaceInfo) GetTeamId() int64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *SpaceInfo) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *SpaceInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SpaceInfo) GetVisibility() string {
	if x != nil {
		return x.Visibility
	}
	return ""
}

func (x *SpaceInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SpaceInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *SpaceInfo) GetIssueStats() *IssueStats {
	if x != nil {
		return x.IssueStats
	}
	return nil
}

func (x *SpaceInfo) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *SpaceInfo) GetStatusChangedAt() string {
	if x != nil {
		return x.StatusChangedAt
	}
	return ""
}

// 问题统计
type IssueStats struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Processing    int32                  `protobuf:"varint,1,opt,name=processing,proto3" json:"processing,omitempty"` // 处理中
	Pending       int32                  `protobuf:"varint,2,opt,name=pending,proto3" json:"pending,omitempty"`       // 待处理
	Completed     int32                  `protobuf:"varint,3,opt,name=completed,proto3" json:"completed,omitempty"`   // 已处理
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IssueStats) Reset() {
	*x = IssueStats{}
	mi := &file_desc_oncall_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IssueStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IssueStats) ProtoMessage() {}

func (x *IssueStats) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IssueStats.ProtoReflect.Descriptor instead.
func (*IssueStats) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{30}
}

func (x *IssueStats) GetProcessing() int32 {
	if x != nil {
		return x.Processing
	}
	return 0
}

func (x *IssueStats) GetPending() int32 {
	if x != nil {
		return x.Pending
	}
	return 0
}

func (x *IssueStats) GetCompleted() int32 {
	if x != nil {
		return x.Completed
	}
	return 0
}

type CreateSpaceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	TeamId        int64                  `protobuf:"varint,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`      // 团队ID
	TeamName      string                 `protobuf:"bytes,3,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"` // 团队名称
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Visibility    string                 `protobuf:"bytes,5,opt,name=visibility,proto3" json:"visibility,omitempty"` // 可见性 private/public
	Enabled       bool                   `protobuf:"varint,6,opt,name=enabled,proto3" json:"enabled,omitempty"`      // 启用状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSpaceReq) Reset() {
	*x = CreateSpaceReq{}
	mi := &file_desc_oncall_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSpaceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSpaceReq) ProtoMessage() {}

func (x *CreateSpaceReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSpaceReq.ProtoReflect.Descriptor instead.
func (*CreateSpaceReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{31}
}

func (x *CreateSpaceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateSpaceReq) GetTeamId() int64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *CreateSpaceReq) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *CreateSpaceReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateSpaceReq) GetVisibility() string {
	if x != nil {
		return x.Visibility
	}
	return ""
}

func (x *CreateSpaceReq) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type UpdateSpaceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	TeamId        int64                  `protobuf:"varint,3,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`      // 团队ID
	TeamName      string                 `protobuf:"bytes,4,opt,name=team_name,json=teamName,proto3" json:"team_name,omitempty"` // 团队名称
	Description   string                 `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Visibility    string                 `protobuf:"bytes,6,opt,name=visibility,proto3" json:"visibility,omitempty"` // 可见性 private/public
	Enabled       bool                   `protobuf:"varint,7,opt,name=enabled,proto3" json:"enabled,omitempty"`      // 启用状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSpaceReq) Reset() {
	*x = UpdateSpaceReq{}
	mi := &file_desc_oncall_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSpaceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSpaceReq) ProtoMessage() {}

func (x *UpdateSpaceReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSpaceReq.ProtoReflect.Descriptor instead.
func (*UpdateSpaceReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{32}
}

func (x *UpdateSpaceReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateSpaceReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateSpaceReq) GetTeamId() int64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *UpdateSpaceReq) GetTeamName() string {
	if x != nil {
		return x.TeamName
	}
	return ""
}

func (x *UpdateSpaceReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateSpaceReq) GetVisibility() string {
	if x != nil {
		return x.Visibility
	}
	return ""
}

func (x *UpdateSpaceReq) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SpaceListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          uint64                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Name          *string                `protobuf:"bytes,3,opt,name=name,proto3,oneof" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpaceListReq) Reset() {
	*x = SpaceListReq{}
	mi := &file_desc_oncall_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpaceListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpaceListReq) ProtoMessage() {}

func (x *SpaceListReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpaceListReq.ProtoReflect.Descriptor instead.
func (*SpaceListReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{33}
}

func (x *SpaceListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SpaceListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SpaceListReq) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

type SpaceListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*SpaceInfo           `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpaceListResp) Reset() {
	*x = SpaceListResp{}
	mi := &file_desc_oncall_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpaceListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpaceListResp) ProtoMessage() {}

func (x *SpaceListResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpaceListResp.ProtoReflect.Descriptor instead.
func (*SpaceListResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{34}
}

func (x *SpaceListResp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SpaceListResp) GetList() []*SpaceInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 添加将告警与空间关联的服务
type AssociateAlertWithSpaceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AlertId       string                 `protobuf:"bytes,1,opt,name=alert_id,json=alertId,proto3" json:"alert_id,omitempty"`
	SpaceId       string                 `protobuf:"bytes,2,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssociateAlertWithSpaceReq) Reset() {
	*x = AssociateAlertWithSpaceReq{}
	mi := &file_desc_oncall_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssociateAlertWithSpaceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssociateAlertWithSpaceReq) ProtoMessage() {}

func (x *AssociateAlertWithSpaceReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssociateAlertWithSpaceReq.ProtoReflect.Descriptor instead.
func (*AssociateAlertWithSpaceReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{35}
}

func (x *AssociateAlertWithSpaceReq) GetAlertId() string {
	if x != nil {
		return x.AlertId
	}
	return ""
}

func (x *AssociateAlertWithSpaceReq) GetSpaceId() string {
	if x != nil {
		return x.SpaceId
	}
	return ""
}

// 添加将集成与空间关联的服务
type AssociateIntegrationWithSpaceReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IntegrationId string                 `protobuf:"bytes,1,opt,name=integration_id,json=integrationId,proto3" json:"integration_id,omitempty"`
	SpaceId       string                 `protobuf:"bytes,2,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssociateIntegrationWithSpaceReq) Reset() {
	*x = AssociateIntegrationWithSpaceReq{}
	mi := &file_desc_oncall_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssociateIntegrationWithSpaceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssociateIntegrationWithSpaceReq) ProtoMessage() {}

func (x *AssociateIntegrationWithSpaceReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssociateIntegrationWithSpaceReq.ProtoReflect.Descriptor instead.
func (*AssociateIntegrationWithSpaceReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{36}
}

func (x *AssociateIntegrationWithSpaceReq) GetIntegrationId() string {
	if x != nil {
		return x.IntegrationId
	}
	return ""
}

func (x *AssociateIntegrationWithSpaceReq) GetSpaceId() string {
	if x != nil {
		return x.SpaceId
	}
	return ""
}

// Assignment Policy related messages - 分配策略
type AssignmentPolicyInfo struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Id            string                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                       // UUID
	SpaceId       int64                   `protobuf:"varint,2,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`             // 空间ID
	RuleId        string                  `protobuf:"bytes,3,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`                 // 规则ID
	RuleName      string                  `protobuf:"bytes,4,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`           // 规则名称
	TemplateId    string                  `protobuf:"bytes,5,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`     // 模板ID
	Description   string                  `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                     // 描述
	Status        string                  `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`                               // 状态
	Priority      int32                   `protobuf:"varint,8,opt,name=priority,proto3" json:"priority,omitempty"`                          // 优先级
	CreatedAt     int64                   `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`       // 创建时间
	UpdatedAt     int64                   `protobuf:"varint,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`      // 更新时间
	UpdatedBy     int64                   `protobuf:"varint,11,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`      // 更新者ID
	Layers        []*NotificationLayer    `protobuf:"bytes,12,rep,name=layers,proto3" json:"layers,omitempty"`                              // 分层通知
	AggrWindow    int32                   `protobuf:"varint,13,opt,name=aggr_window,json=aggrWindow,proto3" json:"aggr_window,omitempty"`   // 聚合窗口(秒)
	TimeFilters   []*TimeFilter           `protobuf:"bytes,14,rep,name=time_filters,json=timeFilters,proto3" json:"time_filters,omitempty"` // 时间过滤器
	Filters       []*FilterConditionGroup `protobuf:"bytes,15,rep,name=filters,proto3" json:"filters,omitempty"`                            // 过滤器组 - 二维数组
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentPolicyInfo) Reset() {
	*x = AssignmentPolicyInfo{}
	mi := &file_desc_oncall_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentPolicyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentPolicyInfo) ProtoMessage() {}

func (x *AssignmentPolicyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentPolicyInfo.ProtoReflect.Descriptor instead.
func (*AssignmentPolicyInfo) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{37}
}

func (x *AssignmentPolicyInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AssignmentPolicyInfo) GetSpaceId() int64 {
	if x != nil {
		return x.SpaceId
	}
	return 0
}

func (x *AssignmentPolicyInfo) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *AssignmentPolicyInfo) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *AssignmentPolicyInfo) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *AssignmentPolicyInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AssignmentPolicyInfo) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *AssignmentPolicyInfo) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *AssignmentPolicyInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AssignmentPolicyInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *AssignmentPolicyInfo) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *AssignmentPolicyInfo) GetLayers() []*NotificationLayer {
	if x != nil {
		return x.Layers
	}
	return nil
}

func (x *AssignmentPolicyInfo) GetAggrWindow() int32 {
	if x != nil {
		return x.AggrWindow
	}
	return 0
}

func (x *AssignmentPolicyInfo) GetTimeFilters() []*TimeFilter {
	if x != nil {
		return x.TimeFilters
	}
	return nil
}

func (x *AssignmentPolicyInfo) GetFilters() []*FilterConditionGroup {
	if x != nil {
		return x.Filters
	}
	return nil
}

// 通知层级
type NotificationLayer struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MaxTimes       int32                  `protobuf:"varint,1,opt,name=max_times,json=maxTimes,proto3" json:"max_times,omitempty"`                   // 最大通知次数
	NotifyStep     int32                  `protobuf:"varint,2,opt,name=notify_step,json=notifyStep,proto3" json:"notify_step,omitempty"`             // 通知步骤
	EscalateWindow int32                  `protobuf:"varint,3,opt,name=escalate_window,json=escalateWindow,proto3" json:"escalate_window,omitempty"` // 升级窗口(分钟)
	ForceEscalate  bool                   `protobuf:"varint,4,opt,name=force_escalate,json=forceEscalate,proto3" json:"force_escalate,omitempty"`    // 强制升级
	Target         *NotificationTarget    `protobuf:"bytes,5,opt,name=target,proto3" json:"target,omitempty"`                                        // 通知目标
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *NotificationLayer) Reset() {
	*x = NotificationLayer{}
	mi := &file_desc_oncall_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationLayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationLayer) ProtoMessage() {}

func (x *NotificationLayer) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationLayer.ProtoReflect.Descriptor instead.
func (*NotificationLayer) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{38}
}

func (x *NotificationLayer) GetMaxTimes() int32 {
	if x != nil {
		return x.MaxTimes
	}
	return 0
}

func (x *NotificationLayer) GetNotifyStep() int32 {
	if x != nil {
		return x.NotifyStep
	}
	return 0
}

func (x *NotificationLayer) GetEscalateWindow() int32 {
	if x != nil {
		return x.EscalateWindow
	}
	return 0
}

func (x *NotificationLayer) GetForceEscalate() bool {
	if x != nil {
		return x.ForceEscalate
	}
	return false
}

func (x *NotificationLayer) GetTarget() *NotificationTarget {
	if x != nil {
		return x.Target
	}
	return nil
}

// 通知目标
type NotificationTarget struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	TeamIds           []int64                   `protobuf:"varint,1,rep,packed,name=team_ids,json=teamIds,proto3" json:"team_ids,omitempty"`                                                                                                     // 团队ID列表
	PersonIds         []int64                   `protobuf:"varint,2,rep,packed,name=person_ids,json=personIds,proto3" json:"person_ids,omitempty"`                                                                                               // 个人ID列表
	ScheduleToRoleIds map[string]*ScheduleRoles `protobuf:"bytes,3,rep,name=schedule_to_role_ids,json=scheduleToRoleIds,proto3" json:"schedule_to_role_ids,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 排班角色映射
	By                *NotificationBy           `protobuf:"bytes,4,opt,name=by,proto3" json:"by,omitempty"`                                                                                                                                      // 通知方式
	Webhooks          []*Webhook                `protobuf:"bytes,5,rep,name=webhooks,proto3" json:"webhooks,omitempty"`                                                                                                                          // Webhook列表
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *NotificationTarget) Reset() {
	*x = NotificationTarget{}
	mi := &file_desc_oncall_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationTarget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationTarget) ProtoMessage() {}

func (x *NotificationTarget) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationTarget.ProtoReflect.Descriptor instead.
func (*NotificationTarget) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{39}
}

func (x *NotificationTarget) GetTeamIds() []int64 {
	if x != nil {
		return x.TeamIds
	}
	return nil
}

func (x *NotificationTarget) GetPersonIds() []int64 {
	if x != nil {
		return x.PersonIds
	}
	return nil
}

func (x *NotificationTarget) GetScheduleToRoleIds() map[string]*ScheduleRoles {
	if x != nil {
		return x.ScheduleToRoleIds
	}
	return nil
}

func (x *NotificationTarget) GetBy() *NotificationBy {
	if x != nil {
		return x.By
	}
	return nil
}

func (x *NotificationTarget) GetWebhooks() []*Webhook {
	if x != nil {
		return x.Webhooks
	}
	return nil
}

// 排班角色
type ScheduleRoles struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RoleIds       []int64                `protobuf:"varint,1,rep,packed,name=role_ids,json=roleIds,proto3" json:"role_ids,omitempty"` // 角色ID列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScheduleRoles) Reset() {
	*x = ScheduleRoles{}
	mi := &file_desc_oncall_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScheduleRoles) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScheduleRoles) ProtoMessage() {}

func (x *ScheduleRoles) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScheduleRoles.ProtoReflect.Descriptor instead.
func (*ScheduleRoles) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{40}
}

func (x *ScheduleRoles) GetRoleIds() []int64 {
	if x != nil {
		return x.RoleIds
	}
	return nil
}

// 通知方式
type NotificationBy struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	FollowPreference bool                   `protobuf:"varint,1,opt,name=follow_preference,json=followPreference,proto3" json:"follow_preference,omitempty"` // 遵循偏好设置
	Critical         []string               `protobuf:"bytes,2,rep,name=critical,proto3" json:"critical,omitempty"`                                          // 严重级别通知方式
	Warning          []string               `protobuf:"bytes,3,rep,name=warning,proto3" json:"warning,omitempty"`                                            // 警告级别通知方式
	Info             []string               `protobuf:"bytes,4,rep,name=info,proto3" json:"info,omitempty"`                                                  // 信息级别通知方式
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *NotificationBy) Reset() {
	*x = NotificationBy{}
	mi := &file_desc_oncall_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NotificationBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NotificationBy) ProtoMessage() {}

func (x *NotificationBy) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NotificationBy.ProtoReflect.Descriptor instead.
func (*NotificationBy) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{41}
}

func (x *NotificationBy) GetFollowPreference() bool {
	if x != nil {
		return x.FollowPreference
	}
	return false
}

func (x *NotificationBy) GetCritical() []string {
	if x != nil {
		return x.Critical
	}
	return nil
}

func (x *NotificationBy) GetWarning() []string {
	if x != nil {
		return x.Warning
	}
	return nil
}

func (x *NotificationBy) GetInfo() []string {
	if x != nil {
		return x.Info
	}
	return nil
}

// Webhook配置
type Webhook struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          string                 `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`                                                                                   // 类型
	Settings      map[string]string      `protobuf:"bytes,2,rep,name=settings,proto3" json:"settings,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 设置
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Webhook) Reset() {
	*x = Webhook{}
	mi := &file_desc_oncall_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Webhook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Webhook) ProtoMessage() {}

func (x *Webhook) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Webhook.ProtoReflect.Descriptor instead.
func (*Webhook) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{42}
}

func (x *Webhook) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Webhook) GetSettings() map[string]string {
	if x != nil {
		return x.Settings
	}
	return nil
}

// 时间过滤器
type TimeFilter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Start         string                 `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`               // 开始时间 HH:MM
	End           string                 `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`                   // 结束时间 HH:MM
	Repeat        []int32                `protobuf:"varint,3,rep,packed,name=repeat,proto3" json:"repeat,omitempty"`     // 重复日期 (1-7)
	CalId         string                 `protobuf:"bytes,4,opt,name=cal_id,json=calId,proto3" json:"cal_id,omitempty"`  // 日历ID
	IsOff         bool                   `protobuf:"varint,5,opt,name=is_off,json=isOff,proto3" json:"is_off,omitempty"` // 是否关闭
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimeFilter) Reset() {
	*x = TimeFilter{}
	mi := &file_desc_oncall_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeFilter) ProtoMessage() {}

func (x *TimeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeFilter.ProtoReflect.Descriptor instead.
func (*TimeFilter) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{43}
}

func (x *TimeFilter) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *TimeFilter) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *TimeFilter) GetRepeat() []int32 {
	if x != nil {
		return x.Repeat
	}
	return nil
}

func (x *TimeFilter) GetCalId() string {
	if x != nil {
		return x.CalId
	}
	return ""
}

func (x *TimeFilter) GetIsOff() bool {
	if x != nil {
		return x.IsOff
	}
	return false
}

// 过滤器条件
type FilterCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`   // 字段名
	Oper          string                 `protobuf:"bytes,2,opt,name=oper,proto3" json:"oper,omitempty"` // 操作符
	Vals          []string               `protobuf:"bytes,3,rep,name=vals,proto3" json:"vals,omitempty"` // 值列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCondition) Reset() {
	*x = FilterCondition{}
	mi := &file_desc_oncall_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCondition) ProtoMessage() {}

func (x *FilterCondition) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCondition.ProtoReflect.Descriptor instead.
func (*FilterCondition) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{44}
}

func (x *FilterCondition) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *FilterCondition) GetOper() string {
	if x != nil {
		return x.Oper
	}
	return ""
}

func (x *FilterCondition) GetVals() []string {
	if x != nil {
		return x.Vals
	}
	return nil
}

// 过滤器条件组 - 用于支持二维数组结构
type FilterConditionGroup struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Conditions    []*FilterCondition     `protobuf:"bytes,1,rep,name=conditions,proto3" json:"conditions,omitempty"` // 条件列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterConditionGroup) Reset() {
	*x = FilterConditionGroup{}
	mi := &file_desc_oncall_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterConditionGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterConditionGroup) ProtoMessage() {}

func (x *FilterConditionGroup) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterConditionGroup.ProtoReflect.Descriptor instead.
func (*FilterConditionGroup) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{45}
}

func (x *FilterConditionGroup) GetConditions() []*FilterCondition {
	if x != nil {
		return x.Conditions
	}
	return nil
}

type CreateAssignmentPolicyReq struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	SpaceId       int64                   `protobuf:"varint,1,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`            // 空间ID
	RuleName      string                  `protobuf:"bytes,2,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`          // 规则名称
	TemplateId    string                  `protobuf:"bytes,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`    // 模板ID
	Description   string                  `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`                    // 描述
	Status        string                  `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                              // 状态
	Priority      int32                   `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`                         // 优先级
	Layers        []*NotificationLayer    `protobuf:"bytes,7,rep,name=layers,proto3" json:"layers,omitempty"`                              // 分层通知
	AggrWindow    int32                   `protobuf:"varint,8,opt,name=aggr_window,json=aggrWindow,proto3" json:"aggr_window,omitempty"`   // 聚合窗口(秒)
	TimeFilters   []*TimeFilter           `protobuf:"bytes,9,rep,name=time_filters,json=timeFilters,proto3" json:"time_filters,omitempty"` // 时间过滤器
	Filters       []*FilterConditionGroup `protobuf:"bytes,10,rep,name=filters,proto3" json:"filters,omitempty"`                           // 过滤器组
	UpdatedBy     int64                   `protobuf:"varint,11,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`     // 创建者ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAssignmentPolicyReq) Reset() {
	*x = CreateAssignmentPolicyReq{}
	mi := &file_desc_oncall_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAssignmentPolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAssignmentPolicyReq) ProtoMessage() {}

func (x *CreateAssignmentPolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAssignmentPolicyReq.ProtoReflect.Descriptor instead.
func (*CreateAssignmentPolicyReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{46}
}

func (x *CreateAssignmentPolicyReq) GetSpaceId() int64 {
	if x != nil {
		return x.SpaceId
	}
	return 0
}

func (x *CreateAssignmentPolicyReq) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *CreateAssignmentPolicyReq) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *CreateAssignmentPolicyReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateAssignmentPolicyReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *CreateAssignmentPolicyReq) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *CreateAssignmentPolicyReq) GetLayers() []*NotificationLayer {
	if x != nil {
		return x.Layers
	}
	return nil
}

func (x *CreateAssignmentPolicyReq) GetAggrWindow() int32 {
	if x != nil {
		return x.AggrWindow
	}
	return 0
}

func (x *CreateAssignmentPolicyReq) GetTimeFilters() []*TimeFilter {
	if x != nil {
		return x.TimeFilters
	}
	return nil
}

func (x *CreateAssignmentPolicyReq) GetFilters() []*FilterConditionGroup {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *CreateAssignmentPolicyReq) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

type UpdateAssignmentPolicyReq struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	RuleId        string                  `protobuf:"bytes,1,opt,name=rule_id,json=ruleId,proto3" json:"rule_id,omitempty"`                 // 规则ID
	SpaceId       int64                   `protobuf:"varint,2,opt,name=space_id,json=spaceId,proto3" json:"space_id,omitempty"`             // 空间ID
	RuleName      string                  `protobuf:"bytes,3,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`           // 规则名称
	TemplateId    string                  `protobuf:"bytes,4,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`     // 模板ID
	Description   string                  `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`                     // 描述
	Status        string                  `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty"`                               // 状态
	Priority      int32                   `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`                          // 优先级
	Layers        []*NotificationLayer    `protobuf:"bytes,8,rep,name=layers,proto3" json:"layers,omitempty"`                               // 分层通知
	AggrWindow    int32                   `protobuf:"varint,9,opt,name=aggr_window,json=aggrWindow,proto3" json:"aggr_window,omitempty"`    // 聚合窗口(秒)
	TimeFilters   []*TimeFilter           `protobuf:"bytes,10,rep,name=time_filters,json=timeFilters,proto3" json:"time_filters,omitempty"` // 时间过滤器
	Filters       []*FilterConditionGroup `protobuf:"bytes,11,rep,name=filters,proto3" json:"filters,omitempty"`                            // 过滤器组
	UpdatedBy     int64                   `protobuf:"varint,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`      // 更新者ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAssignmentPolicyReq) Reset() {
	*x = UpdateAssignmentPolicyReq{}
	mi := &file_desc_oncall_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAssignmentPolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAssignmentPolicyReq) ProtoMessage() {}

func (x *UpdateAssignmentPolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAssignmentPolicyReq.ProtoReflect.Descriptor instead.
func (*UpdateAssignmentPolicyReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{47}
}

func (x *UpdateAssignmentPolicyReq) GetRuleId() string {
	if x != nil {
		return x.RuleId
	}
	return ""
}

func (x *UpdateAssignmentPolicyReq) GetSpaceId() int64 {
	if x != nil {
		return x.SpaceId
	}
	return 0
}

func (x *UpdateAssignmentPolicyReq) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *UpdateAssignmentPolicyReq) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *UpdateAssignmentPolicyReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateAssignmentPolicyReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdateAssignmentPolicyReq) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *UpdateAssignmentPolicyReq) GetLayers() []*NotificationLayer {
	if x != nil {
		return x.Layers
	}
	return nil
}

func (x *UpdateAssignmentPolicyReq) GetAggrWindow() int32 {
	if x != nil {
		return x.AggrWindow
	}
	return 0
}

func (x *UpdateAssignmentPolicyReq) GetTimeFilters() []*TimeFilter {
	if x != nil {
		return x.TimeFilters
	}
	return nil
}

func (x *UpdateAssignmentPolicyReq) GetFilters() []*FilterConditionGroup {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *UpdateAssignmentPolicyReq) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

type AssignmentPolicyListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          uint64                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	SpaceId       *int64                 `protobuf:"varint,3,opt,name=space_id,json=spaceId,proto3,oneof" json:"space_id,omitempty"` // 空间ID
	Status        *string                `protobuf:"bytes,4,opt,name=status,proto3,oneof" json:"status,omitempty"`                   // 状态
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentPolicyListReq) Reset() {
	*x = AssignmentPolicyListReq{}
	mi := &file_desc_oncall_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentPolicyListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentPolicyListReq) ProtoMessage() {}

func (x *AssignmentPolicyListReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentPolicyListReq.ProtoReflect.Descriptor instead.
func (*AssignmentPolicyListReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{48}
}

func (x *AssignmentPolicyListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *AssignmentPolicyListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *AssignmentPolicyListReq) GetSpaceId() int64 {
	if x != nil && x.SpaceId != nil {
		return *x.SpaceId
	}
	return 0
}

func (x *AssignmentPolicyListReq) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

type AssignmentPolicyListResp struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Total         uint64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*AssignmentPolicyInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AssignmentPolicyListResp) Reset() {
	*x = AssignmentPolicyListResp{}
	mi := &file_desc_oncall_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AssignmentPolicyListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssignmentPolicyListResp) ProtoMessage() {}

func (x *AssignmentPolicyListResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssignmentPolicyListResp.ProtoReflect.Descriptor instead.
func (*AssignmentPolicyListResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{49}
}

func (x *AssignmentPolicyListResp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *AssignmentPolicyListResp) GetList() []*AssignmentPolicyInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// UUID请求消息
type UUIDReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UUIDReq) Reset() {
	*x = UUIDReq{}
	mi := &file_desc_oncall_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UUIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UUIDReq) ProtoMessage() {}

func (x *UUIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UUIDReq.ProtoReflect.Descriptor instead.
func (*UUIDReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{50}
}

func (x *UUIDReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UUIDsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UUIDsReq) Reset() {
	*x = UUIDsReq{}
	mi := &file_desc_oncall_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UUIDsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UUIDsReq) ProtoMessage() {}

func (x *UUIDsReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UUIDsReq.ProtoReflect.Descriptor instead.
func (*UUIDsReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{51}
}

func (x *UUIDsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

// 添加新的 RawAlert 相关消息和服务
type RawAlertInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RawId         string                 `protobuf:"bytes,2,opt,name=raw_id,json=rawId,proto3" json:"raw_id,omitempty"`
	RawData       string                 `protobuf:"bytes,3,opt,name=raw_data,json=rawData,proto3" json:"raw_data,omitempty"` // 原始数据
	CreatedAt     int64                  `protobuf:"varint,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RawAlertInfo) Reset() {
	*x = RawAlertInfo{}
	mi := &file_desc_oncall_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RawAlertInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawAlertInfo) ProtoMessage() {}

func (x *RawAlertInfo) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawAlertInfo.ProtoReflect.Descriptor instead.
func (*RawAlertInfo) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{52}
}

func (x *RawAlertInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RawAlertInfo) GetRawId() string {
	if x != nil {
		return x.RawId
	}
	return ""
}

func (x *RawAlertInfo) GetRawData() string {
	if x != nil {
		return x.RawData
	}
	return ""
}

func (x *RawAlertInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type RawAlertListReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Page           uint64                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize       uint64                 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	RawId          *string                `protobuf:"bytes,3,opt,name=raw_id,json=rawId,proto3,oneof" json:"raw_id,omitempty"`
	Severity       *string                `protobuf:"bytes,4,opt,name=severity,proto3,oneof" json:"severity,omitempty"`
	Status         *string                `protobuf:"bytes,5,opt,name=status,proto3,oneof" json:"status,omitempty"`
	StartTimeBegin *int64                 `protobuf:"varint,6,opt,name=start_time_begin,json=startTimeBegin,proto3,oneof" json:"start_time_begin,omitempty"`
	StartTimeEnd   *int64                 `protobuf:"varint,7,opt,name=start_time_end,json=startTimeEnd,proto3,oneof" json:"start_time_end,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RawAlertListReq) Reset() {
	*x = RawAlertListReq{}
	mi := &file_desc_oncall_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RawAlertListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawAlertListReq) ProtoMessage() {}

func (x *RawAlertListReq) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawAlertListReq.ProtoReflect.Descriptor instead.
func (*RawAlertListReq) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{53}
}

func (x *RawAlertListReq) GetPage() uint64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *RawAlertListReq) GetPageSize() uint64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *RawAlertListReq) GetRawId() string {
	if x != nil && x.RawId != nil {
		return *x.RawId
	}
	return ""
}

func (x *RawAlertListReq) GetSeverity() string {
	if x != nil && x.Severity != nil {
		return *x.Severity
	}
	return ""
}

func (x *RawAlertListReq) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *RawAlertListReq) GetStartTimeBegin() int64 {
	if x != nil && x.StartTimeBegin != nil {
		return *x.StartTimeBegin
	}
	return 0
}

func (x *RawAlertListReq) GetStartTimeEnd() int64 {
	if x != nil && x.StartTimeEnd != nil {
		return *x.StartTimeEnd
	}
	return 0
}

type RawAlertListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         uint64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*RawAlertInfo        `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RawAlertListResp) Reset() {
	*x = RawAlertListResp{}
	mi := &file_desc_oncall_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RawAlertListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawAlertListResp) ProtoMessage() {}

func (x *RawAlertListResp) ProtoReflect() protoreflect.Message {
	mi := &file_desc_oncall_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawAlertListResp.ProtoReflect.Descriptor instead.
func (*RawAlertListResp) Descriptor() ([]byte, []int) {
	return file_desc_oncall_proto_rawDescGZIP(), []int{54}
}

func (x *RawAlertListResp) GetTotal() uint64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *RawAlertListResp) GetList() []*RawAlertInfo {
	if x != nil {
		return x.List
	}
	return nil
}

var File_desc_oncall_proto protoreflect.FileDescriptor

var file_desc_oncall_proto_rawDesc = string([]byte{
	0x0a, 0x11, 0x64, 0x65, 0x73, 0x63, 0x2f, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x06, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x22, 0x2e, 0x0a, 0x0a, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x1b, 0x0a, 0x07, 0x42,
	0x61, 0x73, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x1c, 0x0a, 0x08, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x30, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x65, 0x55, 0x55,
	0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x17, 0x0a, 0x05, 0x49, 0x44, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1a, 0x0a, 0x06, 0x49, 0x44,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0xa6, 0x03, 0x0a, 0x0f, 0x49, 0x6e, 0x74, 0x65, 0x67,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x69,
	0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x61, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x61, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6e,
	0x6f, 0x69, 0x73, 0x65, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6e, 0x6f, 0x69, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x16,
	0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x6c,
	0x65, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x6f,
	0x75, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x22,
	0x7a, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6b,
	0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7a, 0x0a, 0x14, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xab, 0x01, 0x0a, 0x12, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x17, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x88, 0x01,
	0x01, 0x12, 0x17, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x02, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x42, 0x07, 0x0a, 0x05,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x58, 0x0a, 0x13, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22,
	0xb4, 0x01, 0x0a, 0x0e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6c, 0x6f, 0x67, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x27, 0x0a,
	0x0f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x11, 0x44, 0x61, 0x74, 0x61, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a,
	0x09, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x17, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22,
	0x56, 0x0a, 0x12, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x6e, 0x63, 0x61,
	0x6c, 0x6c, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xce, 0x02, 0x0a, 0x0e, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x43,
	0x61, 0x73, 0x65, 0x52, 0x05, 0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x6e,
	0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x52, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x76, 0x0a, 0x09, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x43, 0x61, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x02, 0x69, 0x66, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x02, 0x69, 0x66, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x74, 0x68, 0x72, 0x6f, 0x75, 0x67, 0x68,
	0x22, 0x4a, 0x0a, 0x0e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x76, 0x61, 0x6c, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x76, 0x61, 0x6c, 0x73, 0x22, 0x2f, 0x0a, 0x0c,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x49, 0x64, 0x73, 0x22, 0xcc, 0x01,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x05,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6f, 0x6e,
	0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x05,
	0x63, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e,
	0x52, 0x6f, 0x75, 0x74, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x64, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xf6, 0x01, 0x0a,
	0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x05, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6f, 0x6e, 0x63,
	0x61, 0x6c, 0x6c, 0x2e, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x43, 0x61, 0x73, 0x65, 0x52, 0x05, 0x63,
	0x61, 0x73, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x07, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xab, 0x01, 0x0a, 0x11, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2a, 0x0a, 0x0e,
	0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x88, 0x01, 0x01, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x56, 0x0a, 0x12, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xdd, 0x07, 0x0a, 0x09,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x64, 0x75, 0x70, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x64, 0x75, 0x70, 0x4b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x35, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x72, 0x5f, 0x6d, 0x75, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x65, 0x76, 0x65, 0x72, 0x4d, 0x75, 0x74, 0x65, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x28, 0x0a, 0x10,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x68, 0x61,
	0x6e, 0x6e, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x43, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6f, 0x6e, 0x63, 0x61,
	0x6c, 0x6c, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x61, 0x77,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a,
	0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x3a, 0x0a, 0x0c, 0x52, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x82, 0x04, 0x0a, 0x0c,
	0x41, 0x6c, 0x65, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a,
	0x08, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x07, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a,
	0x09, 0x64, 0x65, 0x64, 0x75, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x01, 0x52, 0x08, 0x64, 0x65, 0x64, 0x75, 0x70, 0x4b, 0x65, 0x79, 0x88, 0x01, 0x01, 0x12,
	0x2a, 0x0a, 0x0e, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0d, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x26, 0x0a, 0x0c, 0x61,
	0x6c, 0x65, 0x72, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x03, 0x52, 0x0b, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x04, 0x52,
	0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x88,
	0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x65, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x48, 0x05, 0x52, 0x0c, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x45, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a,
	0x0e, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x07, 0x52, 0x07, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x61, 0x6c,
	0x65, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x70,
	0x5f, 0x6b, 0x65, 0x79, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x73,
	0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x61, 0x6c, 0x65, 0x72,
	0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x42, 0x11, 0x0a,
	0x0f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64,
	0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x22, 0x4c, 0x0a, 0x0d, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xf2,
	0x08, 0x0a, 0x0c, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x64, 0x75, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x64, 0x75, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x08,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x69, 0x6e, 0x63,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x61, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f,
	0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63,
	0x6c, 0x6f, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c,
	0x6c, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x10, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d,
	0x70, 0x61, 0x63, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x70, 0x61,
	0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x63, 0x61, 0x75, 0x73, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x6f, 0x74, 0x43, 0x61, 0x75, 0x73,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x72, 0x5f, 0x6d, 0x75, 0x74, 0x65, 0x64, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x65, 0x76, 0x65, 0x72, 0x4d, 0x75, 0x74, 0x65, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x6e, 0x6f, 0x6f, 0x7a, 0x65, 0x64, 0x42, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6d, 0x65, 0x74,
	0x68, 0x6f, 0x64, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63,
	0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x43,
	0x6e, 0x74, 0x12, 0x42, 0x0a, 0x0a, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x73,
	0x18, 0x1c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e,
	0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x43, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x43, 0x6e, 0x74, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x64, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x28, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x64, 0x61, 0x74, 0x61,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3c, 0x0a, 0x0e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x43, 0x6e,
	0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xdc, 0x03, 0x0a, 0x0f, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x09, 0x64, 0x65, 0x64, 0x75,
	0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x64,
	0x65, 0x64, 0x75, 0x70, 0x4b, 0x65, 0x79, 0x88, 0x01, 0x01, 0x12, 0x30, 0x0a, 0x11, 0x69, 0x6e,
	0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x10, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x2c, 0x0a, 0x0f,
	0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0e, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x66, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x03, 0x52,
	0x09, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a,
	0x10, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x65, 0x67, 0x69,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x48, 0x04, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x05, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x45, 0x6e, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x06, 0x52, 0x07, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x64, 0x65, 0x64, 0x75,
	0x70, 0x5f, 0x6b, 0x65, 0x79, 0x42, 0x14, 0x0a, 0x12, 0x5f, 0x69, 0x6e, 0x63, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x42, 0x12, 0x0a, 0x10, 0x5f,
	0x69, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42,
	0x0c, 0x0a, 0x0a, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x42, 0x13, 0x0a,
	0x11, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x65, 0x67,
	0x69, 0x6e, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x65, 0x6e, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x22, 0x52, 0x0a, 0x10, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x28, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x6e, 0x63,
	0x61, 0x6c, 0x6c, 0x2e, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xe0, 0x02, 0x0a, 0x09, 0x53, 0x70, 0x61, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x33,
	0x0a, 0x0b, 0x69, 0x73, 0x73, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x49, 0x73, 0x73,
	0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x0a, 0x69, 0x73, 0x73, 0x75, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x41, 0x74, 0x22, 0x64, 0x0a, 0x0a, 0x49, 0x73, 0x73,
	0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x22,
	0xb6, 0x01, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x61, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e,
	0x0a, 0x0a, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0xc6, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x61, 0x6d,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x61,
	0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x76, 0x69, 0x73, 0x69, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x22, 0x61, 0x0a, 0x0c, 0x53, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4c, 0x0a, 0x0d, 0x53, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6f, 0x6e, 0x63, 0x61,
	0x6c, 0x6c, 0x2e, 0x53, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0x52, 0x0a, 0x1a, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x20, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69,
	0x74, 0x68, 0x53, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x69, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x8e, 0x04, 0x0a,
	0x14, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x75, 0x6c,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x75,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x31, 0x0a, 0x06, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x6e, 0x63,
	0x61, 0x6c, 0x6c, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x06, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x61, 0x67, 0x67, 0x72, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x61, 0x67, 0x67, 0x72, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x35,
	0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0e,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0xd5, 0x01,
	0x0a, 0x11, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61,
	0x79, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6d, 0x61, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x5f, 0x73, 0x74, 0x65, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x53, 0x74, 0x65,
	0x70, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x65, 0x73, 0x63, 0x61,
	0x6c, 0x61, 0x74, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x6f,
	0x72, 0x63, 0x65, 0x5f, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0d, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74,
	0x65, 0x12, 0x32, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x22, 0xe4, 0x02, 0x0a, 0x12, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07,
	0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x62, 0x0a, 0x14, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x6f, 0x52, 0x6f, 0x6c, 0x65, 0x49,
	0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x54, 0x6f, 0x52, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x02, 0x62, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x52, 0x02,
	0x62, 0x79, 0x12, 0x2b, 0x0a, 0x08, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x52, 0x08, 0x77, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x73, 0x1a,
	0x5b, 0x0a, 0x16, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x6f, 0x52, 0x6f, 0x6c,
	0x65, 0x49, 0x64, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2b, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x6e, 0x63,
	0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x73, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x2a, 0x0a, 0x0d,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x07, 0x72, 0x6f, 0x6c, 0x65, 0x49, 0x64, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x0e, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x12, 0x2b, 0x0a, 0x11, 0x66,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x69, 0x74,
	0x69, 0x63, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x69, 0x74,
	0x69, 0x63, 0x61, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x22, 0x95, 0x01, 0x0a, 0x07, 0x57, 0x65, 0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x39, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x57, 0x65,
	0x62, 0x68, 0x6f, 0x6f, 0x6b, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x1a, 0x3b, 0x0a,
	0x0d, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7a, 0x0a, 0x0a, 0x54, 0x69,
	0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x06, 0x72, 0x65, 0x70, 0x65, 0x61, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x63, 0x61, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x61, 0x6c, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x05, 0x69, 0x73, 0x4f, 0x66, 0x66, 0x22, 0x4b, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6f,
	0x70, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x76, 0x61, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x76,
	0x61, 0x6c, 0x73, 0x22, 0x4f, 0x0a, 0x14, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x37, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43,
	0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0xac, 0x03, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x31, 0x0a, 0x06, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x06, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x67, 0x67, 0x72, 0x5f, 0x77, 0x69, 0x6e,
	0x64, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x67, 0x67, 0x72, 0x57,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x35, 0x0a, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x6e,
	0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x0b, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x07, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x62, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x42, 0x79, 0x22, 0xc5, 0x03, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x75, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x31, 0x0a, 0x06, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x6e, 0x63, 0x61,
	0x6c, 0x6c, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x06, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x61, 0x67, 0x67, 0x72, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x61, 0x67, 0x67, 0x72, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x12, 0x35, 0x0a,
	0x0c, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x12, 0x36, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18,
	0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0x9f, 0x01, 0x0a, 0x17,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x08, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x62, 0x0a,
	0x18, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x30, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0x19, 0x0a, 0x07, 0x55, 0x55, 0x49, 0x44, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1c, 0x0a, 0x08,
	0x55, 0x55, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x6f, 0x0a, 0x0c, 0x52, 0x61,
	0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x61,
	0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x61, 0x77, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x61, 0x77, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x61, 0x77, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xc1, 0x02, 0x0a, 0x0f,
	0x52, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1a, 0x0a, 0x06, 0x72, 0x61, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x05, 0x72, 0x61, 0x77, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08,
	0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x12, 0x1b, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x10, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x48, 0x03, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a, 0x0e, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x48, 0x04, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x45, 0x6e,
	0x64, 0x88, 0x01, 0x01, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x72, 0x61, 0x77, 0x5f, 0x69, 0x64, 0x42,
	0x0b, 0x0a, 0x09, 0x5f, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x42, 0x09, 0x0a, 0x07,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x13, 0x0a, 0x11, 0x5f, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x65, 0x6e, 0x64, 0x22,
	0x52, 0x0a, 0x10, 0x52, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c,
	0x2e, 0x52, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x32, 0xe1, 0x09, 0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c,
	0x6c, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e,
	0x42, 0x61, 0x73, 0x65, 0x55, 0x55, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x43, 0x0a, 0x11,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1c, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x4d, 0x0a, 0x12, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x49, 0x6e, 0x74,
	0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x3e, 0x0a, 0x12, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x79, 0x49, 0x64, 0x12, 0x0f, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e,
	0x55, 0x55, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c,
	0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x37, 0x0a, 0x11, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x55,
	0x55, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c,
	0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12, 0x1b, 0x2e,
	0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6f, 0x6e, 0x63,
	0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x55, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x41, 0x0a, 0x10, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x12, 0x1b, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x4a, 0x0a, 0x11, 0x67, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52,
	0x6f, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c,
	0x6c, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x3a, 0x0a, 0x11, 0x67, 0x65, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65,
	0x42, 0x79, 0x49, 0x64, 0x12, 0x0d, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x10, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x6f, 0x75, 0x74, 0x65, 0x12,
	0x0e, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x49, 0x44, 0x73, 0x52, 0x65, 0x71, 0x1a,
	0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x4f, 0x0a, 0x17, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x22, 0x2e, 0x6f,
	0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x57, 0x69, 0x74, 0x68, 0x53, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x5b, 0x0a, 0x1d, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x49,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x53, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x28, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x73, 0x73,
	0x6f, 0x63, 0x69, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x57, 0x69, 0x74, 0x68, 0x53, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e,
	0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x51, 0x0a, 0x16, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x21, 0x2e, 0x6f, 0x6e, 0x63, 0x61,
	0x6c, 0x6c, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6f,
	0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x55, 0x49, 0x44, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x4d, 0x0a, 0x16, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x21, 0x2e, 0x6f,
	0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x1a,
	0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x5c, 0x0a, 0x17, 0x67, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1f, 0x2e, 0x6f,
	0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e,
	0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x48, 0x0a, 0x17, 0x67, 0x65, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x79, 0x49, 0x64, 0x12, 0x0f, 0x2e, 0x6f, 0x6e, 0x63,
	0x61, 0x6c, 0x6c, 0x2e, 0x55, 0x55, 0x49, 0x44, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x6f, 0x6e,
	0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a, 0x16, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x12, 0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x55, 0x55, 0x49,
	0x44, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x32, 0x9d, 0x02, 0x0a, 0x05, 0x53, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x16, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c,
	0x6c, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x55, 0x55, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x37,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x16, 0x2e,
	0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x70, 0x61,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0c, 0x67, 0x65, 0x74, 0x53, 0x70,
	0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c,
	0x2e, 0x53, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e,
	0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x0c, 0x67, 0x65, 0x74, 0x53, 0x70, 0x61, 0x63, 0x65,
	0x42, 0x79, 0x49, 0x64, 0x12, 0x0d, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x53, 0x70, 0x61,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2f, 0x0a, 0x0b, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x0e, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x49,
	0x44, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e, 0x6f, 0x6e, 0x63, 0x61, 0x6c, 0x6c, 0x2e, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x42, 0x0a, 0x5a, 0x08, 0x2e, 0x2f, 0x6f, 0x6e, 0x63,
	0x61, 0x6c, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_desc_oncall_proto_rawDescOnce sync.Once
	file_desc_oncall_proto_rawDescData []byte
)

func file_desc_oncall_proto_rawDescGZIP() []byte {
	file_desc_oncall_proto_rawDescOnce.Do(func() {
		file_desc_oncall_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_desc_oncall_proto_rawDesc), len(file_desc_oncall_proto_rawDesc)))
	})
	return file_desc_oncall_proto_rawDescData
}

var file_desc_oncall_proto_msgTypes = make([]protoimpl.MessageInfo, 61)
var file_desc_oncall_proto_goTypes = []any{
	(*BaseIDResp)(nil),                       // 0: oncall.BaseIDResp
	(*BaseMsg)(nil),                          // 1: oncall.BaseMsg
	(*BaseResp)(nil),                         // 2: oncall.BaseResp
	(*BaseUUIDResp)(nil),                     // 3: oncall.BaseUUIDResp
	(*Empty)(nil),                            // 4: oncall.Empty
	(*IDReq)(nil),                            // 5: oncall.IDReq
	(*IDsReq)(nil),                           // 6: oncall.IDsReq
	(*IntegrationInfo)(nil),                  // 7: oncall.IntegrationInfo
	(*CreateIntegrationReq)(nil),             // 8: oncall.CreateIntegrationReq
	(*UpdateIntegrationReq)(nil),             // 9: oncall.UpdateIntegrationReq
	(*IntegrationListReq)(nil),               // 10: oncall.IntegrationListReq
	(*IntegrationListResp)(nil),              // 11: oncall.IntegrationListResp
	(*DataSourceInfo)(nil),                   // 12: oncall.DataSourceInfo
	(*DataSourceListReq)(nil),                // 13: oncall.DataSourceListReq
	(*DataSourceListResp)(nil),               // 14: oncall.DataSourceListResp
	(*AlertRouteInfo)(nil),                   // 15: oncall.AlertRouteInfo
	(*RouteCase)(nil),                        // 16: oncall.RouteCase
	(*RouteCondition)(nil),                   // 17: oncall.RouteCondition
	(*RouteDefault)(nil),                     // 18: oncall.RouteDefault
	(*CreateAlertRouteReq)(nil),              // 19: oncall.CreateAlertRouteReq
	(*UpdateAlertRouteReq)(nil),              // 20: oncall.UpdateAlertRouteReq
	(*AlertRouteListReq)(nil),                // 21: oncall.AlertRouteListReq
	(*AlertRouteListResp)(nil),               // 22: oncall.AlertRouteListResp
	(*AlertInfo)(nil),                        // 23: oncall.AlertInfo
	(*AlertListReq)(nil),                     // 24: oncall.AlertListReq
	(*AlertListResp)(nil),                    // 25: oncall.AlertListResp
	(*IncidentInfo)(nil),                     // 26: oncall.IncidentInfo
	(*IncidentListReq)(nil),                  // 27: oncall.IncidentListReq
	(*IncidentListResp)(nil),                 // 28: oncall.IncidentListResp
	(*SpaceInfo)(nil),                        // 29: oncall.SpaceInfo
	(*IssueStats)(nil),                       // 30: oncall.IssueStats
	(*CreateSpaceReq)(nil),                   // 31: oncall.CreateSpaceReq
	(*UpdateSpaceReq)(nil),                   // 32: oncall.UpdateSpaceReq
	(*SpaceListReq)(nil),                     // 33: oncall.SpaceListReq
	(*SpaceListResp)(nil),                    // 34: oncall.SpaceListResp
	(*AssociateAlertWithSpaceReq)(nil),       // 35: oncall.AssociateAlertWithSpaceReq
	(*AssociateIntegrationWithSpaceReq)(nil), // 36: oncall.AssociateIntegrationWithSpaceReq
	(*AssignmentPolicyInfo)(nil),             // 37: oncall.AssignmentPolicyInfo
	(*NotificationLayer)(nil),                // 38: oncall.NotificationLayer
	(*NotificationTarget)(nil),               // 39: oncall.NotificationTarget
	(*ScheduleRoles)(nil),                    // 40: oncall.ScheduleRoles
	(*NotificationBy)(nil),                   // 41: oncall.NotificationBy
	(*Webhook)(nil),                          // 42: oncall.Webhook
	(*TimeFilter)(nil),                       // 43: oncall.TimeFilter
	(*FilterCondition)(nil),                  // 44: oncall.FilterCondition
	(*FilterConditionGroup)(nil),             // 45: oncall.FilterConditionGroup
	(*CreateAssignmentPolicyReq)(nil),        // 46: oncall.CreateAssignmentPolicyReq
	(*UpdateAssignmentPolicyReq)(nil),        // 47: oncall.UpdateAssignmentPolicyReq
	(*AssignmentPolicyListReq)(nil),          // 48: oncall.AssignmentPolicyListReq
	(*AssignmentPolicyListResp)(nil),         // 49: oncall.AssignmentPolicyListResp
	(*UUIDReq)(nil),                          // 50: oncall.UUIDReq
	(*UUIDsReq)(nil),                         // 51: oncall.UUIDsReq
	(*RawAlertInfo)(nil),                     // 52: oncall.RawAlertInfo
	(*RawAlertListReq)(nil),                  // 53: oncall.RawAlertListReq
	(*RawAlertListResp)(nil),                 // 54: oncall.RawAlertListResp
	nil,                                      // 55: oncall.AlertInfo.LabelsEntry
	nil,                                      // 56: oncall.AlertInfo.RawDataEntry
	nil,                                      // 57: oncall.IncidentInfo.LabelsEntry
	nil,                                      // 58: oncall.IncidentInfo.AlertCntsEntry
	nil,                                      // 59: oncall.NotificationTarget.ScheduleToRoleIdsEntry
	nil,                                      // 60: oncall.Webhook.SettingsEntry
}
var file_desc_oncall_proto_depIdxs = []int32{
	7,  // 0: oncall.IntegrationListResp.list:type_name -> oncall.IntegrationInfo
	12, // 1: oncall.DataSourceListResp.list:type_name -> oncall.DataSourceInfo
	16, // 2: oncall.AlertRouteInfo.cases:type_name -> oncall.RouteCase
	18, // 3: oncall.AlertRouteInfo.default:type_name -> oncall.RouteDefault
	17, // 4: oncall.RouteCase.if:type_name -> oncall.RouteCondition
	16, // 5: oncall.CreateAlertRouteReq.cases:type_name -> oncall.RouteCase
	18, // 6: oncall.CreateAlertRouteReq.default:type_name -> oncall.RouteDefault
	16, // 7: oncall.UpdateAlertRouteReq.cases:type_name -> oncall.RouteCase
	18, // 8: oncall.UpdateAlertRouteReq.default:type_name -> oncall.RouteDefault
	15, // 9: oncall.AlertRouteListResp.list:type_name -> oncall.AlertRouteInfo
	55, // 10: oncall.AlertInfo.labels:type_name -> oncall.AlertInfo.LabelsEntry
	56, // 11: oncall.AlertInfo.raw_data:type_name -> oncall.AlertInfo.RawDataEntry
	23, // 12: oncall.AlertListResp.list:type_name -> oncall.AlertInfo
	57, // 13: oncall.IncidentInfo.labels:type_name -> oncall.IncidentInfo.LabelsEntry
	58, // 14: oncall.IncidentInfo.alert_cnts:type_name -> oncall.IncidentInfo.AlertCntsEntry
	26, // 15: oncall.IncidentListResp.list:type_name -> oncall.IncidentInfo
	30, // 16: oncall.SpaceInfo.issue_stats:type_name -> oncall.IssueStats
	29, // 17: oncall.SpaceListResp.list:type_name -> oncall.SpaceInfo
	38, // 18: oncall.AssignmentPolicyInfo.layers:type_name -> oncall.NotificationLayer
	43, // 19: oncall.AssignmentPolicyInfo.time_filters:type_name -> oncall.TimeFilter
	45, // 20: oncall.AssignmentPolicyInfo.filters:type_name -> oncall.FilterConditionGroup
	39, // 21: oncall.NotificationLayer.target:type_name -> oncall.NotificationTarget
	59, // 22: oncall.NotificationTarget.schedule_to_role_ids:type_name -> oncall.NotificationTarget.ScheduleToRoleIdsEntry
	41, // 23: oncall.NotificationTarget.by:type_name -> oncall.NotificationBy
	42, // 24: oncall.NotificationTarget.webhooks:type_name -> oncall.Webhook
	60, // 25: oncall.Webhook.settings:type_name -> oncall.Webhook.SettingsEntry
	44, // 26: oncall.FilterConditionGroup.conditions:type_name -> oncall.FilterCondition
	38, // 27: oncall.CreateAssignmentPolicyReq.layers:type_name -> oncall.NotificationLayer
	43, // 28: oncall.CreateAssignmentPolicyReq.time_filters:type_name -> oncall.TimeFilter
	45, // 29: oncall.CreateAssignmentPolicyReq.filters:type_name -> oncall.FilterConditionGroup
	38, // 30: oncall.UpdateAssignmentPolicyReq.layers:type_name -> oncall.NotificationLayer
	43, // 31: oncall.UpdateAssignmentPolicyReq.time_filters:type_name -> oncall.TimeFilter
	45, // 32: oncall.UpdateAssignmentPolicyReq.filters:type_name -> oncall.FilterConditionGroup
	37, // 33: oncall.AssignmentPolicyListResp.list:type_name -> oncall.AssignmentPolicyInfo
	52, // 34: oncall.RawAlertListResp.list:type_name -> oncall.RawAlertInfo
	40, // 35: oncall.NotificationTarget.ScheduleToRoleIdsEntry.value:type_name -> oncall.ScheduleRoles
	8,  // 36: oncall.Integration.createIntegration:input_type -> oncall.CreateIntegrationReq
	9,  // 37: oncall.Integration.updateIntegration:input_type -> oncall.UpdateIntegrationReq
	10, // 38: oncall.Integration.getIntegrationList:input_type -> oncall.IntegrationListReq
	50, // 39: oncall.Integration.getIntegrationById:input_type -> oncall.UUIDReq
	51, // 40: oncall.Integration.deleteIntegration:input_type -> oncall.UUIDsReq
	19, // 41: oncall.Integration.createAlertRoute:input_type -> oncall.CreateAlertRouteReq
	20, // 42: oncall.Integration.updateAlertRoute:input_type -> oncall.UpdateAlertRouteReq
	21, // 43: oncall.Integration.getAlertRouteList:input_type -> oncall.AlertRouteListReq
	5,  // 44: oncall.Integration.getAlertRouteById:input_type -> oncall.IDReq
	6,  // 45: oncall.Integration.deleteAlertRoute:input_type -> oncall.IDsReq
	35, // 46: oncall.Integration.associateAlertWithSpace:input_type -> oncall.AssociateAlertWithSpaceReq
	36, // 47: oncall.Integration.associateIntegrationWithSpace:input_type -> oncall.AssociateIntegrationWithSpaceReq
	46, // 48: oncall.Integration.createAssignmentPolicy:input_type -> oncall.CreateAssignmentPolicyReq
	47, // 49: oncall.Integration.updateAssignmentPolicy:input_type -> oncall.UpdateAssignmentPolicyReq
	48, // 50: oncall.Integration.getAssignmentPolicyList:input_type -> oncall.AssignmentPolicyListReq
	50, // 51: oncall.Integration.getAssignmentPolicyById:input_type -> oncall.UUIDReq
	51, // 52: oncall.Integration.deleteAssignmentPolicy:input_type -> oncall.UUIDsReq
	31, // 53: oncall.Space.createSpace:input_type -> oncall.CreateSpaceReq
	32, // 54: oncall.Space.updateSpace:input_type -> oncall.UpdateSpaceReq
	33, // 55: oncall.Space.getSpaceList:input_type -> oncall.SpaceListReq
	5,  // 56: oncall.Space.getSpaceById:input_type -> oncall.IDReq
	6,  // 57: oncall.Space.deleteSpace:input_type -> oncall.IDsReq
	3,  // 58: oncall.Integration.createIntegration:output_type -> oncall.BaseUUIDResp
	2,  // 59: oncall.Integration.updateIntegration:output_type -> oncall.BaseResp
	11, // 60: oncall.Integration.getIntegrationList:output_type -> oncall.IntegrationListResp
	7,  // 61: oncall.Integration.getIntegrationById:output_type -> oncall.IntegrationInfo
	2,  // 62: oncall.Integration.deleteIntegration:output_type -> oncall.BaseResp
	3,  // 63: oncall.Integration.createAlertRoute:output_type -> oncall.BaseUUIDResp
	2,  // 64: oncall.Integration.updateAlertRoute:output_type -> oncall.BaseResp
	22, // 65: oncall.Integration.getAlertRouteList:output_type -> oncall.AlertRouteListResp
	15, // 66: oncall.Integration.getAlertRouteById:output_type -> oncall.AlertRouteInfo
	2,  // 67: oncall.Integration.deleteAlertRoute:output_type -> oncall.BaseResp
	2,  // 68: oncall.Integration.associateAlertWithSpace:output_type -> oncall.BaseResp
	2,  // 69: oncall.Integration.associateIntegrationWithSpace:output_type -> oncall.BaseResp
	3,  // 70: oncall.Integration.createAssignmentPolicy:output_type -> oncall.BaseUUIDResp
	2,  // 71: oncall.Integration.updateAssignmentPolicy:output_type -> oncall.BaseResp
	49, // 72: oncall.Integration.getAssignmentPolicyList:output_type -> oncall.AssignmentPolicyListResp
	37, // 73: oncall.Integration.getAssignmentPolicyById:output_type -> oncall.AssignmentPolicyInfo
	2,  // 74: oncall.Integration.deleteAssignmentPolicy:output_type -> oncall.BaseResp
	3,  // 75: oncall.Space.createSpace:output_type -> oncall.BaseUUIDResp
	2,  // 76: oncall.Space.updateSpace:output_type -> oncall.BaseResp
	34, // 77: oncall.Space.getSpaceList:output_type -> oncall.SpaceListResp
	29, // 78: oncall.Space.getSpaceById:output_type -> oncall.SpaceInfo
	2,  // 79: oncall.Space.deleteSpace:output_type -> oncall.BaseResp
	58, // [58:80] is the sub-list for method output_type
	36, // [36:58] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_desc_oncall_proto_init() }
func file_desc_oncall_proto_init() {
	if File_desc_oncall_proto != nil {
		return
	}
	file_desc_oncall_proto_msgTypes[10].OneofWrappers = []any{}
	file_desc_oncall_proto_msgTypes[13].OneofWrappers = []any{}
	file_desc_oncall_proto_msgTypes[21].OneofWrappers = []any{}
	file_desc_oncall_proto_msgTypes[24].OneofWrappers = []any{}
	file_desc_oncall_proto_msgTypes[27].OneofWrappers = []any{}
	file_desc_oncall_proto_msgTypes[33].OneofWrappers = []any{}
	file_desc_oncall_proto_msgTypes[48].OneofWrappers = []any{}
	file_desc_oncall_proto_msgTypes[53].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_desc_oncall_proto_rawDesc), len(file_desc_oncall_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   61,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_desc_oncall_proto_goTypes,
		DependencyIndexes: file_desc_oncall_proto_depIdxs,
		MessageInfos:      file_desc_oncall_proto_msgTypes,
	}.Build()
	File_desc_oncall_proto = out.File
	file_desc_oncall_proto_goTypes = nil
	file_desc_oncall_proto_depIdxs = nil
}
