// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.23.3
// source: desc/oncall.proto

package oncall

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Integration_CreateIntegration_FullMethodName             = "/oncall.Integration/createIntegration"
	Integration_UpdateIntegration_FullMethodName             = "/oncall.Integration/updateIntegration"
	Integration_GetIntegrationList_FullMethodName            = "/oncall.Integration/getIntegrationList"
	Integration_GetIntegrationById_FullMethodName            = "/oncall.Integration/getIntegrationById"
	Integration_DeleteIntegration_FullMethodName             = "/oncall.Integration/deleteIntegration"
	Integration_CreateAlertRoute_FullMethodName              = "/oncall.Integration/createAlertRoute"
	Integration_UpdateAlertRoute_FullMethodName              = "/oncall.Integration/updateAlertRoute"
	Integration_GetAlertRouteList_FullMethodName             = "/oncall.Integration/getAlertRouteList"
	Integration_GetAlertRouteById_FullMethodName             = "/oncall.Integration/getAlertRouteById"
	Integration_DeleteAlertRoute_FullMethodName              = "/oncall.Integration/deleteAlertRoute"
	Integration_AssociateAlertWithSpace_FullMethodName       = "/oncall.Integration/associateAlertWithSpace"
	Integration_AssociateIntegrationWithSpace_FullMethodName = "/oncall.Integration/associateIntegrationWithSpace"
	Integration_CreateAssignmentPolicy_FullMethodName        = "/oncall.Integration/createAssignmentPolicy"
	Integration_UpdateAssignmentPolicy_FullMethodName        = "/oncall.Integration/updateAssignmentPolicy"
	Integration_GetAssignmentPolicyList_FullMethodName       = "/oncall.Integration/getAssignmentPolicyList"
	Integration_GetAssignmentPolicyById_FullMethodName       = "/oncall.Integration/getAssignmentPolicyById"
	Integration_DeleteAssignmentPolicy_FullMethodName        = "/oncall.Integration/deleteAssignmentPolicy"
)

// IntegrationClient is the client API for Integration service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type IntegrationClient interface {
	// Integration management
	CreateIntegration(ctx context.Context, in *CreateIntegrationReq, opts ...grpc.CallOption) (*BaseUUIDResp, error)
	UpdateIntegration(ctx context.Context, in *UpdateIntegrationReq, opts ...grpc.CallOption) (*BaseResp, error)
	GetIntegrationList(ctx context.Context, in *IntegrationListReq, opts ...grpc.CallOption) (*IntegrationListResp, error)
	GetIntegrationById(ctx context.Context, in *UUIDReq, opts ...grpc.CallOption) (*IntegrationInfo, error)
	DeleteIntegration(ctx context.Context, in *UUIDsReq, opts ...grpc.CallOption) (*BaseResp, error)
	// AlertRoute management
	CreateAlertRoute(ctx context.Context, in *CreateAlertRouteReq, opts ...grpc.CallOption) (*BaseUUIDResp, error)
	UpdateAlertRoute(ctx context.Context, in *UpdateAlertRouteReq, opts ...grpc.CallOption) (*BaseResp, error)
	GetAlertRouteList(ctx context.Context, in *AlertRouteListReq, opts ...grpc.CallOption) (*AlertRouteListResp, error)
	GetAlertRouteById(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*AlertRouteInfo, error)
	DeleteAlertRoute(ctx context.Context, in *IDsReq, opts ...grpc.CallOption) (*BaseResp, error)
	// 关联管理
	AssociateAlertWithSpace(ctx context.Context, in *AssociateAlertWithSpaceReq, opts ...grpc.CallOption) (*BaseResp, error)
	AssociateIntegrationWithSpace(ctx context.Context, in *AssociateIntegrationWithSpaceReq, opts ...grpc.CallOption) (*BaseResp, error)
	// Assignment Policy management - 分配策略管理
	CreateAssignmentPolicy(ctx context.Context, in *CreateAssignmentPolicyReq, opts ...grpc.CallOption) (*BaseUUIDResp, error)
	UpdateAssignmentPolicy(ctx context.Context, in *UpdateAssignmentPolicyReq, opts ...grpc.CallOption) (*BaseResp, error)
	GetAssignmentPolicyList(ctx context.Context, in *AssignmentPolicyListReq, opts ...grpc.CallOption) (*AssignmentPolicyListResp, error)
	GetAssignmentPolicyById(ctx context.Context, in *UUIDReq, opts ...grpc.CallOption) (*AssignmentPolicyInfo, error)
	DeleteAssignmentPolicy(ctx context.Context, in *UUIDsReq, opts ...grpc.CallOption) (*BaseResp, error)
}

type integrationClient struct {
	cc grpc.ClientConnInterface
}

func NewIntegrationClient(cc grpc.ClientConnInterface) IntegrationClient {
	return &integrationClient{cc}
}

func (c *integrationClient) CreateIntegration(ctx context.Context, in *CreateIntegrationReq, opts ...grpc.CallOption) (*BaseUUIDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseUUIDResp)
	err := c.cc.Invoke(ctx, Integration_CreateIntegration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) UpdateIntegration(ctx context.Context, in *UpdateIntegrationReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Integration_UpdateIntegration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) GetIntegrationList(ctx context.Context, in *IntegrationListReq, opts ...grpc.CallOption) (*IntegrationListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IntegrationListResp)
	err := c.cc.Invoke(ctx, Integration_GetIntegrationList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) GetIntegrationById(ctx context.Context, in *UUIDReq, opts ...grpc.CallOption) (*IntegrationInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IntegrationInfo)
	err := c.cc.Invoke(ctx, Integration_GetIntegrationById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) DeleteIntegration(ctx context.Context, in *UUIDsReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Integration_DeleteIntegration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) CreateAlertRoute(ctx context.Context, in *CreateAlertRouteReq, opts ...grpc.CallOption) (*BaseUUIDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseUUIDResp)
	err := c.cc.Invoke(ctx, Integration_CreateAlertRoute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) UpdateAlertRoute(ctx context.Context, in *UpdateAlertRouteReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Integration_UpdateAlertRoute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) GetAlertRouteList(ctx context.Context, in *AlertRouteListReq, opts ...grpc.CallOption) (*AlertRouteListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AlertRouteListResp)
	err := c.cc.Invoke(ctx, Integration_GetAlertRouteList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) GetAlertRouteById(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*AlertRouteInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AlertRouteInfo)
	err := c.cc.Invoke(ctx, Integration_GetAlertRouteById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) DeleteAlertRoute(ctx context.Context, in *IDsReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Integration_DeleteAlertRoute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) AssociateAlertWithSpace(ctx context.Context, in *AssociateAlertWithSpaceReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Integration_AssociateAlertWithSpace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) AssociateIntegrationWithSpace(ctx context.Context, in *AssociateIntegrationWithSpaceReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Integration_AssociateIntegrationWithSpace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) CreateAssignmentPolicy(ctx context.Context, in *CreateAssignmentPolicyReq, opts ...grpc.CallOption) (*BaseUUIDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseUUIDResp)
	err := c.cc.Invoke(ctx, Integration_CreateAssignmentPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) UpdateAssignmentPolicy(ctx context.Context, in *UpdateAssignmentPolicyReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Integration_UpdateAssignmentPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) GetAssignmentPolicyList(ctx context.Context, in *AssignmentPolicyListReq, opts ...grpc.CallOption) (*AssignmentPolicyListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentPolicyListResp)
	err := c.cc.Invoke(ctx, Integration_GetAssignmentPolicyList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) GetAssignmentPolicyById(ctx context.Context, in *UUIDReq, opts ...grpc.CallOption) (*AssignmentPolicyInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AssignmentPolicyInfo)
	err := c.cc.Invoke(ctx, Integration_GetAssignmentPolicyById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *integrationClient) DeleteAssignmentPolicy(ctx context.Context, in *UUIDsReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Integration_DeleteAssignmentPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IntegrationServer is the server API for Integration service.
// All implementations must embed UnimplementedIntegrationServer
// for forward compatibility.
type IntegrationServer interface {
	// Integration management
	CreateIntegration(context.Context, *CreateIntegrationReq) (*BaseUUIDResp, error)
	UpdateIntegration(context.Context, *UpdateIntegrationReq) (*BaseResp, error)
	GetIntegrationList(context.Context, *IntegrationListReq) (*IntegrationListResp, error)
	GetIntegrationById(context.Context, *UUIDReq) (*IntegrationInfo, error)
	DeleteIntegration(context.Context, *UUIDsReq) (*BaseResp, error)
	// AlertRoute management
	CreateAlertRoute(context.Context, *CreateAlertRouteReq) (*BaseUUIDResp, error)
	UpdateAlertRoute(context.Context, *UpdateAlertRouteReq) (*BaseResp, error)
	GetAlertRouteList(context.Context, *AlertRouteListReq) (*AlertRouteListResp, error)
	GetAlertRouteById(context.Context, *IDReq) (*AlertRouteInfo, error)
	DeleteAlertRoute(context.Context, *IDsReq) (*BaseResp, error)
	// 关联管理
	AssociateAlertWithSpace(context.Context, *AssociateAlertWithSpaceReq) (*BaseResp, error)
	AssociateIntegrationWithSpace(context.Context, *AssociateIntegrationWithSpaceReq) (*BaseResp, error)
	// Assignment Policy management - 分配策略管理
	CreateAssignmentPolicy(context.Context, *CreateAssignmentPolicyReq) (*BaseUUIDResp, error)
	UpdateAssignmentPolicy(context.Context, *UpdateAssignmentPolicyReq) (*BaseResp, error)
	GetAssignmentPolicyList(context.Context, *AssignmentPolicyListReq) (*AssignmentPolicyListResp, error)
	GetAssignmentPolicyById(context.Context, *UUIDReq) (*AssignmentPolicyInfo, error)
	DeleteAssignmentPolicy(context.Context, *UUIDsReq) (*BaseResp, error)
	mustEmbedUnimplementedIntegrationServer()
}

// UnimplementedIntegrationServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedIntegrationServer struct{}

func (UnimplementedIntegrationServer) CreateIntegration(context.Context, *CreateIntegrationReq) (*BaseUUIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIntegration not implemented")
}
func (UnimplementedIntegrationServer) UpdateIntegration(context.Context, *UpdateIntegrationReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIntegration not implemented")
}
func (UnimplementedIntegrationServer) GetIntegrationList(context.Context, *IntegrationListReq) (*IntegrationListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIntegrationList not implemented")
}
func (UnimplementedIntegrationServer) GetIntegrationById(context.Context, *UUIDReq) (*IntegrationInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIntegrationById not implemented")
}
func (UnimplementedIntegrationServer) DeleteIntegration(context.Context, *UUIDsReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIntegration not implemented")
}
func (UnimplementedIntegrationServer) CreateAlertRoute(context.Context, *CreateAlertRouteReq) (*BaseUUIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAlertRoute not implemented")
}
func (UnimplementedIntegrationServer) UpdateAlertRoute(context.Context, *UpdateAlertRouteReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAlertRoute not implemented")
}
func (UnimplementedIntegrationServer) GetAlertRouteList(context.Context, *AlertRouteListReq) (*AlertRouteListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlertRouteList not implemented")
}
func (UnimplementedIntegrationServer) GetAlertRouteById(context.Context, *IDReq) (*AlertRouteInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAlertRouteById not implemented")
}
func (UnimplementedIntegrationServer) DeleteAlertRoute(context.Context, *IDsReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAlertRoute not implemented")
}
func (UnimplementedIntegrationServer) AssociateAlertWithSpace(context.Context, *AssociateAlertWithSpaceReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssociateAlertWithSpace not implemented")
}
func (UnimplementedIntegrationServer) AssociateIntegrationWithSpace(context.Context, *AssociateIntegrationWithSpaceReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AssociateIntegrationWithSpace not implemented")
}
func (UnimplementedIntegrationServer) CreateAssignmentPolicy(context.Context, *CreateAssignmentPolicyReq) (*BaseUUIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAssignmentPolicy not implemented")
}
func (UnimplementedIntegrationServer) UpdateAssignmentPolicy(context.Context, *UpdateAssignmentPolicyReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAssignmentPolicy not implemented")
}
func (UnimplementedIntegrationServer) GetAssignmentPolicyList(context.Context, *AssignmentPolicyListReq) (*AssignmentPolicyListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssignmentPolicyList not implemented")
}
func (UnimplementedIntegrationServer) GetAssignmentPolicyById(context.Context, *UUIDReq) (*AssignmentPolicyInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAssignmentPolicyById not implemented")
}
func (UnimplementedIntegrationServer) DeleteAssignmentPolicy(context.Context, *UUIDsReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAssignmentPolicy not implemented")
}
func (UnimplementedIntegrationServer) mustEmbedUnimplementedIntegrationServer() {}
func (UnimplementedIntegrationServer) testEmbeddedByValue()                     {}

// UnsafeIntegrationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to IntegrationServer will
// result in compilation errors.
type UnsafeIntegrationServer interface {
	mustEmbedUnimplementedIntegrationServer()
}

func RegisterIntegrationServer(s grpc.ServiceRegistrar, srv IntegrationServer) {
	// If the following call pancis, it indicates UnimplementedIntegrationServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Integration_ServiceDesc, srv)
}

func _Integration_CreateIntegration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIntegrationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).CreateIntegration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_CreateIntegration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).CreateIntegration(ctx, req.(*CreateIntegrationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_UpdateIntegration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIntegrationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).UpdateIntegration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_UpdateIntegration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).UpdateIntegration(ctx, req.(*UpdateIntegrationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_GetIntegrationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IntegrationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).GetIntegrationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_GetIntegrationList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).GetIntegrationList(ctx, req.(*IntegrationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_GetIntegrationById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UUIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).GetIntegrationById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_GetIntegrationById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).GetIntegrationById(ctx, req.(*UUIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_DeleteIntegration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UUIDsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).DeleteIntegration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_DeleteIntegration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).DeleteIntegration(ctx, req.(*UUIDsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_CreateAlertRoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAlertRouteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).CreateAlertRoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_CreateAlertRoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).CreateAlertRoute(ctx, req.(*CreateAlertRouteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_UpdateAlertRoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAlertRouteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).UpdateAlertRoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_UpdateAlertRoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).UpdateAlertRoute(ctx, req.(*UpdateAlertRouteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_GetAlertRouteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AlertRouteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).GetAlertRouteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_GetAlertRouteList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).GetAlertRouteList(ctx, req.(*AlertRouteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_GetAlertRouteById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).GetAlertRouteById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_GetAlertRouteById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).GetAlertRouteById(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_DeleteAlertRoute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).DeleteAlertRoute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_DeleteAlertRoute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).DeleteAlertRoute(ctx, req.(*IDsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_AssociateAlertWithSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssociateAlertWithSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).AssociateAlertWithSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_AssociateAlertWithSpace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).AssociateAlertWithSpace(ctx, req.(*AssociateAlertWithSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_AssociateIntegrationWithSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssociateIntegrationWithSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).AssociateIntegrationWithSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_AssociateIntegrationWithSpace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).AssociateIntegrationWithSpace(ctx, req.(*AssociateIntegrationWithSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_CreateAssignmentPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAssignmentPolicyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).CreateAssignmentPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_CreateAssignmentPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).CreateAssignmentPolicy(ctx, req.(*CreateAssignmentPolicyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_UpdateAssignmentPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAssignmentPolicyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).UpdateAssignmentPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_UpdateAssignmentPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).UpdateAssignmentPolicy(ctx, req.(*UpdateAssignmentPolicyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_GetAssignmentPolicyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentPolicyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).GetAssignmentPolicyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_GetAssignmentPolicyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).GetAssignmentPolicyList(ctx, req.(*AssignmentPolicyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_GetAssignmentPolicyById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UUIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).GetAssignmentPolicyById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_GetAssignmentPolicyById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).GetAssignmentPolicyById(ctx, req.(*UUIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Integration_DeleteAssignmentPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UUIDsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IntegrationServer).DeleteAssignmentPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Integration_DeleteAssignmentPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IntegrationServer).DeleteAssignmentPolicy(ctx, req.(*UUIDsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Integration_ServiceDesc is the grpc.ServiceDesc for Integration service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Integration_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "oncall.Integration",
	HandlerType: (*IntegrationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createIntegration",
			Handler:    _Integration_CreateIntegration_Handler,
		},
		{
			MethodName: "updateIntegration",
			Handler:    _Integration_UpdateIntegration_Handler,
		},
		{
			MethodName: "getIntegrationList",
			Handler:    _Integration_GetIntegrationList_Handler,
		},
		{
			MethodName: "getIntegrationById",
			Handler:    _Integration_GetIntegrationById_Handler,
		},
		{
			MethodName: "deleteIntegration",
			Handler:    _Integration_DeleteIntegration_Handler,
		},
		{
			MethodName: "createAlertRoute",
			Handler:    _Integration_CreateAlertRoute_Handler,
		},
		{
			MethodName: "updateAlertRoute",
			Handler:    _Integration_UpdateAlertRoute_Handler,
		},
		{
			MethodName: "getAlertRouteList",
			Handler:    _Integration_GetAlertRouteList_Handler,
		},
		{
			MethodName: "getAlertRouteById",
			Handler:    _Integration_GetAlertRouteById_Handler,
		},
		{
			MethodName: "deleteAlertRoute",
			Handler:    _Integration_DeleteAlertRoute_Handler,
		},
		{
			MethodName: "associateAlertWithSpace",
			Handler:    _Integration_AssociateAlertWithSpace_Handler,
		},
		{
			MethodName: "associateIntegrationWithSpace",
			Handler:    _Integration_AssociateIntegrationWithSpace_Handler,
		},
		{
			MethodName: "createAssignmentPolicy",
			Handler:    _Integration_CreateAssignmentPolicy_Handler,
		},
		{
			MethodName: "updateAssignmentPolicy",
			Handler:    _Integration_UpdateAssignmentPolicy_Handler,
		},
		{
			MethodName: "getAssignmentPolicyList",
			Handler:    _Integration_GetAssignmentPolicyList_Handler,
		},
		{
			MethodName: "getAssignmentPolicyById",
			Handler:    _Integration_GetAssignmentPolicyById_Handler,
		},
		{
			MethodName: "deleteAssignmentPolicy",
			Handler:    _Integration_DeleteAssignmentPolicy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "desc/oncall.proto",
}

const (
	Space_CreateSpace_FullMethodName  = "/oncall.Space/createSpace"
	Space_UpdateSpace_FullMethodName  = "/oncall.Space/updateSpace"
	Space_GetSpaceList_FullMethodName = "/oncall.Space/getSpaceList"
	Space_GetSpaceById_FullMethodName = "/oncall.Space/getSpaceById"
	Space_DeleteSpace_FullMethodName  = "/oncall.Space/deleteSpace"
)

// SpaceClient is the client API for Space service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Space management
type SpaceClient interface {
	CreateSpace(ctx context.Context, in *CreateSpaceReq, opts ...grpc.CallOption) (*BaseUUIDResp, error)
	UpdateSpace(ctx context.Context, in *UpdateSpaceReq, opts ...grpc.CallOption) (*BaseResp, error)
	GetSpaceList(ctx context.Context, in *SpaceListReq, opts ...grpc.CallOption) (*SpaceListResp, error)
	GetSpaceById(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*SpaceInfo, error)
	DeleteSpace(ctx context.Context, in *IDsReq, opts ...grpc.CallOption) (*BaseResp, error)
}

type spaceClient struct {
	cc grpc.ClientConnInterface
}

func NewSpaceClient(cc grpc.ClientConnInterface) SpaceClient {
	return &spaceClient{cc}
}

func (c *spaceClient) CreateSpace(ctx context.Context, in *CreateSpaceReq, opts ...grpc.CallOption) (*BaseUUIDResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseUUIDResp)
	err := c.cc.Invoke(ctx, Space_CreateSpace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *spaceClient) UpdateSpace(ctx context.Context, in *UpdateSpaceReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Space_UpdateSpace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *spaceClient) GetSpaceList(ctx context.Context, in *SpaceListReq, opts ...grpc.CallOption) (*SpaceListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SpaceListResp)
	err := c.cc.Invoke(ctx, Space_GetSpaceList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *spaceClient) GetSpaceById(ctx context.Context, in *IDReq, opts ...grpc.CallOption) (*SpaceInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SpaceInfo)
	err := c.cc.Invoke(ctx, Space_GetSpaceById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *spaceClient) DeleteSpace(ctx context.Context, in *IDsReq, opts ...grpc.CallOption) (*BaseResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BaseResp)
	err := c.cc.Invoke(ctx, Space_DeleteSpace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SpaceServer is the server API for Space service.
// All implementations must embed UnimplementedSpaceServer
// for forward compatibility.
//
// Space management
type SpaceServer interface {
	CreateSpace(context.Context, *CreateSpaceReq) (*BaseUUIDResp, error)
	UpdateSpace(context.Context, *UpdateSpaceReq) (*BaseResp, error)
	GetSpaceList(context.Context, *SpaceListReq) (*SpaceListResp, error)
	GetSpaceById(context.Context, *IDReq) (*SpaceInfo, error)
	DeleteSpace(context.Context, *IDsReq) (*BaseResp, error)
	mustEmbedUnimplementedSpaceServer()
}

// UnimplementedSpaceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSpaceServer struct{}

func (UnimplementedSpaceServer) CreateSpace(context.Context, *CreateSpaceReq) (*BaseUUIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSpace not implemented")
}
func (UnimplementedSpaceServer) UpdateSpace(context.Context, *UpdateSpaceReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSpace not implemented")
}
func (UnimplementedSpaceServer) GetSpaceList(context.Context, *SpaceListReq) (*SpaceListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSpaceList not implemented")
}
func (UnimplementedSpaceServer) GetSpaceById(context.Context, *IDReq) (*SpaceInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSpaceById not implemented")
}
func (UnimplementedSpaceServer) DeleteSpace(context.Context, *IDsReq) (*BaseResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSpace not implemented")
}
func (UnimplementedSpaceServer) mustEmbedUnimplementedSpaceServer() {}
func (UnimplementedSpaceServer) testEmbeddedByValue()               {}

// UnsafeSpaceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SpaceServer will
// result in compilation errors.
type UnsafeSpaceServer interface {
	mustEmbedUnimplementedSpaceServer()
}

func RegisterSpaceServer(s grpc.ServiceRegistrar, srv SpaceServer) {
	// If the following call pancis, it indicates UnimplementedSpaceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Space_ServiceDesc, srv)
}

func _Space_CreateSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpaceServer).CreateSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Space_CreateSpace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpaceServer).CreateSpace(ctx, req.(*CreateSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Space_UpdateSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSpaceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpaceServer).UpdateSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Space_UpdateSpace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpaceServer).UpdateSpace(ctx, req.(*UpdateSpaceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Space_GetSpaceList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SpaceListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpaceServer).GetSpaceList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Space_GetSpaceList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpaceServer).GetSpaceList(ctx, req.(*SpaceListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Space_GetSpaceById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpaceServer).GetSpaceById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Space_GetSpaceById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpaceServer).GetSpaceById(ctx, req.(*IDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Space_DeleteSpace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IDsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SpaceServer).DeleteSpace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Space_DeleteSpace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SpaceServer).DeleteSpace(ctx, req.(*IDsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Space_ServiceDesc is the grpc.ServiceDesc for Space service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Space_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "oncall.Space",
	HandlerType: (*SpaceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "createSpace",
			Handler:    _Space_CreateSpace_Handler,
		},
		{
			MethodName: "updateSpace",
			Handler:    _Space_UpdateSpace_Handler,
		},
		{
			MethodName: "getSpaceList",
			Handler:    _Space_GetSpaceList_Handler,
		},
		{
			MethodName: "getSpaceById",
			Handler:    _Space_GetSpaceById_Handler,
		},
		{
			MethodName: "deleteSpace",
			Handler:    _Space_DeleteSpace_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "desc/oncall.proto",
}
